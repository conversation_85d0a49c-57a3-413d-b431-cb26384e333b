const path = require('path');
const fs = require('fs');
const Suite = require('../models/suite');
const logger = require('../services/core/loggerService');

/**
 * File Controller - Secure File Access
 * All endpoints require authentication (JWT)
 * 
 * Pre-release: Only authentication (any logged-in user can access any file)
 * TODO: Enable authorization before production (see commented lines)
 */

/**
 * Stream image for preview
 * GET /files/images/:suiteId/:filename
 */
exports.streamImage = async (req, res) => {
  try {
    const { suiteId, filename } = req.params;
    const userId = req.user._id;

    const suite = await Suite.findById(suiteId);
    if (!suite) {
      return res.status(404).json({ success: false, message: 'Suite not found.' });
    }

    // TODO: Enable authorization before production
    // if (suite.user_id.toString() !== userId.toString() && 
    //     req.user.role !== 'admin' && req.user.role !== 'super_admin') {
    //   logger.warn(`Access denied: User ${userId} → Suite ${suiteId}`);
    //   return res.status(403).json({ success: false, message: 'Access denied.' });
    // }

    // Verify file exists in suite
    const fileExists = suite.images?.some(img => 
      img.file_name === filename || img.path?.includes(filename)
    );

    if (!fileExists) {
      logger.warn(`File not found: ${filename} in suite ${suiteId}`);
      return res.status(404).json({ success: false, message: 'Image not found.' });
    }

    // Get file path
    const image = suite.images.find(img => 
      img.file_name === filename || img.path?.includes(filename)
    );

    if (!image?.path) {
      return res.status(404).json({ success: false, message: 'Image path not found.' });
    }

    const absolutePath = path.join(__dirname, '..', image.path);

    if (!fs.existsSync(absolutePath)) {
      logger.error(`File missing: ${absolutePath}`);
      return res.status(404).json({ success: false, message: 'File not found on server.' });
    }

    // Audit log
    logger.info(`Image access: User ${userId} → Suite ${suiteId} → ${filename}`);

    // Set headers
    const ext = path.extname(filename).toLowerCase();
    const contentType = {
      '.png': 'image/png',
      '.jpg': 'image/jpeg',
      '.jpeg': 'image/jpeg',
      '.gif': 'image/gif',
      '.webp': 'image/webp',
      '.svg': 'image/svg+xml'
    }[ext] || 'application/octet-stream';

    res.setHeader('Content-Type', contentType);
    res.setHeader('Cache-Control', 'private, max-age=3600');
    res.setHeader('X-Content-Type-Options', 'nosniff');

    // Stream file
    const fileStream = fs.createReadStream(absolutePath);
    fileStream.on('error', (err) => {
      logger.error(`Stream error: ${err.message}`);
      if (!res.headersSent) {
        res.status(500).json({ success: false, message: 'Error streaming image.' });
      }
    });
    fileStream.pipe(res);

  } catch (error) {
    logger.error(`streamImage error: ${error.message}`);
    if (!res.headersSent) {
      res.status(500).json({ success: false, message: 'Internal server error.' });
    }
  }
};

/**
 * Get file metadata
 * GET /files/info/:suiteId/:filename
 */
exports.getFileInfo = async (req, res) => {
  try {
    const { suiteId, filename } = req.params;
    const userId = req.user._id;

    const suite = await Suite.findById(suiteId);
    if (!suite) {
      return res.status(404).json({ success: false, message: 'Suite not found.' });
    }

    // TODO: Enable authorization before production
    // if (suite.user_id.toString() !== userId.toString() && 
    //     req.user.role !== 'admin' && req.user.role !== 'super_admin') {
    //   return res.status(403).json({ success: false, message: 'Access denied.' });
    // }

    const image = suite.images?.find(img => 
      img.file_name === filename || img.path?.includes(filename)
    );

    if (!image) {
      return res.status(404).json({ success: false, message: 'File not found.' });
    }

    logger.info(`File info: User ${userId} → Suite ${suiteId} → ${filename}`);

    res.json({
      success: true,
      data: {
        original_name: image.original_name,
        file_name: image.file_name,
        size: image.size,
        mime_type: image.mime_type,
        uploaded_at: suite.createdAt
      }
    });

  } catch (error) {
    logger.error(`getFileInfo error: ${error.message}`);
    res.status(500).json({ success: false, message: 'Internal server error.' });
  }
};

