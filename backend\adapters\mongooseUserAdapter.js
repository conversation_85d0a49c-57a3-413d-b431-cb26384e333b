const UserService = require('../services/core/userService');
const User = require('../models/user');

class MongooseUserAdapter extends UserService {
  async createUser(data) {
    return User.create(data);
  }
  
  async getUserByEmail(email) {
    return User.findOne({ email });
  }
  
  async getUserById(id) {
    return User.findById(id);
  }
  
  async listUsers() {
    return User.find();
  }
  
  async getUsersPaginated(filters, sort, skip, limit) {
    return User.find(filters)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');
  }
  
  async countUsers(filters) {
    return User.countDocuments(filters);
  }
  
  async updateUser(id, data) {
    return User.findByIdAndUpdate(id, data, { new: true });
  }
  
  async deleteUser(id) {
    return User.findByIdAndDelete(id);
  }
  
  async bulkUpdateUsers(userIds, updates) {
    return User.updateMany(
      { _id: { $in: userIds } },
      { $set: updates }
    );
  }
  
  async getUserStats() {
    const totalUsers = await User.countDocuments();
    const activeUsers = await User.countDocuments({ status: 'active' });
    const inactiveUsers = await User.countDocuments({ status: 'inactive' });
    const suspendedUsers = await User.countDocuments({ status: 'suspended' });
    const pendingUsers = await User.countDocuments({ status: 'pending' });
    
    const adminUsers = await User.countDocuments({ role: 'admin' });
    const moderatorUsers = await User.countDocuments({ role: 'moderator' });
    const regularUsers = await User.countDocuments({ role: 'user' });
    
    // Get users created in the last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    const newUsers = await User.countDocuments({ 
      createdAt: { $gte: thirtyDaysAgo } 
    });
    
    // Get users who logged in today
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    const todayUsers = await User.countDocuments({ 
      lastLogin: { $gte: today } 
    });
    
    return {
      total: totalUsers,
      byStatus: {
        active: activeUsers,
        inactive: inactiveUsers,
        suspended: suspendedUsers,
        pending: pendingUsers
      },
      byRole: {
        admin: adminUsers,
        moderator: moderatorUsers,
        user: regularUsers
      },
      recent: {
        newUsers,
        todayUsers
      }
    };
  }
  
  async searchUsers(searchTerm, filters = {}) {
    const searchFilters = {
      ...filters,
      $or: [
        { name: { $regex: searchTerm, $options: 'i' } },
        { email: { $regex: searchTerm, $options: 'i' } }
      ]
    };
    
    return User.find(searchFilters)
      .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');
  }
  
  async getUsersByRole(role) {
    return User.find({ role })
      .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');
  }
  
  async getUsersByStatus(status) {
    return User.find({ status })
      .select('-password -emailVerificationToken -passwordResetToken -passwordResetExpires');
  }
}
module.exports = new MongooseUserAdapter();
