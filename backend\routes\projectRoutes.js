const express = require('express');
const router = express.Router();
const projectController = require('../controllers/projectController');
const validate = require('../middleware/validate');
const authMiddleware = require('../middleware/auth');
const { createProjectSchema, updateProjectSchema } = require('../validation/projectSchemas');

router.get('/', authMiddleware, projectController.getProjects);
router.get('/filter', authMiddleware, projectController.filterProject);
router.get('/get-project-join-requests', authMiddleware, projectController.getProjectJoinRequests);
router.post('/approve-project-join-request', authMiddleware, projectController.approveProjectJoinRequest);
router.post('/', authMiddleware, validate(createProjectSchema), projectController.createProject);
router.put('/:id', authMiddleware, validate(updateProjectSchema), projectController.updateProject);
router.delete('/:id', authMiddleware, projectController.deleteProject);
router.get('/:id', authMiddleware, projectController.getProjectDetail);
router.post('/add-user-to-project', authMiddleware, projectController.addUserToProject);
router.post('/remove-user-project', authMiddleware, projectController.removeUserProject);
router.post('/change-user-project-role', authMiddleware, projectController.changeUserProjectRole);
router.post('/request-join-project', authMiddleware, projectController.requestJoinProject);

module.exports = router; 