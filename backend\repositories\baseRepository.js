const mongoose = require('mongoose');

class BaseRepository {
  constructor(model) {
    this.model = model;
  }

  async findById(id) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return null;
    }
    return await this.model
      .findOne({ _id: id })
      .lean();
  }

  async findAll(filter = {}, options = {}) {
    const sort = options.sort || { _id: 1 };

    return await this.model
        .find({ ...filter }, null, options)
        .sort(sort)
        .lean();
  }


  async findOne(filter = {}, options = {}) {
    return await this.model
      .findOne({ ...filter }, null, options)
      .lean();
  }


  async create(data) {
    return await this.model.create(data);
  }

  async update(id, data, conditions = {}) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return null;
    }

    return await this.model.findOneAndUpdate(
      { _id: id, ...conditions },
      { $set: data },
      { new: true }
    ).lean();
  }

  async delete(id, conditions = {}) {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return null;
    }

    return await this.model.findOneAndUpdate(
      { _id: id, ...conditions },
      {
        $set: {
          del_flag: 1
        }
      },
      { new: true }
    ).lean();
  }

  async count(filter = {}) {
    return await this.model.countDocuments({ ...filter });
  }

  async exists(filter = {}) {
    return await this.model.exists({ ...filter });
  }

  async aggregate(pipeline = []) {
    return await this.model.aggregate(pipeline);
  }

  async updateMany(query, data) {
    return this.model.updateMany(query, data);
  }

  async deleteMany(query) {
    return this.model.updateMany(
      { ...query },
      {
        $set: {
          del_flag: 1
        }
      }
    );
  }
}

module.exports = BaseRepository;
