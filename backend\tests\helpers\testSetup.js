/**
 * Test Setup và Configuration cho Auth Module
 * <PERSON> ch<PERSON> do<PERSON>h nghiệp với comprehensive mocking và utilities
 */

const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');

let mongoServer;

/**
 * Setup MongoDB Memory Server cho testing
 */
const setupDatabase = async () => {
  try {
    mongoServer = await MongoMemoryServer.create();
    const mongoUri = mongoServer.getUri();
    
    await mongoose.connect(mongoUri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
      dbName: 'auth_test_db'
    });
    
    console.log('✅ Test database connected successfully');
  } catch (error) {
    console.error('❌ Test database connection failed:', error);
    throw error;
  }
};

/**
 * Cleanup database sau khi test
 */
const teardownDatabase = async () => {
  try {
    if (mongoose.connection.readyState !== 0) {
      await mongoose.connection.dropDatabase();
      await mongoose.disconnect();
    }
    
    if (mongoServer) {
      await mongoServer.stop();
    }
    
    console.log('✅ Test database cleaned up successfully');
  } catch (error) {
    console.error('❌ Test database cleanup failed:', error);
    throw error;
  }
};

/**
 * Clear tất cả collections trong database
 */
const clearDatabase = async () => {
  try {
    const collections = mongoose.connection.collections;
    
    for (const key in collections) {
      const collection = collections[key];
      await collection.deleteMany({});
    }
    
    console.log('✅ Database collections cleared');
  } catch (error) {
    console.error('❌ Database clear failed:', error);
    throw error;
  }
};

/**
 * Mock JWT functions cho testing
 */
const mockJWT = {
  sign: jest.fn(),
  verify: jest.fn(),
  decode: jest.fn()
};

/**
 * Mock bcrypt functions cho testing
 */
const mockBcrypt = {
  hash: jest.fn(),
  compare: jest.fn()
};

/**
 * Reset tất cả mocks
 */
const resetMocks = () => {
  jest.clearAllMocks();
  mockJWT.sign.mockReset();
  mockJWT.verify.mockReset();
  mockJWT.decode.mockReset();
  mockBcrypt.hash.mockReset();
  mockBcrypt.compare.mockReset();
};

/**
 * Setup default mock behaviors
 */
const setupDefaultMocks = () => {
  // JWT mocks
  mockJWT.sign.mockImplementation((payload, secret, options) => {
    return `mock.jwt.token.${Date.now()}`;
  });
  
  mockJWT.verify.mockImplementation((token, secret) => {
    if (token.includes('expired')) {
      const error = new Error('Token expired');
      error.name = 'TokenExpiredError';
      throw error;
    }
    if (token.includes('invalid')) {
      const error = new Error('Invalid token');
      error.name = 'JsonWebTokenError';
      throw error;
    }
    return {
      _id: 'mock_user_id',
      email: '<EMAIL>',
      role: 'user',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    };
  });
  
  mockJWT.decode.mockImplementation((token) => {
    if (token.includes('malformed')) return null;
    return {
      _id: 'mock_user_id',
      email: '<EMAIL>',
      role: 'user',
      iat: Math.floor(Date.now() / 1000),
      exp: Math.floor(Date.now() / 1000) + 3600
    };
  });
  
  // Bcrypt mocks
  mockBcrypt.hash.mockImplementation(async (password, saltRounds) => {
    return `hashed_${password}_${saltRounds}`;
  });
  
  mockBcrypt.compare.mockImplementation(async (password, hash) => {
    return hash === `hashed_${password}_10`;
  });
};

/**
 * Test environment configuration
 */
const testConfig = {
  jwtSecret: 'test_jwt_secret_key_for_testing',
  jwtExpiresIn: '1h',
  jwtRefreshExpiresIn: '7d',
  bcryptSaltRounds: 10,
  maxLoginAttempts: 5,
  lockoutTime: 15 * 60 * 1000 // 15 minutes
};

module.exports = {
  setupDatabase,
  teardownDatabase,
  clearDatabase,
  mockJWT,
  mockBcrypt,
  resetMocks,
  setupDefaultMocks,
  testConfig
};
