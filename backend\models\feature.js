const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');

const { Schema } = mongoose;

const FeatureSchema = new Schema({
  suite_id: { type: Schema.Types.ObjectId, ref: 'suite', required: true },
  name: { type: String, required: true, trim: true },
  description: { type: String, default: '' },
  source_files: { type: [String], default: [] },
  merged_from: { type: [String], default: [] },
  permissions: {
    roles_allowed: { type: [String], default: [] },
    permission_description: { type: String, default: '' }
  },
  ui_components: {
    screens: { type: [String], default: [] },
    elements: [{
      element_name: { type: String },
      element_type: { type: String },
      element_description: { type: String }
    }]
  },
  order: { type: Number, default: 0 },
  previous_response_id: { type: String, default: null },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

FeatureSchema.plugin(softDeletePlugin);

FeatureSchema.index({ suite_id: 1, order: 1 });
FeatureSchema.index({ suite_id: 1, name: 1 });

module.exports = mongoose.model('feature', FeatureSchema);


