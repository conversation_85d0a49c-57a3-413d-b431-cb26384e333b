const express = require('express');
const router = express.Router();
const testScriptController = require('../controllers/testScriptController');
const fileService = require('../services/core/fileService');
const validate = require('../middleware/validate');
const authMiddleware = require('../middleware/auth');
// const { createTestScriptSchema } = require('../validation/testScriptSchemas');

// Create test script
// router.post('/', authMiddleware, validate(createTestScriptSchema), testScriptController.create);
router.post('/gen-prompt', authMiddleware, testScriptController.genPrompt);
router.post('/call-openai', authMiddleware, testScriptController.callOpenAi);

// Extract headings from multiple DOCX files
router.post('/extract-headings', 
  authMiddleware, 
  ...fileService.uploadFilesForExtractHeadings,
  testScriptController.extractHeadings
);

module.exports = router; 