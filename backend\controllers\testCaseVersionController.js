const Suite = require('../models/suite');
const TestCaseVersionRepository = require('../repositories/testCaseVersionRepository');
const TestCaseHistoryRepository = require('../repositories/testCaseHistoryRepository');
const mongoose = require('mongoose');
const logger = require('../services/core/loggerService');

const testCaseVersionController = {
    async getListVersions(req, res) {
        try {
            const { suite_id } = req.query;
            
            let filter = {};
            if (suite_id) {
                if (!mongoose.Types.ObjectId.isValid(suite_id)) {
                    return res.json([]);
                }
                filter.suite_id = suite_id;
            }
            
            const TestCaseVersion = require('../models/testCaseVersion');
            const versions = await TestCaseVersion
                .find(filter)
                .populate({
                    path: 'created_by',
                    select: 'name'
                })
                .select('id version_name version_number modified_at created_by createdAt updatedAt')
                .sort({ version_number: -1 })
                .lean();
            res.json(versions);
        } catch (error) {
            logger.error('Lỗi khi lấy danh sách versions', error, req.originalUrl);
            res.status(500).json({ error: error.message });
        }
    },

    async getVersionDetail(req, res) {
        try {
            const { id } = req.params;

            // Validate ID format
            if (!mongoose.Types.ObjectId.isValid(id)) {
                return res.status(400).json({ error: 'ID không hợp lệ.' });
            }

            // Get version details with populated created_by
            const TestCaseVersion = require('../models/testCaseVersion');
            const version = await TestCaseVersion
                .findById(id)
                .populate({
                    path: 'created_by',
                    select: 'name'
                })
                .lean();
            
            if (!version) {
                return res.status(404).json({ error: 'Version không tồn tại.' });
            }

            // Get related suite information
            const suite = await Suite.findById(version.suite_id)
                .select('feature_name field_type objective approval_status')
                .lean();

            const TestCaseHistory = require('../models/testCaseHistory');
            const histories = await TestCaseHistory
                .find({ test_case_version_id: id })
                .populate({
                    path: 'test_case_id',
                    select: 'detail',
                    options: { includeDeleted: true }
                })
                .sort({ modified_at: -1 })
                .lean();

            // Get test_case_regenerates by suite_id
            const TestCaseRegenerate = require('../models/testCaseRegenerate');
            const regenerates = await TestCaseRegenerate
                .find({ test_case_version_id: id })
                .populate({
                    path: 'test_case_id',
                    select: 'detail',
                    options: { includeDeleted: true }
                })
                .sort({ regenerated_at: -1 })
                .lean();

            const response = {
                version: {
                    ...version,
                    suite: suite || null,
                    test_case_histories: histories || [],
                    test_case_regenerates: regenerates || []
                }
            };

            res.json(response);
        } catch (error) {
            logger.error('Lỗi khi lấy chi tiết version', error, req.originalUrl);
            res.status(500).json({ error: error.message });
        }
    }
};

module.exports = testCaseVersionController; 