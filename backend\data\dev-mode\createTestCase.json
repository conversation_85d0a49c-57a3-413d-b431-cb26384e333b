{"features": [{"_id": "68d25cb738ec9064b3b87b3a", "name": "<PERSON><PERSON><PERSON> phê du<PERSON>t cuộc tranh chấp", "description": "<PERSON> phép người dùng gửi cuộc tranh chấp lên người có thẩm quyền phê duyệt, chọn phòng ban và người duyệt phù hợp theo quy định tổ chức.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb149", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb14b", "name": "Chỉ người có quyền mới gửi phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người dùng có quyền mới có thể gửi phê duyệt tranh chấp.", "test_cases": [{"_id": "68d25e15841e3bd82603a9cc", "test_case_id": "Permission_001", "test_case_name": "<PERSON>ư<PERSON><PERSON> dùng có quyền gửi phê duyệt thành công", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền gửi phê duyệt\nCó tranh chấp hợp lệ cần gửi phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp\nBước 2: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON><PERSON> phê duyệt'\nBước 3: <PERSON><PERSON><PERSON> phòng ban và người duyệt hợp lệ\nBước 4: <PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> gửi", "test_data": "<PERSON><PERSON><PERSON>: user_approve\nT<PERSON><PERSON> chấp: #123", "expected_result": "Bước 1: <PERSON><PERSON> chi tiết tranh chấp hiển thị đầy đủ\nBước 2: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê duyệt' hiể<PERSON> thị\nBướ<PERSON> 3: <PERSON><PERSON> thể chọn phòng ban/người duyệt\nBước 4: <PERSON><PERSON><PERSON> thành công, tr<PERSON><PERSON> thá<PERSON> cậ<PERSON> nh<PERSON>t", "test_case_type": "Positive"}, {"_id": "68d25e15841e3bd82603a9cf", "test_case_id": "Permission_002", "test_case_name": "Người dùng không có quyền không gửi đượ<PERSON> phê duyệt", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản không có quyền gửi phê duyệt\nC<PERSON> tranh chấp hợp lệ", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp\nBước 2: <PERSON><PERSON><PERSON> tra sự xuất hiện của nút '<PERSON><PERSON><PERSON> phê duyệt'", "test_data": "<PERSON><PERSON><PERSON>: user_no_approve\nT<PERSON><PERSON> chấp: #123", "expected_result": "Bước 1: <PERSON><PERSON> chi tiết tranh chấp hiển thị\nBước 2: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê duyệt' không hiển thị", "test_case_type": "Negative"}, {"_id": "68d25e15841e3bd82603a9d2", "test_case_id": "Permission_003", "test_case_name": "<PERSON><PERSON><PERSON> tra phân quyền khi đổi vai trò người dùng", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nCó thể chuyển đổi vai trò người dùng", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang tranh chấp\nBước 2: <PERSON><PERSON><PERSON> vai trò sang không có quyền\nBước 3: <PERSON><PERSON><PERSON> mới trang", "test_data": "<PERSON><PERSON><PERSON>: user_switch_role", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê duyệt' hiể<PERSON> thị\nBước 2: <PERSON><PERSON> khi đổi vai trò, nút biến mất\nBước 3: <PERSON><PERSON> cập nhật đúng quyền hạn", "test_case_type": "Edge"}, {"_id": "68d25e15841e3bd82603a9d5", "test_case_id": "Permission_004", "test_case_name": "<PERSON><PERSON><PERSON> tra truy cập API gửi phê duyệt bằng tài khoản không đủ quyền", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản không có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> request API gửi phê duyệt bằng Postman hoặc công cụ tương tự", "test_data": "API: /api/dispute/approve\n<PERSON><PERSON><PERSON>: user_no_approve", "expected_result": "Bước 1: API trả về lỗi 403 Forbidden hoặc thông báo không đủ quyền", "test_case_type": "Negative"}, {"_id": "68d25e15841e3bd82603a9d8", "test_case_id": "Permission_005", "test_case_name": "<PERSON><PERSON><PERSON> tra UI ẩn nút gửi phê duyệt trên các trình duyệt khác nhau với người không đủ quyền", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản không có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> cập trang tranh chấp trên <PERSON>rome\nBước 2: <PERSON><PERSON><PERSON> cập trên Firefox\nBước 3: <PERSON><PERSON><PERSON> cậ<PERSON> trên <PERSON>", "test_data": "<PERSON><PERSON><PERSON>: user_no_approve", "expected_result": "Bước 1-3: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê duyệ<PERSON>' khô<PERSON> hiển thị trên tất cả trình duyệt", "test_case_type": "Compatibility"}]}, {"_id": "68d25d74b5e186666c9cb14d", "name": "Ẩn nút gửi với người không đủ quyền", "rationale": "<PERSON><PERSON><PERSON> tra giao diện không hiển thị chức năng gửi phê duyệt cho người không đủ quyền.", "test_cases": [{"_id": "68d25e15841e3bd82603a9dc", "test_case_id": "HideButton_001", "test_case_name": "<PERSON><PERSON><PERSON> gửi phê duyệt không hiển thị với người không đủ quyền", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản không đủ quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp", "test_data": "<PERSON><PERSON><PERSON>: user_no_approve", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê duyệt' không hiển thị trên giao di<PERSON>n", "test_case_type": "UI"}, {"_id": "68d25e15841e3bd82603a9df", "test_case_id": "HideButton_002", "test_case_name": "<PERSON><PERSON><PERSON> tra tab ẩn nút gửi phê duyệt khi resize màn hình nhỏ", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản không đủ quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang tranh chấp\n<PERSON>ước 2: <PERSON><PERSON> nhỏ cửa sổ trình du<PERSON>t", "test_data": "<PERSON><PERSON><PERSON>: user_no_approve", "expected_result": "Bước 1-2: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê duyệt' khô<PERSON> xuất hiện ở mọi độ phân giải", "test_case_type": "UI"}, {"_id": "68d25e15841e3bd82603a9e2", "test_case_id": "HideButton_003", "test_case_name": "<PERSON><PERSON><PERSON> tra <PERSON>n nút gửi phê duyệt khi load lại trang", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản không đủ quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang tranh chấp\n<PERSON> 2: <PERSON><PERSON><PERSON><PERSON> F5 để reload trang", "test_data": "<PERSON><PERSON><PERSON>: user_no_approve", "expected_result": "Bước 1-2: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê duyệ<PERSON>' kh<PERSON><PERSON> xu<PERSON>t hiện sau khi reload", "test_case_type": "Edge"}]}]}, {"_id": "68d25d74b5e186666c9cb14f", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb151", "name": "<PERSON><PERSON> thông tin tranh chấp tr<PERSON><PERSON><PERSON> khi gửi", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> các trườ<PERSON> bắ<PERSON> buộc của tranh chấp đã được điền đầy đủ trước khi gửi phê duyệt.", "test_cases": [{"_id": "68d25e15841e3bd82603a9e7", "test_case_id": "Precondition_001", "test_case_name": "<PERSON><PERSON><PERSON> phê duyệt khi đã điền đủ thông tin bắt buộc", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nTranh chấp đã điền đủ các trường bắt buộc", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp\nBước 2: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> phê duyệt'", "test_data": "Tranh chấp: <PERSON><PERSON> điền đủ các trường", "expected_result": "Bước 1: <PERSON><PERSON> hiển thị đầy đủ thông tin\nBước 2: <PERSON><PERSON><PERSON> phê duyệt thành công", "test_case_type": "Positive"}, {"_id": "68d25e15841e3bd82603a9ea", "test_case_id": "Precondition_002", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> cho phép gửi khi thiếu trư<PERSON><PERSON> b<PERSON><PERSON> buộc", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nTranh chấp thiếu trường bắt buộc (ví dụ: mô tả)", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp\nBước 2: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> phê duyệt'", "test_data": "T<PERSON>h chấp: <PERSON><PERSON><PERSON><PERSON> trường mô tả", "expected_result": "Bước 1: <PERSON><PERSON> hiển thị cảnh báo thiếu thông tin\nBước 2: <PERSON><PERSON><PERSON><PERSON> gử<PERSON> đượ<PERSON> phê du<PERSON>, hiển thị thông báo lỗi", "test_case_type": "Negative"}, {"_id": "68d25e15841e3bd82603a9ed", "test_case_id": "Precondition_003", "test_case_name": "<PERSON><PERSON><PERSON> tra trư<PERSON><PERSON> hợp nhập dữ liệu tối đa cho các trư<PERSON><PERSON> b<PERSON><PERSON> buộc", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> dữ liệu tối đa cho các trư<PERSON><PERSON> bắt buộc\nBước 2: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> phê duyệt'", "test_data": "Mô tả: 1000 ký tự, Tiêu đề: 255 ký tự", "expected_result": "Bước 1: <PERSON><PERSON> liệ<PERSON> đ<PERSON><PERSON><PERSON> chấp nhận\nBước 2: <PERSON><PERSON><PERSON> <PERSON>hê duyệt thành công", "test_case_type": "Boundary"}, {"_id": "68d25e15841e3bd82603a9f0", "test_case_id": "Precondition_004", "test_case_name": "<PERSON><PERSON><PERSON> tra nhập ký tự đặc biệt vào trường bắt buộc", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> ký tự đặc biệt vào trường mô tả\nBước 2: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> phê duyệt'", "test_data": "Mô tả: @#$%^&*()", "expected_result": "Bước 1: <PERSON><PERSON> liệu được chấp nhận hoặc cảnh báo nếu không hợp lệ\nBước 2: <PERSON><PERSON><PERSON> phê duyệt thành công hoặc hiển thị lỗi hợp lệ", "test_case_type": "EP"}]}, {"_id": "68d25d74b5e186666c9cb153", "name": "<PERSON><PERSON>n phòng ban và người duyệt hợp lệ", "rationale": "<PERSON><PERSON><PERSON> tra người dùng phải chọn đúng phòng ban và người duyệt theo quy định trước khi gửi.", "test_cases": [{"_id": "68d25e15841e3bd82603a9f4", "test_case_id": "Department_001", "test_case_name": "<PERSON><PERSON>n phòng ban và người duyệt hợp lệ", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> <PERSON>ậ<PERSON> trang gửi phê duyệt\nBước 2: <PERSON><PERSON><PERSON> phòng ban hợp lệ\nBước 3: <PERSON><PERSON><PERSON> người duyệt hợp lệ\nBước 4: <PERSON><PERSON><PERSON><PERSON>ử<PERSON>", "test_data": "Phòng ban: <PERSON><PERSON> <PERSON><PERSON> du<PERSON>t: <PERSON><PERSON><PERSON><PERSON>", "expected_result": "Bước 1-3: <PERSON><PERSON>h<PERSON>ng ban/ngư<PERSON><PERSON> duyệt hiển thị đúng\nBước 4: <PERSON><PERSON><PERSON> thành công", "test_case_type": "Positive"}, {"_id": "68d25e15841e3bd82603a9f7", "test_case_id": "Department_002", "test_case_name": "<PERSON>h<PERSON>ng chọn phòng ban hoặc người duyệt", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang gửi phê duyệt\nBước 2: <PERSON><PERSON><PERSON><PERSON> chọn phòng ban hoặc người duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> gửi", "test_data": "Phòng ban: (bỏ trống)\nNgười duyệt: (bỏ trống)", "expected_result": "Bước 1-3: <PERSON><PERSON> thống cảnh báo b<PERSON><PERSON> buộc chọn phòng ban/ngư<PERSON><PERSON> du<PERSON>, kh<PERSON><PERSON> gử<PERSON> đ<PERSON>", "test_case_type": "Negative"}, {"_id": "68d25e15841e3bd82603a9fa", "test_case_id": "Department_003", "test_case_name": "<PERSON>ọn phòng ban không có người du<PERSON>t", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> phòng ban không có người duyệt\nBước 2: <PERSON><PERSON><PERSON><PERSON> gửi", "test_data": "Phòng ban: IT\nNgười duyệt: (không có)", "expected_result": "Bước 1: <PERSON><PERSON>ch người duyệt trống\nBước 2: <PERSON><PERSON> thống cảnh báo không có người duyệt", "test_case_type": "Edge"}]}]}, {"_id": "68d25d74b5e186666c9cb155", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb157", "name": "<PERSON><PERSON><PERSON> thị đúng danh sách phòng ban/ng<PERSON><PERSON><PERSON> du<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o danh sách phòng ban và người duyệt hiển thị đúng theo dữ liệu hệ thống.", "test_cases": [{"_id": "68d25e15841e3bd82603a9ff", "test_case_id": "Display_001", "test_case_name": "<PERSON><PERSON><PERSON> thị đầy đủ danh sách phòng ban/ngư<PERSON><PERSON> du<PERSON>t", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang gửi phê duyệt\nBước 2: <PERSON><PERSON><PERSON> tra danh sách phòng ban và người duyệt", "test_data": "<PERSON><PERSON> liệu hệ thống: 3 phòng ban, mỗi phòng ban 2 người duyệt", "expected_result": "Bước 1-2: <PERSON><PERSON> hiể<PERSON> thị đ<PERSON>, đ<PERSON><PERSON> đ<PERSON> theo dữ liệu hệ thống", "test_case_type": "UI"}, {"_id": "68d25e15841e3bd82603aa02", "test_case_id": "Display_002", "test_case_name": "<PERSON><PERSON><PERSON> tra danh sách phòng ban/ngư<PERSON>i duyệt khi dữ liệu lớn", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang gửi phê duyệt\nBước 2: <PERSON><PERSON><PERSON> tra hiển thị danh sách với 100 phòng ban, 1000 người duyệt", "test_data": "<PERSON><PERSON> liệu hệ thống: 100 phòng ban, 1000 người duyệt", "expected_result": "Bước 1-2: <PERSON><PERSON>ch hiển thị đầy đủ, kh<PERSON><PERSON> bị vỡ layout, có phân trang/tìm kiếm nếu cần", "test_case_type": "Edge"}]}, {"_id": "68d25d74b5e186666c9cb159", "name": "Thông báo gửi phê duyệt thành công/thất bại", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống hiển thị thông báo phù hợp sau khi gửi phê duyệt.", "test_cases": [{"_id": "68d25e15841e3bd82603aa06", "test_case_id": "Notification_001", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> báo gửi phê duyệt thành công", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nĐiền đủ thông tin", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> phê du<PERSON>'", "test_data": "<PERSON><PERSON><PERSON> ch<PERSON>p h<PERSON>p l<PERSON>", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> thị thông báo thành công rõ ràng, <PERSON><PERSON> hiểu", "test_case_type": "Positive"}, {"_id": "68d25e15841e3bd82603aa09", "test_case_id": "Notification_002", "test_case_name": "<PERSON>h<PERSON><PERSON> báo lỗi khi gửi phê duyệt thất bại", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nThiếu thông tin bắt buộc", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> phê du<PERSON>'", "test_data": "<PERSON><PERSON><PERSON> chấp thi<PERSON>u thông tin", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> thị thông báo lỗi rõ ràng, chỉ ra nguyên nhân", "test_case_type": "Negative"}]}]}, {"_id": "68d25d74b5e186666c9cb15b", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb15d", "name": "<PERSON><PERSON><PERSON> gửi phê duyệt thành công", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> khi gửi phê duyệt hợp lệ, hệ thống chuyển trạng thái tranh chấp đúng.", "test_cases": [{"_id": "68d25e15841e3bd82603aa0e", "test_case_id": "Flow_001", "test_case_name": "<PERSON><PERSON><PERSON> phê duyệt thành công chuyển trạng thái tranh chấp", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nTranh chấp ở trạng thái 'Chờ gửi phê duyệt'", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> phê du<PERSON>'", "test_data": "T<PERSON><PERSON> chấp: #123", "expected_result": "Bước 1: <PERSON><PERSON><PERSON><PERSON> thá<PERSON> tranh chấ<PERSON> sang '<PERSON><PERSON> chờ phê duyệt'", "test_case_type": "Positive"}]}, {"_id": "68d25d74b5e186666c9cb15f", "name": "<PERSON>ồng gửi phê duyệt bị từ chối do thiếu thông tin", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống xử lý đúng khi gửi phê duyệt với thông tin không hợp lệ.", "test_cases": [{"_id": "68d25e15841e3bd82603aa12", "test_case_id": "Flow_002", "test_case_name": "<PERSON><PERSON><PERSON> phê duyệt bị từ chối do thiếu thông tin", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nTranh chấp thiếu thông tin", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> phê du<PERSON>'", "test_data": "<PERSON><PERSON><PERSON> chấp thiếu trường mô tả", "expected_result": "Bước 1: <PERSON><PERSON> thống không chuyển trạng thái, hiển thị thông báo lỗi", "test_case_type": "Negative"}]}]}, {"_id": "68d25d74b5e186666c9cb161", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb163", "name": "<PERSON>ử lý lỗi kết nối khi gửi phê duyệt", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống thông báo lỗi khi gặp sự cố kết nối trong quá trình gửi.", "test_cases": [{"_id": "68d25e15841e3bd82603aa17", "test_case_id": "Exception_001", "test_case_name": "<PERSON><PERSON> phỏng mất kết nối khi gửi phê duyệt", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> kế<PERSON> n<PERSON>i mạng\nBước 2: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON><PERSON> phê duyệt'", "test_data": "<PERSON><PERSON><PERSON> ch<PERSON>p h<PERSON>p l<PERSON>", "expected_result": "Bước 1: <PERSON><PERSON> thống hiển thị thông báo lỗi kết nối, không gửi được", "test_case_type": "Edge"}]}, {"_id": "68d25d74b5e186666c9cb165", "name": "<PERSON><PERSON> lý gửi phê duyệt trùng lặp", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không cho phép gửi phê duyệt nhiều lần cho cùng một tranh chấp.", "test_cases": [{"_id": "68d25e15841e3bd82603aa1b", "test_case_id": "Exception_002", "test_case_name": "<PERSON><PERSON><PERSON> phê duyệt trùng lặp cho cùng một tranh chấp", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nTranh chấp đã được gửi phê duyệt trước đó", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê du<PERSON> <PERSON><PERSON><PERSON> 2 cho cùng một tranh chấp", "test_data": "T<PERSON>h chấp: #123 (đ<PERSON> g<PERSON><PERSON>)", "expected_result": "Bước 1: <PERSON><PERSON> thống không cho phép g<PERSON><PERSON> lạ<PERSON>, hiển thị thông báo lỗi", "test_case_type": "Edge"}]}]}, {"_id": "68d25d74b5e186666c9cb167", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb169", "name": "<PERSON><PERSON><PERSON> nhật trạng thái tranh chấp sau g<PERSON>i", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp đ<PERSON><PERSON><PERSON> cập nhật chính xác sau khi gửi phê duyệt.", "test_cases": [{"_id": "68d25e15841e3bd82603aa20", "test_case_id": "Impact_001", "test_case_name": "<PERSON><PERSON><PERSON> tra trạng thái tranh chấp sau khi gửi phê duy<PERSON>t", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nTranh chấp ở trạng thái 'Chờ gửi phê duyệt'", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê du<PERSON>'\nBước 2: <PERSON><PERSON><PERSON> mới trang", "test_data": "T<PERSON><PERSON> chấp: #123", "expected_result": "Bước 1: <PERSON><PERSON><PERSON><PERSON> thá<PERSON> chuy<PERSON> sang '<PERSON>ang chờ phê duyệt'\nBước 2: <PERSON>r<PERSON><PERSON> thái vẫn giữ nguyên sau khi reload", "test_case_type": "Positive"}]}, {"_id": "68d25d74b5e186666c9cb16b", "name": "<PERSON><PERSON> <PERSON>h<PERSON>n lịch sử gửi phê du<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống lưu lại lịch sử gửi phê duyệt cho tranh chấp.", "test_cases": [{"_id": "68d25e15841e3bd82603aa24", "test_case_id": "Impact_002", "test_case_name": "<PERSON><PERSON><PERSON> tra lịch sử gửi phê duyệt đ<PERSON><PERSON><PERSON> ghi nhận", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nTranh chấp chưa có lịch sử gửi phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> phê du<PERSON>'\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> tab lịch sử tranh chấp", "test_data": "T<PERSON><PERSON> chấp: #123", "expected_result": "Bước 1: <PERSON><PERSON><PERSON>hê du<PERSON>t thành công\nBước 2: <PERSON><PERSON><PERSON> sử ghi nhận thời gian, ng<PERSON><PERSON><PERSON> g<PERSON>, tr<PERSON><PERSON> thá<PERSON>", "test_case_type": "Positive"}]}]}]}, {"_id": "68d25cb738ec9064b3b87b3c", "name": "<PERSON><PERSON> cuộc tranh chấp", "description": "<PERSON><PERSON><PERSON><PERSON> duyệt có thể phê duyệt cuộc tranh chấp, <PERSON><PERSON><PERSON> <PERSON>hận qua popup và cập nhật trạng thái tranh chấp.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb16e", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb170", "name": "Chỉ người duyệt đ<PERSON><PERSON><PERSON> phép phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người có quyền duyệt mới thực hiện được thao tác phê duyệt.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa2b", "test_case_id": "Phan<PERSON><PERSON>en_001", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> duyệt thực hiện phê duyệt thành công", "priority": "High", "pre_condition": "Người dùng đã đăng nhập với vai trò người duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON>hập với tài kho<PERSON>n người duyệt\nBước 2: <PERSON><PERSON><PERSON> cập trang chi tiết tranh chấp\nBước 3: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON> duyệt'\nBước 4: <PERSON><PERSON><PERSON> <PERSON>hận trên popup", "test_data": "<PERSON><PERSON><PERSON>: duyet01, <PERSON><PERSON><PERSON>: 123456\nID tranh chấp: 1001", "expected_result": "Bước 3: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> duyệt' hi<PERSON><PERSON> thị\nBướ<PERSON> 4: <PERSON><PERSON> xác nhận xuất hiện\nSau khi xác nhận: <PERSON><PERSON><PERSON><PERSON> thái tranh chấp chuy<PERSON> sang '<PERSON><PERSON> phê duyệt'", "test_case_type": "Positive"}, {"_id": "68d25e4c841e3bd82603aa2e", "test_case_id": "Phan<PERSON><PERSON>en_002", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> không có quyền duyệt không thể phê duyệt", "priority": "High", "pre_condition": "Người dùng đã đăng nhập với vai trò không phải người duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập với tài khoản không phải người duyệt\nBước 2: <PERSON><PERSON><PERSON> cập trang chi tiết tranh chấp", "test_data": "<PERSON><PERSON><PERSON>: nhanvien01, <PERSON><PERSON><PERSON>: 123456\nID tranh chấp: 1001", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> du<PERSON>' không hiển thị trên giao di<PERSON>n", "test_case_type": "Negative"}, {"_id": "68d25e4c841e3bd82603aa31", "test_case_id": "Phan<PERSON><PERSON>en_003", "test_case_name": "<PERSON><PERSON><PERSON> tra <PERSON> bị chặn khi người không đủ quyền gọi trực tiếp", "priority": "Medium", "pre_condition": "Người dùng đã đăng nhập với vai trò không phải người duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> request API phê duyệt tranh chấp với token của người không đủ quyền", "test_data": "Token: nhanvien01\nID tranh chấp: 1001", "expected_result": "API trả về lỗi 403 Forbidden hoặc thông báo không đủ quyền", "test_case_type": "Negative"}, {"_id": "68d25e4c841e3bd82603aa34", "test_case_id": "PhanQuyen_004", "test_case_name": "<PERSON>ư<PERSON><PERSON> duyệt thuộc phòng ban khác không thể phê duyệt", "priority": "Medium", "pre_condition": "Người dùng là người duyệt nhưng không thuộc phòng ban xử lý tranh chấp", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập với tài khoản người duyệt thuộc phòng ban khác\nBước 2: <PERSON><PERSON><PERSON> cập trang chi tiết tranh chấp", "test_data": "<PERSON><PERSON><PERSON>: duyet02 (ph<PERSON><PERSON> ban <PERSON>), <PERSON><PERSON><PERSON><PERSON>: 123456\nID tranh chấp: 1001", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> du<PERSON>' không hiển thị hoặc thao tác bị chặn", "test_case_type": "Negative"}]}, {"_id": "68d25d74b5e186666c9cb172", "name": "Ẩn nút phê duyệt với người không đủ quyền", "rationale": "<PERSON><PERSON><PERSON> tra giao diện không hiển thị chức năng phê duyệt cho người không đủ quyền.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa38", "test_case_id": "Phan<PERSON><PERSON>en_005", "test_case_name": "Ẩn nút phê duyệt với người dùng khách", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON><PERSON> dùng chưa đăng nhập", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp khi chưa đăng nhập", "test_data": "ID tranh chấp: 1001", "expected_result": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> du<PERSON>' không hiển thị trên giao di<PERSON>n", "test_case_type": "UI"}, {"_id": "68d25e4c841e3bd82603aa3b", "test_case_id": "Phan<PERSON><PERSON>en_006", "test_case_name": "Ẩn nút phê duyệt với người dùng có vai trò khác", "priority": "High", "pre_condition": "Người dùng đã đăng nhập với vai trò không phải người duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> nh<PERSON><PERSON> với tài k<PERSON>n nhân viên\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp", "test_data": "<PERSON><PERSON><PERSON>: nhanvien02, <PERSON><PERSON><PERSON>: 123456\nID tranh chấp: 1001", "expected_result": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> du<PERSON>' không hiển thị trên giao di<PERSON>n", "test_case_type": "UI"}]}]}, {"_id": "68d25d74b5e186666c9cb174", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb176", "name": "<PERSON><PERSON><PERSON> chấp ở trạng thái chờ phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ tranh chấp ở trạng thái chờ phê duyệt mới được phép phê duyệt.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa40", "test_case_id": "TienDieuKien_001", "test_case_name": "Chỉ tranh chấp chờ phê duyệt mới có nút phê duyệt", "priority": "High", "pre_condition": "<PERSON>ư<PERSON><PERSON> dùng là ng<PERSON><PERSON>i du<PERSON>", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập với tài khoản người duyệt\nBước 2: <PERSON><PERSON><PERSON> cập tranh chấp ở trạng thái 'Chờ phê duyệt'", "test_data": "ID tranh chấp: 1002 (<PERSON><PERSON>)", "expected_result": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>' hiển thị trên giao di<PERSON>n", "test_case_type": "Positive"}, {"_id": "68d25e4c841e3bd82603aa43", "test_case_id": "TienDieuKien_002", "test_case_name": "<PERSON><PERSON><PERSON>ng hiển thị nút phê duyệt với tranh chấp đã phê duyệt", "priority": "High", "pre_condition": "<PERSON>ư<PERSON><PERSON> dùng là ng<PERSON><PERSON>i du<PERSON>", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập với tài khoản người duyệt\nBước 2: <PERSON><PERSON><PERSON> cập tranh chấp ở trạng thái '<PERSON><PERSON> phê duyệt'", "test_data": "ID tranh chấp: 1003 (<PERSON><PERSON>)", "expected_result": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> du<PERSON>' không hiển thị trên giao di<PERSON>n", "test_case_type": "Negative"}]}, {"_id": "68d25d74b5e186666c9cb178", "name": "<PERSON><PERSON><PERSON><PERSON> du<PERSON> thu<PERSON><PERSON> phòng ban phù hợp", "rationale": "<PERSON><PERSON><PERSON> tra người duyệt phải thuộc phòng ban đư<PERSON><PERSON> phân công xử lý tranh chấp.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa47", "test_case_id": "TienDieuKien_003", "test_case_name": "Người duyệt đúng phòng ban mới đư<PERSON><PERSON> phê duyệt", "priority": "High", "pre_condition": "<PERSON>ư<PERSON>i dùng là người duyệt thu<PERSON><PERSON> phòng ban xử lý tranh chấp", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập với tài khoản ngư<PERSON><PERSON> duyệt thuộc phòng ban phù hợp\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp cần phê duyệt", "test_data": "<PERSON><PERSON><PERSON>: duyet03 (phòng ban A), <PERSON><PERSON><PERSON> <PERSON><PERSON>: 123456\nID tranh chấp: 1004 (phòng ban A)", "expected_result": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>' hiển thị trên giao di<PERSON>n", "test_case_type": "Positive"}, {"_id": "68d25e4c841e3bd82603aa4a", "test_case_id": "TienDieuKien_004", "test_case_name": "Người duyệt sai phòng ban không đư<PERSON><PERSON> phê duyệt", "priority": "High", "pre_condition": "<PERSON>ư<PERSON>i dùng là người duyệt thu<PERSON><PERSON> phòng ban khác", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập với tài khoản ngư<PERSON><PERSON> duyệt thuộc phòng ban B\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp thuộc phòng ban A", "test_data": "<PERSON><PERSON><PERSON>: duyet04 (phòng ban B), <PERSON><PERSON><PERSON> <PERSON><PERSON>: 123456\nID tranh chấp: 1004 (phòng ban A)", "expected_result": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> du<PERSON>' không hiển thị trên giao di<PERSON>n", "test_case_type": "Negative"}]}]}, {"_id": "68d25d74b5e186666c9cb17a", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb17c", "name": "<PERSON><PERSON><PERSON> thị popup x<PERSON>c nhận phê du<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống hiển thị popup xác nhận khi người dùng thực hiện phê du<PERSON>.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa4f", "test_case_id": "ManHinh_001", "test_case_name": "<PERSON><PERSON><PERSON> thị popup x<PERSON>c nhận khi nhấn phê duyệt", "priority": "High", "pre_condition": "Người dùng là người duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON>h<PERSON>p với tài kho<PERSON>n người duyệt\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp cần phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> nút '<PERSON><PERSON> duyệt'", "test_data": "ID tranh chấp: 1005", "expected_result": "Popup xác nhận phê duyệt hiển thị với nội dung rõ ràng, c<PERSON> nút xác nhận và hủy", "test_case_type": "UI"}, {"_id": "68d25e4c841e3bd82603aa52", "test_case_id": "ManHinh_002", "test_case_name": "<PERSON><PERSON><PERSON> tra popup x<PERSON>c nhận trên nhiều trình <PERSON>", "priority": "Medium", "pre_condition": "<PERSON>ư<PERSON><PERSON> dùng là ng<PERSON><PERSON>i du<PERSON>", "steps": "Bước 1: <PERSON><PERSON><PERSON> <PERSON>hập với tài khoản người duyệt trên Chrome\nBước 2: <PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> '<PERSON><PERSON> duyệt' và kiểm tra popup\nBước 3: Lặp lại trê<PERSON> Firefox, Edge", "test_data": "ID tranh chấp: 1005", "expected_result": "Popup xác nhận hiển thị đúng trên tất cả các trình du<PERSON>, kh<PERSON><PERSON> bị vỡ layout", "test_case_type": "Compatibility"}]}, {"_id": "68d25d74b5e186666c9cb17e", "name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> kết quả phê duyệt", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống hiển thị thông báo thành công hoặc thất bại sau khi phê duyệt.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa56", "test_case_id": "ManHinh_003", "test_case_name": "<PERSON><PERSON><PERSON> thị thông báo thành công sau khi phê duyệt", "priority": "High", "pre_condition": "Người dùng là người duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON>h<PERSON>p với tài kho<PERSON>n người duyệt\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp cần phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON> duyệt'\nBước 4: <PERSON><PERSON><PERSON> nhận trên popup", "test_data": "ID tranh chấp: 1006", "expected_result": "<PERSON><PERSON> <PERSON>hi xác nhận: <PERSON><PERSON><PERSON> thị thông bá<PERSON> '<PERSON><PERSON> duyệt thành công' rõ ràng, d<PERSON> hiểu", "test_case_type": "UI"}, {"_id": "68d25e4c841e3bd82603aa59", "test_case_id": "ManHinh_004", "test_case_name": "Hi<PERSON>n thị thông báo lỗi khi phê duyệt thất bại", "priority": "High", "pre_condition": "Người dùng là người duyệt\nTranh chấp ở trạng thái chờ phê duyệt\nMô phỏng lỗi hệ thống", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập với tài kho<PERSON>n người duyệt\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp cần phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON> duyệt'\nBước 4: <PERSON><PERSON><PERSON> nhận trên popup (khi hệ thống lỗi)", "test_data": "ID tranh chấp: 1007 (mô phỏng lỗi backend)", "expected_result": "<PERSON><PERSON> <PERSON>hi xác nhận: <PERSON><PERSON><PERSON> thị thông báo lỗi rõ ràng, không thay đổi trạng thái tranh chấp", "test_case_type": "UI"}]}]}, {"_id": "68d25d74b5e186666c9cb180", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb182", "name": "<PERSON><PERSON><PERSON> phê duyệt thành công", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp đ<PERSON><PERSON><PERSON> cập nhật đúng khi phê duyệt thành công.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa5e", "test_case_id": "XuLy_001", "test_case_name": "<PERSON><PERSON> duy<PERSON>t thành công cập nhật trạng thái", "priority": "High", "pre_condition": "Người dùng là người duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON>h<PERSON>p với tài kho<PERSON>n người duyệt\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp cần phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON> duyệt'\nBước 4: <PERSON><PERSON><PERSON> nhận trên popup", "test_data": "ID tranh chấp: 1008", "expected_result": "<PERSON><PERSON> <PERSON>hi x<PERSON><PERSON> nhận: <PERSON>r<PERSON><PERSON> thái tranh chấp chuy<PERSON> sang 'Đã phê duyệt'", "test_case_type": "Positive"}]}, {"_id": "68d25d74b5e186666c9cb184", "name": "<PERSON><PERSON><PERSON> ph<PERSON> du<PERSON>t bị hủy qua popup", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không thay đổi trạng thái khi người dùng hủy thao tác phê duyệt.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa62", "test_case_id": "XuLy_002", "test_case_name": "<PERSON><PERSON><PERSON> phê duyệt trên popup không thay đổi trạng thái", "priority": "High", "pre_condition": "Người dùng là người duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON>h<PERSON>p với tài kho<PERSON>n người duyệt\nBước 2: <PERSON><PERSON><PERSON> cập tranh chấp cần phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON> duyệt'\nBước 4: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON><PERSON>' trên popup xác nhận", "test_data": "ID tranh chấp: 1009", "expected_result": "<PERSON>r<PERSON><PERSON> thái tranh chấp không thay đổi, vẫn là 'Chờ phê duyệt'", "test_case_type": "Positive"}]}]}, {"_id": "68d25d74b5e186666c9cb186", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb188", "name": "<PERSON>ử lý lỗi khi phê duyệt thất bại", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống thông báo lỗi khi phê duyệt không thành công do sự cố hệ thống.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa67", "test_case_id": "NgoaiLe_001", "test_case_name": "<PERSON><PERSON> du<PERSON>t thất bại do lỗi backend", "priority": "High", "pre_condition": "Người dùng là người duyệt\nTranh chấp ở trạng thái chờ phê duyệt\nMô phỏng lỗi backend", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập với tài kho<PERSON>n người duyệt\nBước 2: <PERSON><PERSON><PERSON> cập tranh chấp cần phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON> duyệt'\nBước 4: <PERSON><PERSON><PERSON> nhận trên popup (khi backend lỗi)", "test_data": "ID tranh chấp: 1010 (mô phỏng lỗi backend)", "expected_result": "<PERSON><PERSON><PERSON> thị thông báo lỗi, trạng thái tranh chấp không thay đổi", "test_case_type": "Negative"}]}, {"_id": "68d25d74b5e186666c9cb18a", "name": "<PERSON><PERSON> lý phê duyệt trùng lặp", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không cho phép phê duyệt nhiều lần cho cùng một tranh chấp.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa6b", "test_case_id": "NgoaiLe_002", "test_case_name": "<PERSON>h<PERSON><PERSON> cho phép phê duyệt trùng lặp", "priority": "High", "pre_condition": "Người dùng là người duyệt\nT<PERSON>h chấp đã đư<PERSON><PERSON> phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập với tài khoản người duyệt\nBước 2: <PERSON><PERSON><PERSON> cập tranh chấp đã phê duyệt\nBước 3: <PERSON><PERSON><PERSON> thao tác phê duyệt lại (nếu có nút hoặc gọi API)", "test_data": "ID tranh chấp: 1011 (đ<PERSON> <PERSON><PERSON>)", "expected_result": "<PERSON><PERSON><PERSON><PERSON> cho ph<PERSON><PERSON> thao tác, hiển thị thông báo lỗi hoặc không hiển thị nút phê duyệt", "test_case_type": "Negative"}]}]}, {"_id": "68d25d74b5e186666c9cb18c", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb18e", "name": "<PERSON><PERSON><PERSON> nhật trạng thái tranh chấp sau phê duy<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp chuy<PERSON> sang đã phê duyệt sau khi thao tác thành công.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa70", "test_case_id": "AnhHuong_001", "test_case_name": "<PERSON>r<PERSON><PERSON> thái tranh chấp chuy<PERSON> sang đã phê duyệt", "priority": "High", "pre_condition": "Người dùng là người duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON>h<PERSON>p với tài kho<PERSON>n người duyệt\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp cần phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON> duyệt'\nBước 4: <PERSON><PERSON><PERSON> nhận trên popup", "test_data": "ID tranh chấp: 1012", "expected_result": "Tr<PERSON>ng thái tranh chấ<PERSON> chuy<PERSON> sang 'Đã phê duyệt' trên giao diện và trong cơ sở dữ liệu", "test_case_type": "Positive"}]}, {"_id": "68d25d74b5e186666c9cb190", "name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> lịch sử phê du<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống lưu lại lịch sử phê duyệt cho tranh chấp.", "test_cases": [{"_id": "68d25e4c841e3bd82603aa74", "test_case_id": "AnhHuong_002", "test_case_name": "<PERSON><PERSON><PERSON> sử phê duyệt đ<PERSON><PERSON><PERSON> ghi nhận sau khi phê duyệt", "priority": "High", "pre_condition": "Người dùng là người duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON>h<PERSON> với tài kho<PERSON>n người duyệt\nBước 2: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp cần phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON><PERSON> duyệt'\nBước 4: <PERSON><PERSON><PERSON> <PERSON>hận trên popup\nBước 5: <PERSON><PERSON><PERSON> <PERSON>ra lịch sử phê duyệt của tranh chấp", "test_data": "ID tranh chấp: 1013", "expected_result": "<PERSON><PERSON><PERSON> sử phê duyệt ghi nhận đầy đủ thông tin: ng<PERSON><PERSON><PERSON> du<PERSON>, th<PERSON><PERSON> gian, tr<PERSON><PERSON> thái", "test_case_type": "Positive"}]}]}]}, {"_id": "68d25cb738ec9064b3b87b3e", "name": "Từ chối cuộc tranh chấp", "description": "<PERSON><PERSON><PERSON><PERSON> duy<PERSON>t có thể từ chối cuộc tranh chấp, <PERSON><PERSON><PERSON> <PERSON>h<PERSON>n qua popup và cập nhật trạng thái tranh chấp.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb193", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb195", "name": "Chỉ người duyệt đ<PERSON><PERSON><PERSON> phép từ chối", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người có quyền duyệt mới thực hiện đư<PERSON>c thao tác từ chối.", "test_cases": [{"_id": "68d25e74841e3bd82603aa7b", "test_case_id": "Permission_001", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> du<PERSON> thực hiện từ chối tranh chấp", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản có quyền duyệt\nCó tranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp\n<PERSON> 2: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON>ừ chối'\nBước 3: <PERSON><PERSON><PERSON> <PERSON>hận trên popup", "test_data": "<PERSON><PERSON><PERSON>: reviewer1\nT<PERSON>h chấp: #123 (chờ ph<PERSON>)", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> thị nút 'Từ chối'\nBước 2: <PERSON><PERSON><PERSON> thị popup xác nhận\nBước 3: <PERSON><PERSON><PERSON><PERSON> thái tranh chấ<PERSON> chuy<PERSON> sang 'Đã từ chối'", "test_case_type": "Positive"}, {"_id": "68d25e74841e3bd82603aa7e", "test_case_id": "Permission_002", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> không có quyền duyệt không thể từ chối", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản không có quyền duyệt\nCó tranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp", "test_data": "<PERSON><PERSON><PERSON>: user1 (khô<PERSON> có quyền du<PERSON>)\n<PERSON><PERSON><PERSON> chấp: #123", "expected_result": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON>ể<PERSON> thị nút 'Từ chối' trên giao di<PERSON>n", "test_case_type": "Negative"}]}, {"_id": "68d25d74b5e186666c9cb197", "name": "Ẩn nút từ chối với người không đủ quyền", "rationale": "<PERSON><PERSON><PERSON> tra giao diện không hiển thị chức năng từ chối cho người không đủ quyền.", "test_cases": [{"_id": "68d25e74841e3bd82603aa82", "test_case_id": "Permission_003", "test_case_name": "Ẩn nút từ chối với vai trò khách", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nh<PERSON>p với tài k<PERSON> kh<PERSON>ch", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp", "test_data": "<PERSON><PERSON><PERSON>: guest1\nT<PERSON>h chấp: #123", "expected_result": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON>ể<PERSON> thị nút 'Từ chối' trên giao di<PERSON>n", "test_case_type": "UI"}, {"_id": "68d25e74841e3bd82603aa85", "test_case_id": "Permission_004", "test_case_name": "Ẩn nút từ chối với vai trò nhân viên không liên quan", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản nhân viên không thuộc phòng ban xử lý", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp", "test_data": "<PERSON><PERSON><PERSON>: staff2 (kh<PERSON><PERSON> thu<PERSON><PERSON> phòng ban xử lý)\nT<PERSON><PERSON> chấp: #123", "expected_result": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON>ể<PERSON> thị nút 'Từ chối' trên giao di<PERSON>n", "test_case_type": "UI"}]}]}, {"_id": "68d25d74b5e186666c9cb199", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb19b", "name": "<PERSON><PERSON><PERSON> chấp ở trạng thái chờ phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ tranh chấp ở trạng thái chờ phê duyệt mới được phép từ chối.", "test_cases": [{"_id": "68d25e74841e3bd82603aa8a", "test_case_id": "Precondition_001", "test_case_name": "Chỉ cho phép từ chối khi tranh chấp ở trạng thái chờ phê duyệt", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài k<PERSON>n có quyền du<PERSON>t", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp ở trạng thái '<PERSON><PERSON> xử lý'\nBước 2: <PERSON><PERSON><PERSON> tra nút 'Từ chối'", "test_data": "T<PERSON><PERSON> chấp: #124 (đã xử lý)", "expected_result": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON>ể<PERSON> thị nút 'Từ chối' trên giao di<PERSON>n", "test_case_type": "Negative"}, {"_id": "68d25e74841e3bd82603aa8d", "test_case_id": "Precondition_002", "test_case_name": "Từ chối tranh chấp ở trạng thái chờ phê duyệt", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài k<PERSON>n có quyền du<PERSON>t", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp ở trạng thái 'Chờ phê duyệt'\nBước 2: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> 'Từ chối'\nBước 3: <PERSON><PERSON><PERSON> nhận trên popup", "test_data": "T<PERSON>h chấp: #125 (chờ ph<PERSON>)", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> thị nút 'Từ chối'\nBước 2: <PERSON><PERSON><PERSON> thị popup xác nhận\nBước 3: <PERSON><PERSON><PERSON><PERSON> th<PERSON><PERSON> chuyể<PERSON> sang 'Đã từ chối'", "test_case_type": "Positive"}]}, {"_id": "68d25d74b5e186666c9cb19d", "name": "<PERSON><PERSON><PERSON><PERSON> du<PERSON> thu<PERSON><PERSON> phòng ban phù hợp", "rationale": "<PERSON><PERSON><PERSON> tra người duyệt phải thuộc phòng ban đư<PERSON><PERSON> phân công xử lý tranh chấp.", "test_cases": [{"_id": "68d25e74841e3bd82603aa91", "test_case_id": "Precondition_003", "test_case_name": "Ngườ<PERSON> duyệt không thuộc phòng ban không thể từ chối", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nh<PERSON>p vớ<PERSON> tà<PERSON> reviewer kh<PERSON><PERSON> thu<PERSON><PERSON> phòng ban xử lý", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp thuộc phòng ban khác\nBước 2: <PERSON><PERSON><PERSON> tra nút 'Từ chối'", "test_data": "<PERSON><PERSON><PERSON>: reviewer2 (phòng ban B)\n<PERSON><PERSON><PERSON> chấp: #126 (phòng ban A)", "expected_result": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON>ể<PERSON> thị nút 'Từ chối' trên giao di<PERSON>n", "test_case_type": "Negative"}]}]}, {"_id": "68d25d74b5e186666c9cb19f", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb1a1", "name": "<PERSON><PERSON>n thị popup x<PERSON>c nhận từ chối", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống hiển thị popup xác nhận khi người dùng thực hiện từ chối.", "test_cases": [{"_id": "68d25e74841e3bd82603aa96", "test_case_id": "UI_001", "test_case_name": "<PERSON><PERSON>n thị popup x<PERSON>c nhận khi nhấn từ chối", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản có quyền duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> trang chi tiết tranh chấp\n<PERSON>ướ<PERSON> 2: <PERSON><PERSON><PERSON><PERSON> nú<PERSON> '<PERSON>ừ chối'", "test_data": "T<PERSON><PERSON> chấp: #127", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> thị nút '<PERSON>ừ chối'\nBước 2: <PERSON><PERSON><PERSON> thị popup xác nhận với nội dung rõ ràng", "test_case_type": "UI"}, {"_id": "68d25e74841e3bd82603aa99", "test_case_id": "UI_002", "test_case_name": "<PERSON><PERSON><PERSON> tra giao diện popup trên nhiều trình <PERSON>", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài k<PERSON>n có quyền du<PERSON>t", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp trên Chrome\nBước 2: <PERSON><PERSON><PERSON><PERSON> '<PERSON><PERSON> chối' và kiểm tra popup\nBước 3: Lặp lạ<PERSON> trê<PERSON> Firefox, Edge", "test_data": "<PERSON><PERSON><PERSON><PERSON>: <PERSON><PERSON>, <PERSON>fo<PERSON>, <PERSON> chấp: #127", "expected_result": "Bước 2-3: <PERSON><PERSON>ể<PERSON> thị đ<PERSON>, kh<PERSON><PERSON> vỡ layout trên tất cả trình du<PERSON>t", "test_case_type": "Compatibility"}]}, {"_id": "68d25d74b5e186666c9cb1a3", "name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o kết quả từ chối", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống hiển thị thông báo thành công hoặc thất bại sau khi từ chối.", "test_cases": [{"_id": "68d25e74841e3bd82603aa9d", "test_case_id": "UI_003", "test_case_name": "<PERSON><PERSON><PERSON> thị thông báo thành công khi từ chối", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản có quyền duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> '<PERSON><PERSON> chối'\nBước 2: <PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> trên popup", "test_data": "T<PERSON><PERSON> chấp: #128", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> thị thông bá<PERSON> '<PERSON><PERSON> chối thành công' r<PERSON> r<PERSON>, <PERSON><PERSON> hi<PERSON><PERSON>", "test_case_type": "UI"}]}]}, {"_id": "68d25d74b5e186666c9cb1a5", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb1a7", "name": "<PERSON><PERSON><PERSON> từ chối thành công", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp đ<PERSON><PERSON><PERSON> cập nhật đúng khi từ chối thành công.", "test_cases": [{"_id": "68d25e74841e3bd82603aaa2", "test_case_id": "Flow_001", "test_case_name": "Từ chối tranh chấp thành công cập nhật trạng thái", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản có quyền duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> '<PERSON><PERSON> chối'\nBước 2: <PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> trên popup", "test_data": "T<PERSON><PERSON> chấp: #129", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> thá<PERSON> tranh chấ<PERSON> sang 'Đã từ chối'", "test_case_type": "Positive"}]}, {"_id": "68d25d74b5e186666c9cb1a9", "name": "<PERSON><PERSON><PERSON> từ chối bị hủy qua popup", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không thay đổi trạng thái khi người dùng hủy thao tác từ chối.", "test_cases": [{"_id": "68d25e74841e3bd82603aaa6", "test_case_id": "Flow_002", "test_case_name": "<PERSON><PERSON><PERSON> thao tác từ chối trên popup", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản có quyền duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> '<PERSON><PERSON> chối'\nBước 2: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON>' trên popup x<PERSON>c nhận", "test_data": "T<PERSON><PERSON> chấp: #130", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> thái tranh chấp không thay đổi, vẫn là 'Chờ phê duyệt'", "test_case_type": "Positive"}]}]}, {"_id": "68d25d74b5e186666c9cb1ab", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb1ad", "name": "<PERSON>ử lý lỗi khi từ chối thất bại", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống thông báo lỗi khi từ chối không thành công do sự cố hệ thống.", "test_cases": [{"_id": "68d25e74841e3bd82603aaab", "test_case_id": "Exception_001", "test_case_name": "Từ chối thất bại do lỗi hệ thống", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản có quyền duyệt\nTranh chấp ở trạng thái chờ phê duyệt\nMô phỏng lỗi hệ thống (ví dụ: ngắt kết nối DB)", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> n<PERSON><PERSON> '<PERSON><PERSON> chối'\nBước 2: <PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> trên popup", "test_data": "T<PERSON><PERSON> chấp: #131", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> thị thông báo lỗi rõ ràng, tr<PERSON><PERSON> thái tranh chấp không thay đổi", "test_case_type": "Edge"}]}, {"_id": "68d25d74b5e186666c9cb1af", "name": "<PERSON><PERSON> lý từ chối trùng lặp", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không cho phép từ chối nhiều lần cho cùng một tranh chấp.", "test_cases": [{"_id": "68d25e74841e3bd82603aaaf", "test_case_id": "Exception_002", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> cho phép từ chối tranh chấp đã bị từ chối", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> chấp đã ở trạng thái 'Đã từ chối'", "steps": "Bước 1: <PERSON><PERSON><PERSON> cậ<PERSON> tranh chấp đã bị từ chối\nBước 2: <PERSON><PERSON><PERSON> tra nút 'Từ chối'", "test_data": "T<PERSON>h chấp: #132 (đã từ chối)", "expected_result": "Bước 1: <PERSON><PERSON><PERSON>ng hiển thị nút 'Từ chối' hoặc hiển thị thông báo không thể thao tác", "test_case_type": "Negative"}]}]}, {"_id": "68d25d74b5e186666c9cb1b1", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb1b3", "name": "<PERSON><PERSON><PERSON> nhật trạng thái tranh chấp sau từ chối", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp chuy<PERSON> sang đã từ chối sau khi thao tác thành công.", "test_cases": [{"_id": "68d25e74841e3bd82603aab4", "test_case_id": "Impact_001", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> thái tranh chấ<PERSON> sang 'Đã từ chối'", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản có quyền duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> '<PERSON><PERSON> chối'\nBước 2: <PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> trên popup\nBước 3: <PERSON><PERSON><PERSON> tra trạng thái tranh chấp", "test_data": "T<PERSON><PERSON> chấp: #133", "expected_result": "Bước 3: <PERSON><PERSON><PERSON><PERSON> thái tranh chấp là '<PERSON><PERSON> từ chối'", "test_case_type": "Positive"}]}, {"_id": "68d25d74b5e186666c9cb1b5", "name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> lịch sử từ chối", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống lưu lại lịch sử từ chối cho tranh chấp.", "test_cases": [{"_id": "68d25e74841e3bd82603aab8", "test_case_id": "Impact_002", "test_case_name": "<PERSON><PERSON><PERSON> sử ghi nhận thao tác từ chối", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> nhập với tài khoản có quyền duyệt\nTranh chấp ở trạng thái chờ phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> '<PERSON><PERSON> chối'\nBước 2: <PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> trên popup\nBước 3: <PERSON><PERSON><PERSON> tra lịch sử tranh chấp", "test_data": "T<PERSON><PERSON> chấp: #134", "expected_result": "Bước 3: <PERSON><PERSON><PERSON> sử ghi nhận thao tác từ chối với thông tin người thao tác, th<PERSON><PERSON> gian", "test_case_type": "Positive"}]}]}]}, {"_id": "68d25cb738ec9064b3b87b40", "name": "<PERSON><PERSON> danh s<PERSON>ch ng<PERSON><PERSON> p<PERSON>ê <PERSON>", "description": "<PERSON><PERSON>n thị danh sách người có quyền phê duyệt dựa trên chi nhánh hoặc phòng ban phụ trách tranh chấp.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb1b8", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb1ba", "name": "Chỉ người có quyền xem danh sách", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người dùng có quyền mới xem được danh sách người phê duyệt.", "test_cases": [{"_id": "68d25ed1841e3bd82603aac0", "test_case_id": "Permission_001", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> dùng có quyền truy cập xem đ<PERSON><PERSON><PERSON> danh sách người phê duyệt", "priority": "High", "pre_condition": "Người dùng đã đư<PERSON><PERSON> gán quyền xem danh sách người phê duyệt\nĐã đăng nhập vào hệ thống", "steps": "Bước 1: <PERSON><PERSON><PERSON> <PERSON>hập v<PERSON><PERSON> hệ thống bằng tài khoản có quyền xem danh sách\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt", "test_data": "<PERSON><PERSON><PERSON>: user_approve (c<PERSON> quyền xem danh sách)", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> thành công\nBước 2: <PERSON><PERSON> sách người phê duyệt hiển thị đúng trên giao di<PERSON>n", "test_case_type": "Positive"}, {"_id": "68d25ed1841e3bd82603aac3", "test_case_id": "Permission_002", "test_case_name": "Người dùng không có quyền truy cập không xem đư<PERSON>c danh sách người phê duyệt", "priority": "High", "pre_condition": "Người dùng không đư<PERSON><PERSON> gán quyền xem danh sách người phê duyệt\nĐã đăng nhập vào hệ thống", "steps": "Bước 1: <PERSON><PERSON><PERSON> <PERSON>hập v<PERSON><PERSON> hệ thống bằng tài khoản không có quyền xem danh sách\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt", "test_data": "<PERSON><PERSON><PERSON>: user_normal (không có quyền)", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> <PERSON>h<PERSON><PERSON> thành công\nBước 2: <PERSON><PERSON> thống hiển thị thông báo lỗi hoặc không hiển thị danh sách người phê duyệt", "test_case_type": "Negative"}, {"_id": "68d25ed1841e3bd82603aac6", "test_case_id": "Permission_003", "test_case_name": "<PERSON><PERSON><PERSON> tra phân quyền khi truy cập trực tiếp bằng URL", "priority": "Medium", "pre_condition": "Người dùng không có quyền xem danh sách người phê duyệt\nĐã đăng nhập vào hệ thống", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản không có quyền\nBước 2: <PERSON><PERSON><PERSON><PERSON> trực tiếp URL của trang danh sách người phê duyệt trên trình duyệt", "test_data": "<PERSON><PERSON><PERSON>: user_normal\nURL: /approver-list/", "expected_result": "Bước 1: <PERSON><PERSON><PERSON> <PERSON>hậ<PERSON> thành công\nBước 2: <PERSON><PERSON> thống từ chối truy cập và hiển thị thông báo lỗi phân quyền", "test_case_type": "Negative"}, {"_id": "68d25ed1841e3bd82603aac9", "test_case_id": "Permission_004", "test_case_name": "<PERSON><PERSON><PERSON> tra hiển thị nút truy cập danh sách theo quyền", "priority": "Medium", "pre_condition": "Ngườ<PERSON> dùng đã đăng nhập vào hệ thống", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> tra sự xuất hiện của nút/link truy cập danh sách người phê duyệt\nBước 3: <PERSON><PERSON><PERSON> xuất và đăng nhập bằng tài khoản không có quyền\nBước 4: <PERSON><PERSON><PERSON> tra lại sự xuất hiện của nút/link", "test_data": "<PERSON><PERSON><PERSON>: user_approve, user_normal", "expected_result": "Bước 2: Nút/link xuất hiện với user_approve\nBước 4: Nút/link không xuất hiện với user_normal", "test_case_type": "UI"}, {"_id": "68d25ed1841e3bd82603aacc", "test_case_id": "Permission_005", "test_case_name": "<PERSON><PERSON><PERSON> tra phân quyền trên các trình du<PERSON>t khác nhau", "priority": "Low", "pre_condition": "Ngườ<PERSON> dùng có và không có quyền đã đăng nhập", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền trên <PERSON>, Firefox, Edge\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt trên từng trình duyệt\nBước 3: Lặp lại với tài khoản không có quyền", "test_data": "<PERSON><PERSON><PERSON>: user_approve, user_normal", "expected_result": "Bước 2: <PERSON><PERSON> sách hiển thị đúng với user_approve trên mọi trình duyệt\nBước 3: <PERSON><PERSON> sách không hiển thị với user_normal trên mọi trình duyệt", "test_case_type": "Compatibility"}]}, {"_id": "68d25d74b5e186666c9cb1bc", "name": "Ẩn danh sách với người không đủ quyền", "rationale": "<PERSON><PERSON><PERSON> tra giao diện không hiển thị danh sách người phê duyệt cho người không đủ quyền.", "test_cases": [{"_id": "68d25ed1841e3bd82603aad0", "test_case_id": "Permission_006", "test_case_name": "Ẩn hoàn toàn danh sách với người không đủ quyền", "priority": "High", "pre_condition": "<PERSON>ư<PERSON><PERSON> dùng không có quyền xem danh sách", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản không đủ quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt", "test_data": "<PERSON><PERSON><PERSON>: user_guest", "expected_result": "Bước 2: <PERSON><PERSON><PERSON>ng hiển thị bất kỳ thông tin nào về người phê duyệt, có thể hiển thị thông báo 'Bạn không có quyền truy cập'", "test_case_type": "Negative"}, {"_id": "68d25ed1841e3bd82603aad3", "test_case_id": "Permission_007", "test_case_name": "<PERSON><PERSON><PERSON> tra không hiển thị thông tin người phê duyệt trong API response với người không đủ quyền", "priority": "Medium", "pre_condition": "<PERSON>ư<PERSON><PERSON> dùng không có quyền", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản không đủ quyền\nBước 2: <PERSON><PERSON><PERSON> request API lấy danh sách người phê duyệt", "test_data": "<PERSON><PERSON><PERSON>: user_guest", "expected_result": "Bước 2: <PERSON> trả về lỗi 403 hoặc không trả về dữ liệu người phê duyệt", "test_case_type": "Negative"}, {"_id": "68d25ed1841e3bd82603aad6", "test_case_id": "Permission_008", "test_case_name": "<PERSON><PERSON><PERSON> tra <PERSON>n danh sách khi chuyển quyền động", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON><PERSON> dùng đang có quyền xem danh sách", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> trị viên thu hồi quyền xem danh sách của tài khoản này\nBước 4: <PERSON><PERSON>resh lại trang", "test_data": "<PERSON><PERSON><PERSON>: user_approve", "expected_result": "Bước 2: <PERSON><PERSON> sách hiển thị\nBước 4: <PERSON><PERSON> sách không còn hiển thị, có thể hiển thị thông báo không đủ quyền", "test_case_type": "Edge"}]}]}, {"_id": "68d25d74b5e186666c9cb1be", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb1c0", "name": "<PERSON><PERSON> tranh chấp thuộc chi nh<PERSON>h/phòng ban", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o chỉ hiển thị danh sách người phê duyệt khi có tranh chấp thuộc chi nhánh hoặc phòng ban.", "test_cases": [{"_id": "68d25ed1841e3bd82603aadb", "test_case_id": "Precondition_001", "test_case_name": "<PERSON><PERSON><PERSON> thị danh sách khi có tranh chấp thu<PERSON><PERSON> chi nh<PERSON>h", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON><PERSON> dùng có quyền xem danh sách\nCó tranh chấp thu<PERSON><PERSON> chi nh<PERSON>h A", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt của chi nhánh A", "test_data": "<PERSON><PERSON><PERSON>: user_approve\nChi n<PERSON>h: A", "expected_result": "Bước 2: <PERSON><PERSON> s<PERSON>ch ngư<PERSON>i phê duyệt của chi nh<PERSON>h A hiển thị đúng", "test_case_type": "Positive"}, {"_id": "68d25ed1841e3bd82603aade", "test_case_id": "Precondition_002", "test_case_name": "<PERSON><PERSON><PERSON>ng hiển thị danh sách khi không có tranh chấp thu<PERSON><PERSON> chi nh<PERSON>h", "priority": "High", "pre_condition": "<PERSON>ư<PERSON><PERSON> dùng có quyền xem danh sách\nKhông có tranh chấp nào thuộc chi nhánh B", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt của chi nhánh B", "test_data": "<PERSON><PERSON><PERSON>: user_approve\nChi nh<PERSON>h: B", "expected_result": "Bước 2: <PERSON><PERSON> thống hiển thị thông bá<PERSON> '<PERSON><PERSON><PERSON>ng có tranh chấp nào thuộc chi nhánh này'", "test_case_type": "Negative"}, {"_id": "68d25ed1841e3bd82603aae1", "test_case_id": "Precondition_003", "test_case_name": "<PERSON><PERSON><PERSON> thị danh sách khi có tranh chấp thu<PERSON><PERSON> phòng ban", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON><PERSON> dùng có quyền xem danh sách\nCó tranh chấp thu<PERSON><PERSON> phòng ban K<PERSON> toán", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt của phòng ban Kế toán", "test_data": "<PERSON><PERSON><PERSON>: user_approve\nPhòng ban: Kế toán", "expected_result": "Bước 2: <PERSON><PERSON> s<PERSON>ch ngư<PERSON>i phê duyệt của phòng ban Kế to<PERSON> hiển thị đúng", "test_case_type": "Positive"}]}]}, {"_id": "68d25d74b5e186666c9cb1c2", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb1c4", "name": "<PERSON><PERSON><PERSON> thị đúng danh sách người phê duyệt", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o danh sách người phê duyệt hiển thị đúng theo dữ liệu hệ thống.", "test_cases": [{"_id": "68d25ed1841e3bd82603aae6", "test_case_id": "UI_001", "test_case_name": "<PERSON><PERSON><PERSON> thị đầy đủ danh sách người phê duyệt", "priority": "High", "pre_condition": "Ngư<PERSON>i dùng có quyền xem danh sách\nCó dữ liệu người phê duyệt trong hệ thống", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt\nBước 3: <PERSON><PERSON><PERSON> chiếu danh sách hiển thị với dữ liệu hệ thống", "test_data": "<PERSON><PERSON><PERSON>: user_approve", "expected_result": "Bước 3: <PERSON><PERSON> s<PERSON>ch hiển thị trùng khớp với dữ liệu hệ thống", "test_case_type": "Positive"}, {"_id": "68d25ed1841e3bd82603aae9", "test_case_id": "UI_002", "test_case_name": "<PERSON><PERSON><PERSON> tra hiển thị danh sách trên các độ phân gi<PERSON>i khác nhau", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON><PERSON> dùng có quyền xem danh sách", "steps": "Bước 1: <PERSON><PERSON><PERSON>h<PERSON> bằng tài kho<PERSON>n có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt trên các độ phân giải: 1920x1080, 1366x768, 375x667\nBước 3: <PERSON><PERSON> s<PERSON>t giao diện hiển thị", "test_data": "<PERSON><PERSON><PERSON>: user_approve", "expected_result": "Bước 3: <PERSON><PERSON> s<PERSON>ch hiển thị đầy đủ, kh<PERSON><PERSON> bị vỡ layout trên mọi độ phân gi<PERSON>i", "test_case_type": "UI"}, {"_id": "68d25ed1841e3bd82603aaec", "test_case_id": "UI_003", "test_case_name": "<PERSON><PERSON><PERSON> tra hiển thị danh sách trên các trình duyệt phổ biến", "priority": "Low", "pre_condition": "<PERSON><PERSON><PERSON><PERSON> dùng có quyền xem danh sách", "steps": "Bước 1: <PERSON><PERSON><PERSON> <PERSON>hập bằng tài kho<PERSON>n có quyền trên <PERSON>, Firefox, Edge\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt trên từng trình duyệt", "test_data": "<PERSON><PERSON><PERSON>: user_approve", "expected_result": "Bước 2: <PERSON><PERSON> s<PERSON>ch hiển thị đúng trên mọi trình du<PERSON>", "test_case_type": "Compatibility"}]}, {"_id": "68d25d74b5e186666c9cb1c6", "name": "<PERSON><PERSON><PERSON> thị thông tin chi tiết người phê duyệt", "rationale": "<PERSON><PERSON><PERSON> tra các thông tin như tên, ph<PERSON><PERSON> ban, chứ<PERSON> vụ của người phê duyệt đư<PERSON><PERSON> hiển thị đầy đủ.", "test_cases": [{"_id": "68d25ed1841e3bd82603aaf0", "test_case_id": "UI_004", "test_case_name": "<PERSON><PERSON><PERSON> thị đầy đủ thông tin chi tiết người phê duyệt", "priority": "High", "pre_condition": "Người dùng có quyền xem danh sách\nCó dữ liệu người phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt\nBước 3: <PERSON><PERSON><PERSON> tra các trường thông tin: tê<PERSON>, ph<PERSON><PERSON> ban, chứ<PERSON> vụ", "test_data": "<PERSON><PERSON><PERSON>: user_approve", "expected_result": "Bước 3: <PERSON><PERSON><PERSON> cả trường thông tin hiển thị đầy đủ, đ<PERSON>g định dạng", "test_case_type": "UI"}, {"_id": "68d25ed1841e3bd82603aaf3", "test_case_id": "UI_005", "test_case_name": "<PERSON><PERSON><PERSON> tra hiển thị thông tin người phê duyệt có tên dài", "priority": "Medium", "pre_condition": "Người dùng có quyền xem danh sách\nCó người phê duyệt với tên dài (trên 50 ký tự)", "steps": "Bước 1: <PERSON><PERSON><PERSON>hập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt\nBước 3: <PERSON><PERSON><PERSON> tra hiển thị tên dài trên giao diện", "test_data": "<PERSON><PERSON><PERSON> ng<PERSON><PERSON>i phê duyệt: <PERSON><PERSON><PERSON><PERSON>ăn A B C D E F G H I J K L M N O P Q R S T U V W X Y Z", "expected_result": "Bước 3: <PERSON><PERSON><PERSON> hiển thị đầy đủ hoặc có dấu '...' h<PERSON><PERSON> lý, không vỡ layout", "test_case_type": "Boundary"}]}]}, {"_id": "68d25d74b5e186666c9cb1c8", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb1ca", "name": "<PERSON><PERSON>ng chọn chi nh<PERSON>h/phòng ban thay đổi danh sách", "rationale": "<PERSON><PERSON><PERSON> bảo khi thay đổi chi nhánh hoặc phòng ban, danh sách người phê duyệt đư<PERSON><PERSON> cập nhật đúng.", "test_cases": [{"_id": "68d25ed1841e3bd82603aaf8", "test_case_id": "Flow_001", "test_case_name": "<PERSON>hay đổi chi nh<PERSON>h cập nhật danh sách người phê duyệt", "priority": "High", "pre_condition": "Người dùng có quyền xem danh sách\nCó nhiều chi nhánh với danh sách người phê duyệt khác nhau", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài kho<PERSON>n có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt\nBước 3: <PERSON><PERSON><PERSON> chi nh<PERSON>h A\nBước 4: <PERSON><PERSON> <PERSON>hận danh sách hiển thị\nBước 5: <PERSON><PERSON><PERSON> chi nh<PERSON>h B\nBước 6: So sánh danh sách hiển thị với dữ liệu hệ thống", "test_data": "Chi nhánh: A, B", "expected_result": "Bước 4: <PERSON><PERSON> sách người phê duyệt của chi nhánh A hiển thị đúng\nBước 6: <PERSON><PERSON> sách người phê duyệt của chi nhánh B hiển thị đúng", "test_case_type": "Positive"}, {"_id": "68d25ed1841e3bd82603aafb", "test_case_id": "Flow_002", "test_case_name": "<PERSON>hay đổi phòng ban cập nhật danh sách người phê duyệt", "priority": "Medium", "pre_condition": "Người dùng có quyền xem danh sách\nCó nhiều phòng ban với danh sách người phê duyệt khác nhau", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài kho<PERSON>n có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt\nBước 3: <PERSON><PERSON><PERSON> phòng ban Kế toán\nBước 4: <PERSON><PERSON> <PERSON>hận danh sách hiển thị\nBước 5: <PERSON><PERSON><PERSON> phòng ban Nhân sự\nBước 6: So sánh danh sách hiển thị với dữ liệu hệ thống", "test_data": "Phòng ban: <PERSON><PERSON> to<PERSON>, <PERSON><PERSON><PERSON> sự", "expected_result": "Bước 4: <PERSON><PERSON> sách người phê duyệt của phòng ban Kế toán hiển thị đúng\nBước 6: <PERSON><PERSON> sách người phê duyệt của phòng ban Nhân sự hiển thị đúng", "test_case_type": "Positive"}]}]}, {"_id": "68d25d74b5e186666c9cb1cc", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb1ce", "name": "<PERSON>ử lý lỗi khi không có người phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bả<PERSON> hệ thống thông báo phù hợp khi không có người phê duyệt nào đư<PERSON><PERSON> tìm thấy.", "test_cases": [{"_id": "68d25ed1841e3bd82603ab00", "test_case_id": "Exception_001", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> có người phê duyệt nào trong chi nhánh/phòng ban", "priority": "High", "pre_condition": "Người dùng có quyền xem danh sách\nChi nh<PERSON>h/phòng ban không có người phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt của chi nhánh/phòng ban không có người phê duyệt", "test_data": "Chi nhánh: C (không có người phê duyệt)", "expected_result": "Bước 2: <PERSON><PERSON> thống hiển thị thông báo '<PERSON>hông tìm thấy người phê duyệt nào'", "test_case_type": "Edge"}]}]}, {"_id": "68d25d74b5e186666c9cb1d0", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb1d2", "name": "<PERSON><PERSON><PERSON> nh<PERSON>t danh sách khi thay đổi dữ liệu người phê duyệt", "rationale": "<PERSON><PERSON><PERSON> tra danh sách người phê duyệt đư<PERSON><PERSON> cập nhật khi có thay đổi về quyền hoặc thông tin người dùng.", "test_cases": [{"_id": "68d25ed1841e3bd82603ab05", "test_case_id": "Impact_001", "test_case_name": "<PERSON><PERSON><PERSON> nh<PERSON>t danh sách khi thêm người phê duyệt mới", "priority": "High", "pre_condition": "Ngư<PERSON>i dùng có quyền xem danh sách\nQuản trị viên có thể thêm người phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> trị viên thêm người phê duyệt mới vào chi nhánh/phòng ban\nBước 4: Refresh lại trang danh sách", "test_data": "<PERSON><PERSON><PERSON><PERSON> phê duyệt mới: <PERSON><PERSON><PERSON><PERSON>", "expected_result": "Bước 4: <PERSON><PERSON><PERSON><PERSON> phê duyệt mới xuất hiện trong danh sách", "test_case_type": "Positive"}, {"_id": "68d25ed1841e3bd82603ab08", "test_case_id": "Impact_002", "test_case_name": "<PERSON><PERSON><PERSON> nh<PERSON>t danh sách khi xóa người phê duy<PERSON>t", "priority": "High", "pre_condition": "Người dùng có quyền xem danh sách\nQuản trị viên có thể xóa người phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt\nBước 3: <PERSON>u<PERSON>n trị viên xóa một người phê duyệt khỏi chi nhánh/phòng ban\nBước 4: <PERSON>fresh lại trang danh sách", "test_data": "<PERSON><PERSON><PERSON><PERSON> phê duyệt bị xóa: <PERSON><PERSON><PERSON><PERSON> Th<PERSON>", "expected_result": "Bước 4: <PERSON><PERSON><PERSON><PERSON> phê duyệt bị xóa không còn xuất hiện trong danh sách", "test_case_type": "Positive"}, {"_id": "68d25ed1841e3bd82603ab0b", "test_case_id": "Impact_003", "test_case_name": "<PERSON><PERSON><PERSON> nh<PERSON>t danh sách khi thay đổi thông tin người phê duyệt", "priority": "Medium", "pre_condition": "Người dùng có quyền xem danh sách\nQuản trị viên có thể chỉnh sửa thông tin người phê duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản có quyền\nBước 2: <PERSON><PERSON><PERSON> cập trang danh sách người phê duyệt\nBước 3: <PERSON><PERSON><PERSON><PERSON> trị viên thay đổi thông tin (t<PERSON><PERSON>, chứ<PERSON> vụ) của người phê duyệt\nBước 4: <PERSON>fresh lại trang danh sách", "test_data": "Ngư<PERSON>i phê duyệt: <PERSON><PERSON> (thay đổi chức vụ)", "expected_result": "Bước 4: <PERSON>h<PERSON><PERSON> tin mới của người phê duyệt đ<PERSON><PERSON><PERSON> cập nhật trên danh sách", "test_case_type": "Positive"}]}]}]}, {"_id": "68d25cb738ec9064b3b87b42", "name": "Gửi email thông báo phê du<PERSON>t", "description": "<PERSON><PERSON> thống tự động gửi email thông báo đến người duyệt khi có yêu cầu phê duyệt hoặc khi trạng thái tranh chấp thay đổi.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb1d5", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb1d7", "name": "Chỉ gửi email cho người có quyền nhận thông báo", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người duyệt hoặc người liên quan mới nhận đư<PERSON><PERSON> email thông báo.", "test_cases": [{"_id": "68d25f10841e3bd82603ab13", "test_case_id": "Permission_001", "test_case_name": "Chỉ người duyệt nhận được email khi có yêu cầu phê duyệt", "priority": "High", "pre_condition": "<PERSON>à<PERSON> khoản A có quyền duyệt\nTài khoản B không có quyền duyệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> <PERSON>hậ<PERSON> bằng tài khoản B\nBước 2: <PERSON><PERSON><PERSON> yêu cầu phê duyệt mới\nBước 3: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của tài khoản A và B", "test_data": "<PERSON><PERSON><PERSON> kho<PERSON>n <PERSON> (<EMAIL>), <PERSON><PERSON><PERSON> kho<PERSON>n <PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON> thống <PERSON>ử<PERSON> email thông báo đến tài khoản A\nBước 3: <PERSON><PERSON><PERSON> khoản B không nhận được email", "test_case_type": "Positive"}, {"_id": "68d25f10841e3bd82603ab16", "test_case_id": "Permission_002", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>i email cho người không liên quan", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> k<PERSON>n C không liên quan đến tranh chấp", "steps": "Bước 1: <PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON> chấ<PERSON> mới\nBước 2: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của tà<PERSON> k<PERSON>n C", "test_data": "<PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> không nhận đ<PERSON><PERSON><PERSON> b<PERSON><PERSON> <PERSON><PERSON> <PERSON> thông báo nào", "test_case_type": "Negative"}, {"_id": "68d25f10841e3bd82603ab19", "test_case_id": "Permission_003", "test_case_name": "Gửi email cho nhiều người duyệt nếu có nhiều người liên quan", "priority": "Medium", "pre_condition": "<PERSON><PERSON> nhiều tài khoản có quyền duyệt cùng liên quan đến tranh chấp", "steps": "Bước 1: <PERSON><PERSON><PERSON> y<PERSON><PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON><PERSON> tra hộp thư email của tất cả người duyệt liên quan", "test_data": "<PERSON><PERSON><PERSON>, <PERSON> (<EMAIL>, <EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> cả người duyệt liên quan đều nhận đư<PERSON><PERSON> email thông báo", "test_case_type": "Positive"}, {"_id": "68d25f10841e3bd82603ab1c", "test_case_id": "Permission_004", "test_case_name": "<PERSON><PERSON><PERSON> tra phân quyền khi thay đổi vai trò người dùng", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> k<PERSON>n F ban đầu không có quyền duyệt", "steps": "Bước 1: <PERSON><PERSON> duyệt cho tài kho<PERSON>n F\nBước 2: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới\nBước 3: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của tài kho<PERSON>n F", "test_data": "<PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 3: <PERSON><PERSON><PERSON> F nhận đư<PERSON><PERSON> email thông báo sau khi đượ<PERSON> gán quyền duyệt", "test_case_type": "Positive"}, {"_id": "68d25f10841e3bd82603ab1f", "test_case_id": "Permission_005", "test_case_name": "Edge: <PERSON><PERSON><PERSON> email khi người duyệt bị xóa khỏi hệ thống ngay trước khi gửi", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON>n <PERSON> có quyền du<PERSON>t", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON><PERSON> tài kho<PERSON>n G khỏi hệ thống ngay trước khi hệ thống gửi email\nBước 3: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của tài khoản G", "test_data": "<PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 3: <PERSON><PERSON><PERSON> G không nhận được email, hệ thống ghi nhận lỗi gửi email", "test_case_type": "Edge"}]}]}, {"_id": "68d25d74b5e186666c9cb1d9", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb1db", "name": "<PERSON><PERSON> đ<PERSON> chỉ email hợp lệ", "rationale": "<PERSON><PERSON><PERSON> bảo người nhận có địa chỉ email hợp lệ trước khi gửi thông báo.", "test_cases": [{"_id": "68d25f10841e3bd82603ab24", "test_case_id": "Precondition_001", "test_case_name": "<PERSON><PERSON><PERSON> email đến địa chỉ hợp lệ", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> có <PERSON> hợp lệ", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới với người duyệt là tài khoản H\nBước 2: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của tài khoản H", "test_data": "<PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> H <PERSON>hận đ<PERSON><PERSON><PERSON> email thông báo", "test_case_type": "Positive"}, {"_id": "68d25f10841e3bd82603ab27", "test_case_id": "Precondition_002", "test_case_name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> email đến địa chỉ email không hợp lệ", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON>n I có <PERSON> không hợp lệ", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới với người duyệt là tài khoản I\nBước 2: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của tài khoản I", "test_data": "<PERSON><PERSON><PERSON> (khong_hople@)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON>n I không nhận được email, hệ thống ghi nhận lỗi gửi email", "test_case_type": "Negative"}, {"_id": "68d25f10841e3bd82603ab2a", "test_case_id": "Precondition_003", "test_case_name": "Edge: <PERSON><PERSON><PERSON> chỉ email chứa ký tự đặc biệt", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> J có email chứa ký tự đặc biệt", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới với người duyệt là tài khoản J\nBước 2: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của tài khoản J", "test_data": "<PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> J nhận được email nếu hệ thống hỗ trợ, hoặc ghi nhận lỗi nếu không hỗ trợ", "test_case_type": "Boundary"}, {"_id": "68d25f10841e3bd82603ab2d", "test_case_id": "Precondition_004", "test_case_name": "Edge: <PERSON><PERSON><PERSON> chỉ email trống", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> K <PERSON>hông có email", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới với người duyệt là tài khoản K\nBước 2: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của tài khoản K", "test_data": "<PERSON><PERSON><PERSON> (email trống)", "expected_result": "Bước 2: <PERSON><PERSON> thống không gửi email, hiển thị thông báo lỗi", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab30", "test_case_id": "Precondition_005", "test_case_name": "Edge: <PERSON><PERSON><PERSON> chỉ email dài tối đa cho phép", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> L có email dài 254 ký tự", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới với người duyệt là tài khoản L\nBước 2: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của tài khoản L", "test_data": "<PERSON><PERSON><PERSON> (email 254 ký tự)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> L nhận được email nếu hợp lệ, hoặc hệ thống ghi nhận lỗi nếu vượt quá giới hạn", "test_case_type": "Boundary"}]}]}, {"_id": "68d25d74b5e186666c9cb1dd", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb1df", "name": "<PERSON><PERSON>n thị thông b<PERSON>o gửi email thành công/thất bại", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống hiển thị thông báo phù hợp khi gửi email thành công hoặc thất bại.", "test_cases": [{"_id": "68d25f10841e3bd82603ab35", "test_case_id": "UI_001", "test_case_name": "<PERSON><PERSON><PERSON> thị thông b<PERSON>o gửi email thành công", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> M có <PERSON> hợp lệ", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới với người duyệt là tài khoản M\nBước 2: <PERSON><PERSON> sát thông báo trên giao di<PERSON>n", "test_data": "<PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON> thống hiển thị thông bá<PERSON> '<PERSON><PERSON><PERSON> email thành công'", "test_case_type": "UI"}, {"_id": "68d25f10841e3bd82603ab38", "test_case_id": "UI_002", "test_case_name": "<PERSON><PERSON>n thị thông b<PERSON><PERSON>i email thất bại", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> có <PERSON> không hợp lệ", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới với người duyệt là tài khoản N\nBước 2: <PERSON><PERSON> sát thông báo trên giao di<PERSON>n", "test_data": "<PERSON><PERSON><PERSON> (ui_fail@)", "expected_result": "Bước 2: <PERSON><PERSON> thống hiển thị thông bá<PERSON> '<PERSON><PERSON><PERSON> email thất bại'", "test_case_type": "UI"}, {"_id": "68d25f10841e3bd82603ab3b", "test_case_id": "UI_003", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> báo hiển thị đúng vị trí, không che khu<PERSON>t nội dung", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hệ thống trên trình du<PERSON>t Chrome", "steps": "Bước 1: <PERSON><PERSON><PERSON> y<PERSON><PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON> sát vị trí thông báo trên giao di<PERSON>n", "test_data": "<PERSON><PERSON><PERSON><PERSON> dụng", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> bá<PERSON> hiển thị ở vị trí quy định, khô<PERSON> che khuất các thành phần khác", "test_case_type": "UI"}, {"_id": "68d25f10841e3bd82603ab3e", "test_case_id": "UI_004", "test_case_name": "<PERSON>h<PERSON><PERSON> báo hiển thị rõ ràng trên các độ phân giải khác nhau", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> cậ<PERSON> hệ thống trên các độ phân giải màn hình khác nhau", "steps": "Bước 1: <PERSON><PERSON><PERSON> y<PERSON><PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON> sát thông báo trên giao diện ở các độ phân giải", "test_data": "<PERSON><PERSON><PERSON><PERSON> dụng", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> bá<PERSON> hiển thị rõ ràng, kh<PERSON><PERSON> bị vỡ layout trên mọi độ phân giải", "test_case_type": "UI"}, {"_id": "68d25f10841e3bd82603ab41", "test_case_id": "UI_005", "test_case_name": "<PERSON><PERSON><PERSON><PERSON> báo hiển thị đúng trên các trình duyệt phổ biến", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> hệ thống trên <PERSON>, Firefox, Edge", "steps": "Bước 1: <PERSON><PERSON><PERSON> y<PERSON><PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON> sát thông báo trên giao diện ở các trình duyệt", "test_data": "<PERSON><PERSON><PERSON><PERSON> dụng", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> bá<PERSON> hiển thị đúng, khô<PERSON> bị lỗi font hoặc layout trên các trình du<PERSON>t", "test_case_type": "Compatibility"}]}]}, {"_id": "68d25d74b5e186666c9cb1e1", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb1e3", "name": "G<PERSON>i email khi có yêu cầu phê duyệt mới", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống gửi email khi có yêu cầu phê duyệt mới đư<PERSON><PERSON> tạo.", "test_cases": [{"_id": "68d25f10841e3bd82603ab46", "test_case_id": "Flow_001", "test_case_name": "Gửi email khi tạo yêu cầu phê duyệt mới", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON>n O có quyền duyệt và email hợp lệ", "steps": "Bước 1: <PERSON><PERSON><PERSON> nhập bằng tài khoản bấ<PERSON> kỳ\nBước 2: <PERSON><PERSON><PERSON> yêu cầu phê duyệt mới với người duyệt là tài khoản O\nBước 3: <PERSON><PERSON><PERSON> tra hộp thư email của tài khoản O", "test_data": "<PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 3: <PERSON><PERSON><PERSON> O <PERSON>hận đư<PERSON><PERSON> email thông báo về yêu cầu phê duyệt mới", "test_case_type": "Positive"}, {"_id": "68d25f10841e3bd82603ab49", "test_case_id": "Flow_002", "test_case_name": "<PERSON><PERSON><PERSON>ng g<PERSON>i email khi không có người duy<PERSON>t", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON><PERSON> có người duyệt được chỉ định", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới không chỉ định người duyệt\nBước 2: <PERSON><PERSON><PERSON> tra hộp thư email của các tài khoản liên quan", "test_data": "<PERSON><PERSON><PERSON><PERSON> dụng", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> có <PERSON> nào đ<PERSON><PERSON><PERSON> gửi đi", "test_case_type": "Negative"}, {"_id": "68d25f10841e3bd82603ab4c", "test_case_id": "Flow_003", "test_case_name": "Edge: <PERSON><PERSON><PERSON> yêu cầu phê duyệt liên tiếp", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> P có quyền duyệt và email hợp lệ", "steps": "Bước 1: <PERSON><PERSON><PERSON> 10 yêu cầu phê duyệt mới liên tiếp với người duyệt là tài khoản P\nBước 2: <PERSON><PERSON><PERSON> tra hộp thư email của tài khoản P", "test_data": "<PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> P nhận được 10 email thông báo tương <PERSON>ng", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab4f", "test_case_id": "Flow_004", "test_case_name": "Edge: <PERSON><PERSON><PERSON> yêu cầu phê duyệt khi hệ thống đang bảo trì", "priority": "Medium", "pre_condition": "<PERSON><PERSON> thống đang ở chế độ bảo trì", "steps": "Bước 1: <PERSON><PERSON><PERSON> y<PERSON><PERSON> cầ<PERSON> phê duyệt mới\nBước 2: <PERSON><PERSON><PERSON> tra hộp thư email của người duyệt", "test_data": "<PERSON><PERSON><PERSON><PERSON> dụng", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> có <PERSON> nào đ<PERSON><PERSON><PERSON> gửi đi, h<PERSON> thống hiển thị thông báo bảo trì", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab52", "test_case_id": "Flow_005", "test_case_name": "Edge: <PERSON><PERSON><PERSON> yêu cầu phê duyệt khi mạng chậm", "priority": "Medium", "pre_condition": "Mạng internet bị giới hạn băng thông", "steps": "Bước 1: <PERSON><PERSON><PERSON><PERSON> hạn băng thông mạng\nBước 2: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới\nBước 3: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của người duyệt sau một khoảng thời gian", "test_data": "<PERSON><PERSON><PERSON><PERSON> dụng", "expected_result": "Bước 3: <PERSON><PERSON> có thể gửi chậm nhưng vẫn phải được gửi thành công hoặc hệ thống thông báo lỗi rõ ràng", "test_case_type": "Edge"}]}, {"_id": "68d25d74b5e186666c9cb1e5", "name": "Gửi email khi trạng thái tranh chấp thay đổi", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống gửi email khi trạng thái tranh chấp đ<PERSON><PERSON><PERSON> cập nhật.", "test_cases": [{"_id": "68d25f10841e3bd82603ab56", "test_case_id": "Status_001", "test_case_name": "<PERSON><PERSON>i email khi cập nhật trạng thái tranh chấp", "priority": "High", "pre_condition": "<PERSON><PERSON>n tại tranh chấp với ngư<PERSON>i du<PERSON> có <PERSON> hợp lệ", "steps": "Bước 1: <PERSON><PERSON> đổ<PERSON> trạng thái tranh chấp\n<PERSON> 2: <PERSON><PERSON><PERSON> tra hộp thư email củ<PERSON> ngư<PERSON>i du<PERSON>t", "test_data": "<PERSON><PERSON><PERSON> chấp #123, <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> du<PERSON>t nhận đư<PERSON><PERSON> email thông báo về thay đổi trạng thái", "test_case_type": "Positive"}, {"_id": "68d25f10841e3bd82603ab59", "test_case_id": "Status_002", "test_case_name": "<PERSON><PERSON><PERSON>ng gửi email khi trạng thái không thay đổi", "priority": "Medium", "pre_condition": "<PERSON><PERSON>n tại tranh chấp với ngư<PERSON>i du<PERSON> có <PERSON> hợp lệ", "steps": "Bước 1: <PERSON><PERSON><PERSON> tranh chấp mà không thay đổi trạng thái\nBước 2: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email củ<PERSON> ng<PERSON><PERSON><PERSON> du<PERSON>", "test_data": "<PERSON><PERSON><PERSON> chấp #124, <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> có <PERSON> nào đ<PERSON><PERSON><PERSON> gửi đi", "test_case_type": "Negative"}, {"_id": "68d25f10841e3bd82603ab5c", "test_case_id": "Status_003", "test_case_name": "Edge: <PERSON><PERSON> đổi trạng thái liên tục trong thời gian ng<PERSON>n", "priority": "Medium", "pre_condition": "<PERSON><PERSON>n tại tranh chấp với ngư<PERSON>i du<PERSON> có <PERSON> hợp lệ", "steps": "Bước 1: <PERSON><PERSON> đổi trạng thái tranh chấp 5 lần liên tiế<PERSON> trong 1 phút\nBước 2: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email của người duy<PERSON>t", "test_data": "<PERSON><PERSON><PERSON> chấp #125, <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> nhận đư<PERSON><PERSON> 5 email thông báo tương <PERSON>ng", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab5f", "test_case_id": "Status_004", "test_case_name": "Edge: <PERSON><PERSON> đổi trạng thái khi người duyệt đã bị khóa tài khoản", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON><PERSON> bị kh<PERSON>a tài k<PERSON>n", "steps": "Bước 1: <PERSON><PERSON> đổ<PERSON> trạng thái tranh chấp\n<PERSON> 2: <PERSON><PERSON><PERSON> tra hộp thư email củ<PERSON> ngư<PERSON>i du<PERSON>t", "test_data": "<PERSON><PERSON><PERSON> chấp #126, <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON><PERSON> có email nào đư<PERSON><PERSON> gửi đi, h<PERSON> thống ghi nhận lỗi gửi email", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab62", "test_case_id": "Status_005", "test_case_name": "Edge: <PERSON>hay đổi trạng thái khi email người duyệt bị thay đổi ngay trướ<PERSON> khi gửi", "priority": "Medium", "pre_condition": "Ngư<PERSON><PERSON> duy<PERSON> thay đổi email ngay tr<PERSON><PERSON><PERSON> khi thay đổi trạng thái", "steps": "Bước 1: <PERSON><PERSON><PERSON> email người duyệt\nBước 2: Thay đổi trạng thái tranh chấp\nBước 3: <PERSON><PERSON><PERSON> tra hộ<PERSON> thư email cũ và mới của người duyệt", "test_data": "<PERSON><PERSON><PERSON> chấp #127, email cũ (<EMAIL>), email mới (<EMAIL>)", "expected_result": "Bước 3: <PERSON><PERSON> chỉ gửi đến địa chỉ email mới, không gửi đến email cũ", "test_case_type": "Edge"}]}]}, {"_id": "68d25d74b5e186666c9cb1e7", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb1e9", "name": "<PERSON>ử lý lỗi gửi email thất bại", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống thông báo lỗi khi gửi email không thành công.", "test_cases": [{"_id": "68d25f10841e3bd82603ab67", "test_case_id": "Exception_001", "test_case_name": "Hiển thị thông báo lỗi khi gửi email thất bại do lỗi SMTP", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> h<PERSON>nh SMTP sai", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON> sát thông báo lỗi trên giao di<PERSON>n", "test_data": "SMTP server sai", "expected_result": "Bước 2: <PERSON><PERSON> thống hiển thị thông báo lỗi gửi email thất bại", "test_case_type": "Negative"}, {"_id": "68d25f10841e3bd82603ab6a", "test_case_id": "Exception_002", "test_case_name": "Edge: <PERSON><PERSON><PERSON> email thất bại do hết quota gửi email", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON><PERSON> email c<PERSON><PERSON> hệ thống đã hết", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON> sát thông báo lỗi trên giao di<PERSON>n", "test_data": "Quota gửi email = 0", "expected_result": "Bước 2: <PERSON><PERSON> thống hiển thị thông báo lỗi gửi email thất bại", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab6d", "test_case_id": "Exception_003", "test_case_name": "Edge: <PERSON><PERSON><PERSON> email thất b<PERSON>i do timeout", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON><PERSON>i SMTP bị timeout", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON> sát thông báo lỗi trên giao di<PERSON>n", "test_data": "Timeout SMTP", "expected_result": "Bước 2: <PERSON><PERSON> thống hiển thị thông báo lỗi gửi email thất bại", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab70", "test_case_id": "Exception_004", "test_case_name": "Edge: <PERSON><PERSON><PERSON> email thất bại do lỗi DNS", "priority": "Medium", "pre_condition": "DNS không phân gi<PERSON>i đư<PERSON><PERSON> tên miền SMTP", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON> sát thông báo lỗi trên giao di<PERSON>n", "test_data": "Lỗi DNS", "expected_result": "Bước 2: <PERSON><PERSON> thống hiển thị thông báo lỗi gửi email thất bại", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab73", "test_case_id": "Exception_005", "test_case_name": "Edge: <PERSON><PERSON><PERSON> email thất bại do hộp thư người nhận đầy", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> thư người nhận đã đầy", "steps": "Bước 1: <PERSON><PERSON><PERSON> yê<PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON> sát thông báo lỗi trên giao di<PERSON>n", "test_data": "<PERSON><PERSON><PERSON> thư <PERSON>", "expected_result": "Bước 2: <PERSON><PERSON> thống hiển thị thông báo lỗi gửi email thất bại", "test_case_type": "Edge"}]}]}, {"_id": "68d25d74b5e186666c9cb1eb", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb1ed", "name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>n lịch sử gửi email", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống lưu lại lịch sử gửi email thông báo cho từng tranh chấp.", "test_cases": [{"_id": "68d25f10841e3bd82603ab78", "test_case_id": "History_001", "test_case_name": "<PERSON><PERSON><PERSON> l<PERSON>ch sử gửi email thành công", "priority": "High", "pre_condition": "<PERSON><PERSON>n tại tranh chấp với ngư<PERSON>i du<PERSON> có <PERSON> hợp lệ", "steps": "Bước 1: <PERSON><PERSON><PERSON> y<PERSON><PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON><PERSON> tra lịch sử gửi email của tranh chấp", "test_data": "<PERSON><PERSON><PERSON> chấ<PERSON> #200, <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> sử gửi email ghi nhận thành công với thông tin thời gian, ng<PERSON><PERSON><PERSON>, tr<PERSON><PERSON> thái", "test_case_type": "Positive"}, {"_id": "68d25f10841e3bd82603ab7b", "test_case_id": "History_002", "test_case_name": "<PERSON><PERSON><PERSON> l<PERSON>ch sử gửi email thất bại", "priority": "High", "pre_condition": "<PERSON><PERSON><PERSON> h<PERSON>nh SMTP sai", "steps": "Bước 1: <PERSON><PERSON><PERSON> y<PERSON><PERSON> cầu phê duyệt mới\nBước 2: <PERSON><PERSON><PERSON> tra lịch sử gửi email của tranh chấp", "test_data": "<PERSON><PERSON><PERSON> chấ<PERSON> #201, <PERSON><PERSON><PERSON><PERSON> (<EMAIL>)", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> sử gửi email ghi nhận thất bại với thông tin thời gian, ng<PERSON><PERSON><PERSON> nh<PERSON>n, tr<PERSON><PERSON> thái lỗi", "test_case_type": "Negative"}, {"_id": "68d25f10841e3bd82603ab7e", "test_case_id": "History_003", "test_case_name": "Edge: <PERSON><PERSON><PERSON> sử gửi email với số lượng lớn", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON> chấp đã g<PERSON><PERSON> hơn 100 email thông báo", "steps": "Bước 1: <PERSON><PERSON><PERSON> tra lịch sử gửi email củ<PERSON> tranh chấp\nBước 2: <PERSON><PERSON><PERSON> <PERSON>hận hiển thị đầy đủ, kh<PERSON><PERSON> bị lỗi hoặc mất dữ liệu", "test_data": "<PERSON><PERSON><PERSON> chấp #202", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> sử gửi email hiển thị đầy đủ, kh<PERSON><PERSON> bị lỗi hoặc mất dữ liệu", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab81", "test_case_id": "History_004", "test_case_name": "Edge: <PERSON><PERSON><PERSON> s<PERSON>ửi email khi người nhận bị xóa khỏi hệ thống", "priority": "Medium", "pre_condition": "<PERSON><PERSON><PERSON>i nhận đã bị xóa khỏi hệ thống", "steps": "Bước 1: <PERSON><PERSON><PERSON> tra lịch sử gửi email c<PERSON><PERSON> tranh chấp\nBước 2: <PERSON><PERSON><PERSON> nhận thông tin người nhận vẫn được lưu trữ trong lịch sử", "test_data": "<PERSON><PERSON><PERSON> chấp #203", "expected_result": "Bước 2: <PERSON><PERSON><PERSON> sử gửi email vẫn lưu thông tin người nhận đã bị xóa", "test_case_type": "Edge"}, {"_id": "68d25f10841e3bd82603ab84", "test_case_id": "History_005", "test_case_name": "Edge: <PERSON><PERSON><PERSON> s<PERSON> gửi email khi refresh trang gi<PERSON><PERSON> chừng", "priority": "Medium", "pre_condition": "<PERSON><PERSON> xem lịch sử gửi email", "steps": "Bước 1: Refresh trang khi đang xem lịch sử gửi email\nBước 2: <PERSON><PERSON><PERSON> nhận dữ liệu vẫn hiển thị đúng sau khi refresh", "test_data": "<PERSON><PERSON><PERSON> chấp #204", "expected_result": "Bước 2: <PERSON><PERSON> liệu lịch sử gửi email vẫn hiển thị đúng, kh<PERSON><PERSON> bị mất hoặc lỗi", "test_case_type": "Edge"}]}]}]}]}