// Simple role-based permission middleware
// Admin has full access to everything
// Users can only access their own data

// Helper function to check if user is admin
const isAdmin = (user) => {
  return user && user.role === 'admin';
};

// Helper function to check if user is viewer or higher
const isViewerOrHigher = (user) => {
  return user && ['viewer', 'tester', 'leader', 'admin'].includes(user.role);
};

// Helper function to check if user can access their own data
const canAccessOwnData = (req) => {
  return req.params.id && req.params.id === req.user._id.toString();
};

// Base permission check function
const checkPermission = (req, res, next, customMessage = 'Access denied') => {
  if (!req.user) {
    return res.status(401).json({
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  // Admin has full access
  if (isAdmin(req.user)) {
    return next();
  }

  // For other roles, check if they can access their own data
  if (canAccessOwnData(req)) {
    return next();
  }

  return res.status(403).json({
    message: customMessage,
    code: 'INSUFFICIENT_PERMISSIONS'
  });
};

// Middleware to check if user has specific permission
const requirePermission = (resource, action) => {
  return (req, res, next) => {
    const message = `Access denied. Required permission: ${action} ${resource}`;
    return checkPermission(req, res, next, message);
  };
};

// Middleware to check if user has any of the specified permissions
const requireAnyPermission = (permissionChecks) => {
  return (req, res, next) => {
    return checkPermission(req, res, next, 'Access denied. Insufficient permissions');
  };
};

// Middleware to check if user has all of the specified permissions
const requireAllPermissions = (permissionChecks) => {
  return (req, res, next) => {
    return checkPermission(req, res, next, 'Access denied. Insufficient permissions');
  };
};

// Middleware to check if user is admin
const requireAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  if (!isAdmin(req.user)) {
    return res.status(403).json({
      message: 'Admin access required',
      code: 'ADMIN_REQUIRED'
    });
  }

  next();
};

// Middleware to check if user is admin (same as requireAdmin for simplicity)
const requireAdminOrModerator = requireAdmin;

// Middleware to check if user can access their own data or is admin
const requireOwnDataOrAdmin = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  // Admin has full access
  if (isAdmin(req.user)) {
    return next();
  }

  // Check if user is accessing their own data
  const requestedId = req.params.id;
  const currentUserId = req.user._id.toString();
  
  // Validate that the requested ID matches the current user's ID
  if (!requestedId || requestedId === 'undefined' || requestedId === 'null') {
    return res.status(400).json({
      message: 'Invalid user ID',
      code: 'INVALID_USER_ID'
    });
  }

  // Strict comparison to prevent ID manipulation
  if (requestedId === currentUserId) {
    return next();
  }

  // Log potential security breach attempt
  console.warn(`Security Alert: User ${currentUserId} attempted to access data for user ${requestedId}`);
  
  return res.status(403).json({
    message: 'Access denied. You can only access your own data',
    code: 'INSUFFICIENT_PERMISSIONS'
  });
};

// Middleware to check if user is viewer or higher
const requireViewerOrHigher = (req, res, next) => {
  if (!req.user) {
    return res.status(401).json({
      message: 'Authentication required',
      code: 'AUTH_REQUIRED'
    });
  }

  if (!isViewerOrHigher(req.user)) {
    return res.status(403).json({
      message: 'Viewer access or higher required',
      code: 'INSUFFICIENT_PERMISSIONS'
    });
  }

  next();
};

module.exports = {
  requirePermission,
  requireAnyPermission,
  requireAllPermissions,
  requireAdmin,
  requireAdminOrModerator,
  requireOwnDataOrAdmin,
  requireViewerOrHigher
};
