const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');
const { Schema } = mongoose;

const TestCaseHistorySchema = new Schema({
  test_case_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'test_case', 
    required: true 
  },
  suite_id: { 
    type: Schema.Types.ObjectId, 
    ref: 'suite', 
    required: true 
  },
  test_case_version_id: {
    type: Schema.Types.ObjectId, 
    ref: 'test_case_version', 
    default: null 
  },
  edit_reason: { 
    type: String, 
    required: true, 
    trim: true 
  },
  affected_field: {
    type: String,
    trim: true
  },
  type: {
    type: Number,
    default: 2
  },
  modified_at: {
    type: Date,
    default: Date.now
  },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

TestCaseHistorySchema.plugin(softDeletePlugin);

TestCaseHistorySchema.index({ test_case_id: 1, createdAt: -1 });

module.exports = mongoose.model('test_case_history', TestCaseHistorySchema); 
