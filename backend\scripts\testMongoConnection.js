const mongoose = require('mongoose');

async function testMongoConnection() {
  try {
    console.log('🔌 Attempting to connect to MongoDB...');
    
    // Try local MongoDB first
    const uri = 'mongodb://localhost:27017/gentest';
    
    await mongoose.connect(uri, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('✅ Connected to MongoDB successfully!');
    
    // Test basic operations
    const User = require('../models/user');
    
    // Count users
    const count = await User.countDocuments({});
    console.log(`📊 Total users: ${count}`);
    
    // Get first 3 users
    const users = await User.find({}).limit(3);
    console.log('📋 Sample users:');
    users.forEach((user, index) => {
      console.log(`  ${index + 1}. ${user.name} (${user.email}) - ${user.role}`);
    });
    
  } catch (error) {
    console.error('❌ MongoDB connection failed:', error.message);
    
    if (error.message.includes('ECONNREFUSED')) {
      console.log('💡 Suggestion: Make sure MongoDB is running on localhost:27017');
      console.log('   You can start it with: mongod');
    }
  } finally {
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('🔌 Connection closed');
    }
  }
}

testMongoConnection();
