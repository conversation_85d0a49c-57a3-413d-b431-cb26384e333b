const express = require('express');
const router = express.Router();
const mindmapController = require('../controllers/mindmapController');
const authMiddleware = require('../middleware/auth');
const fileService = require('../services/core/fileService');

router.post('/feature/:suite_id?', authMiddleware, ...fileService.uploadFilesForExtractMindmap, mindmapController.createFeature);
router.post('/mindmap-with-testcases/:suite_id?', authMiddleware, ...fileService.uploadFilesForExtractMindmap, mindmapController.createMindmapWithTestCases);
router.post('/checklist', authMiddleware, mindmapController.createChecklist);
router.post('/test-case', authMiddleware, mindmapController.createTestCase);
router.get('/get-mindmap', authMiddleware, mindmapController.getMindmap);
router.post('/review-test-case', authMiddleware, mindmapController.reviewTestCase);
router.post('/gen-auto-test', authMiddleware, mindmapController.genAutoTest);
router.get('/get-automation-scripts', authMiddleware, mindmapController.getAutomationScripts);
router.post('/update-mindmap', authMiddleware, mindmapController.updateMindmap);
router.get('/list-feature', authMiddleware, mindmapController.listFeature);
router.get('/list-checklist', authMiddleware, mindmapController.listChecklist);

module.exports = router; 