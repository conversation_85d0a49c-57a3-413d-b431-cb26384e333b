const AuthService = require('../services/core/authService');
const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const config = require('../config');

class JWTAuthAdapter extends AuthService {
  async hashPassword(password) {
    return bcrypt.hash(password, 10);
  }
  async comparePassword(password, hash) {
    return bcrypt.compare(password, hash);
  }
  async generateToken(payload) {
    return jwt.sign(payload, config.jwtSecret, { expiresIn: config.jwtExpiresIn });
  }
  async generateRefreshToken(payload) {
    return jwt.sign(payload, config.jwtSecret, { expiresIn: config.jwtRefreshExpiresIn });
  }
  async verifyToken(token) {
    try {
      return jwt.verify(token, config.jwtSecret);
    } catch (error) {
      if (error.name === 'TokenExpiredError') {
        throw new Error('Token expired');
      }
      if (error.name === 'JsonWebTokenError') {
        throw new Error('Invalid token');
      }
      throw error;
    }
  }
  async decodeToken(token) {
    return jwt.decode(token);
  }
  isTokenExpired(token) {
    try {
      const decoded = jwt.decode(token);
      if (!decoded || !decoded.exp) return true;
      return decoded.exp * 1000 < Date.now();
    } catch (error) {
      return true;
    }
  }
  async getUserLogin(token) {
    const decoded = await this.verifyToken(token);
    return decoded;
  }
}
module.exports = new JWTAuthAdapter();
