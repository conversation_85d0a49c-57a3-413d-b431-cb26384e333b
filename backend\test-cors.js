// Simple CORS test script
const axios = require('axios');

async function testCORS() {
  const testUrls = [
    'http://localhost:3000',
    'http://localhost:5173',
    'http://127.0.0.1:3000',
    'http://127.0.0.1:5173'
  ];

  console.log('Testing CORS configuration...\n');

  for (const url of testUrls) {
    try {
      console.log(`Testing origin: ${url}`);
      
      const response = await axios.get('http://localhost:3000/users', {
        headers: {
          'Origin': url
        }
      });
      
      console.log(`✅ CORS allowed for ${url}`);
      console.log(`   Response status: ${response.status}`);
      console.log(`   Access-Control-Allow-Origin: ${response.headers['access-control-allow-origin']}\n`);
      
    } catch (error) {
      if (error.response) {
        console.log(`❌ CORS blocked for ${url}`);
        console.log(`   Status: ${error.response.status}`);
        console.log(`   Error: ${error.response.data}\n`);
      } else {
        console.log(`❌ Network error for ${url}: ${error.message}\n`);
      }
    }
  }
}

// Only run if this file is executed directly
if (require.main === module) {
  testCORS().catch(console.error);
}

module.exports = { testCORS }; 