const BaseRepository = require('./baseRepository');
const TestCaseRegenerate = require('../models/testCaseRegenerate');

class TestCaseRegenerateRepository extends BaseRepository {
  constructor() {
    super(TestCaseRegenerate);
  }
  
  async getLatestRegeneration(testCaseId) {
    return await this.model.getLatestRegeneration(testCaseId);
  }
  
  async getRegenerationHistory(testCaseId) {
    return await this.model.getRegenerationHistory(testCaseId);
  }
  
  async findByTestCaseId(testCaseId) {
    return await this.findAll({ test_case_id: testCaseId });
  }
  
  async findBySuiteId(suiteId) {
    return await this.findAll({ suite_id: suiteId });
  }
}

module.exports = new TestCaseRegenerateRepository(); 