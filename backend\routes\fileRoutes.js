const express = require('express');
const router = express.Router();
const fileController = require('../controllers/fileController');
const authMiddleware = require('../middleware/auth');

// All routes require authentication
router.use(authMiddleware);

// Stream image for preview (requires JWT)
router.get('/images/:suiteId/:filename', fileController.streamImage);

// Get file metadata (requires JWT)
router.get('/info/:suiteId/:filename', fileController.getFileInfo);

module.exports = router;

