const mongoose = require('mongoose');
const softDelete = require('../plugins/softDeletePlugin');
const config = require('../config');

const suiteSchema = new mongoose.Schema({
  project_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'project',
    required: true
  },
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user',
    required: false
  },
  final_approver_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user',
    required: false
  },
  field_type: {
    type: String,
      enum: config.suite.field_type,
      required: false
  },
  objective: {
    type: String,
    enum: config.suite.objective,
    required: false
  },
  feature_name: {
    type: String,
    required: false,
    trim: true
  },
  approval_status: {
    type: String,
    required: true,
    enum: config.suite.approval_status,
    default: 'draft'
  },
  submit_approval_time: {
    type: Date,
    default: null
  },
  submit_approval: {
    approver_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'user',
      default: null
    },
    notes: {
      type: String,
      trim: true
    },
    sender_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'user',
      default: null
    }
  },
  rejected_notes: {
    type: String,
    trim: true,
    default: null
  },
  approved_notes: {
    type: String,
    trim: true,
    default: null
  },
  prompt: {
    input: String,
    output: Object
  },
  previous_response_id: {
    type: String,
    default: null
  },
  mindmap: {
    type: Object,
    default: []
  },
  document: {
    type: Object,
    default: {
      original_name: String,
      file_name: String,
      path: String
    }
  },
  figma_url: {
    type: String,
    default: null,
    trim: true
  },
  documents: {
    main: [{
      original_name: String,
      file_name: String,
      path: String,
      mime_type: String,
      size: Number
    }],
    sub: [{
      original_name: String,
      file_name: String,
      path: String,
      mime_type: String,
      size: Number
    }]
  },
  images: [{
    original_name: String,
    file_name: String,
    path: String,
    size: Number
  }],
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

suiteSchema.plugin(softDelete);


const Suite = mongoose.model('suite', suiteSchema);

module.exports = Suite; 