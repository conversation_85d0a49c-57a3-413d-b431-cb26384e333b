const mongoose = require('mongoose');
const BaseRepository = require('./baseRepository');
const UserProject = require('../models/userProject');

class userProjectRepository extends BaseRepository {
  constructor() {
    super(UserProject);
  }

  async findUsersByProjectId(projectId) {
    if (!mongoose.Types.ObjectId.isValid(projectId)) {
      return [];
    }

    return this.model.aggregate([
      {
        $match: {
          project_id: new mongoose.Types.ObjectId(projectId),
          del_flag: 0,
          status: 'active'
        }
      },
      {
        $lookup: {
          from: 'users',
          localField: 'user_id',
          foreignField: '_id',
          as: 'user'
        }
      },
      {
        $unwind: '$user'
      },
      {
        $project: {
          _id: 1,
          project_id: 1,
          user_id: 1,
          project_role: 1,
          status: 1,
          createdAt: 1,
          updatedAt: 1,
          'user._id': 1,
          'user.name': 1,
          'user.email': 1,
          'user.role': 1,
          'user.status': 1,
          'user.avatar': 1,
          'user.lastLogin': 1
        }
      },
      {
        $sort: {
          createdAt: 1
        }
      }
    ]);
  }
}

module.exports = new userProjectRepository(); 