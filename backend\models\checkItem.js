const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');

const { Schema } = mongoose;

const CheckItemSchema = new Schema({
  suite_id: { type: Schema.Types.ObjectId, ref: 'suite', required: true },
  feature_id: { type: Schema.Types.ObjectId, ref: 'feature', required: true },
  parent_id: { type: Schema.Types.ObjectId, ref: 'check_item', default: null }, // null = category
  type: { type: String, enum: ['category', 'checklist'], required: true },
  name: { type: String, required: true, trim: true },
  rationale: { type: String, default: '' },
  order: { type: Number, default: 0 },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

CheckItemSchema.plugin(softDeletePlugin);

CheckItemSchema.index({ suite_id: 1, feature_id: 1, parent_id: 1, order: 1 });
CheckItemSchema.index({ suite_id: 1, parent_id: 1, name: 1 });

module.exports = mongoose.model('check_item', CheckItemSchema);


