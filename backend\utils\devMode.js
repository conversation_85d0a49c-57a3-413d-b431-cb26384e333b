const fs = require('fs');
const path = require('path');

/**
 * Kiểm tra xem có đang ở chế độ DEV_MODE không
 * @returns {boolean}
 */
function isMindmapDevMode() {
  return process.env.MINDMAP_DEV_MODE === 'true';
}

/**
 * Đọc fake data từ file JSON
 * @param {string} filename - Tên file (không cần extension .json)
 * @returns {Object|null} - Fake data object hoặc null nếu có lỗi
 */
function readFakeData(filename) {
  try {
    const filePath = path.join(__dirname, '../data/dev-mode', `${filename}.json`);
    
    if (!fs.existsSync(filePath)) {
      console.error(`Fake data file not found: ${filePath}`);
      return null;
    }
    
    const rawData = fs.readFileSync(filePath, 'utf8');
    const jsonData = JSON.parse(rawData);
    
    console.log(`[DEV_MODE] Loaded fake data from: ${filename}.json`);
    return jsonData;
  } catch (error) {
    console.error(`Error reading fake data from ${filename}.json:`, error.message);
    return null;
  }
}

/**
 * Simulate async delay (để giống như call API thật)
 * @param {number} ms - Milliseconds to delay (default: 1000)
 */
function simulateDelay(ms = 1000) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * Wrapper function cho DEV_MODE responses
 * @param {string} filename - Tên file fake data
 * @param {number} delay - Delay time in ms (default: 500)
 * @returns {Promise<Object|null>}
 */
async function getFakeResponse(filename, delay = 500) {
  if (!isMindmapDevMode()) {
    return null;
  }
  
  // Simulate API call delay
  await simulateDelay(delay);
  
  return readFakeData(filename);
}

module.exports = {
  isMindmapDevMode,
  readFakeData,
  simulateDelay,
  getFakeResponse
};
