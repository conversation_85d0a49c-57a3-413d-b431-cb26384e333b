const fs = require("fs");
const path = require("path");
const axios = require("axios");
const logger = require("./loggerService");


const FIGMA_API_URL = process.env.FIGMA_API_URL;
const FIGMA_TOKEN = process.env.FIGMA_TOKEN;

function getFileKeyFromUrl(figmaUrl) {
  const match = figmaUrl.match(/figma\.com\/design\/([a-zA-Z0-9]+)\//);
  if (!match) throw new Error("Không tìm thấy FILE_KEY trong link Figma");
  return match[1];
}

// Helper download
async function downloadImage(url, outputPath) {
  const response = await axios.get(url, { responseType: "arraybuffer" });
  fs.writeFileSync(outputPath, response.data);
  console.log("Downloaded:", outputPath);
}

async function getDocument(fileKey) {
  try {
    const res = await axios.get(`${FIGMA_API_URL}/files/${fileKey}`, {
      headers: { "X-Figma-Token": FIGMA_TOKEN },
    });
    return res.data.document;
  } catch (error) {
    logger.error("Lỗi khi lấy document:", error.response?.status, error.response?.data);
    if (error.response?.status === 400) {
      throw new Error(`Lỗi 400: File key '${fileKey}' không hợp lệ hoặc file không tồn tại`);
    }
    if (error.response?.status === 403) {
      throw new Error(`Lỗi 403: Token không có quyền truy cập file '${fileKey}'`);
    }
    throw error;
  }
}

function extractFrames(node, frames = []) {
  const allowedTypes = ["FRAME", "GROUP", "COMPONENT", "INSTANCE", "SECTION"];
  if (allowedTypes.includes(node.type) && node.absoluteBoundingBox) {
    const { width, height } = node.absoluteBoundingBox;
  
    // only get frame that is large enough (example above 600x400)
    if (width > 600 && height > 400) {
        frames.push({
        id: node.id,
        name: node.name,
        width,
        height,
        });
    }
  }

  if (node.children) {
    for (const child of node.children) {
        extractFrames(child, frames);
    }
  }
  return frames;
}

// get link export image from figma
async function getImageUrls(fileKey, nodeIds, format = "png", scale = 2) {
  const ids = nodeIds.join(",");
  try {
    const res = await axios.get(
      `${FIGMA_API_URL}/images/${fileKey}?ids=${ids}&format=${format}&scale=${scale}`,
      { headers: { "X-Figma-Token": FIGMA_TOKEN } }
    );
    return res.data.images;
  } catch (error) {
    logger.error("Lỗi khi lấy image URLs:", error.response?.status, error.response?.data);
    if (error.response?.status === 400) {
      throw new Error(`Lỗi 400: ${error.response?.data?.message || 'File không tồn tại hoặc không có quyền truy cập'}`);
    }
    if (error.response?.status === 403) {
      throw new Error("Lỗi 403: Token không có quyền truy cập file này");
    }
    throw error;
  }
}

async function downloadFigmaScreens(figmaUrl, suiteId = null, formatImage = { format: "png", scale: 2 }) {
    try {
        const fileKey = getFileKeyFromUrl(figmaUrl);
        const doc = await getDocument(fileKey);

        const frames = extractFrames(doc);
        console.log("Found frames:", frames.map((f) => `${f.name} (${f.width}x${f.height})`));

        const baseUploadPath = suiteId ? 
            path.join("uploads", "extract_mindmap", suiteId.toString(), "figma") : 
            "uploads/images";
        
        if (!fs.existsSync(baseUploadPath)) {
            fs.mkdirSync(baseUploadPath, { recursive: true });
        }

        const imageUrls = await getImageUrls(fileKey, frames.map((f) => f.id), formatImage.format, formatImage.scale);
        const downloadedFiles = [];

        for (const frame of frames) {
            const url = imageUrls[frame.id];
            if (!url) continue;

            const filename = `${frame.name.replace(/[/\\?%*:|"<>]/g, "_")}.${formatImage.format}`;
            const filepath = path.join(baseUploadPath, filename);
            await downloadImage(url, filepath);
            
            downloadedFiles.push({
                fileName: filename,
                nodeId: frame.id,
                path: filepath,
                frameName: frame.name,
                width: frame.width,
                height: frame.height
            });
        }

        return {
            success: true,
            files: downloadedFiles,
            downloadPath: baseUploadPath
        };
    } catch (error) {
        logger.error("Lỗi khi download Figma:", error.message);
        return {
            success: false,
            error: error.message
        }
    }
}

module.exports = {
    downloadFigmaScreens
};

