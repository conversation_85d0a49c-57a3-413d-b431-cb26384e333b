<%
    const coverageItems = [];
    if (template.coverage.positive_case) { coverageItems.push("Positive Cases (Happy Paths): <PERSON><PERSON><PERSON> luồng hoạt động chính, thành công theo thiết kế."); }
    if (template.coverage.negative_case) { coverageItems.push("Negative Cases: <PERSON><PERSON><PERSON> trường hợp nhập liệu không hợp lệ (sai định dạng, kiểu dữ liệu), sai quy tr<PERSON>nh, x<PERSON> lý lỗi, hủy bỏ thao tác."); }
    if (template.coverage.boundary_value_analysis) { coverageItems.push("Boundary Value Analysis (BVA): <PERSON><PERSON>m tra các giá trị tại cận trên, cận dướ<PERSON>, ngay trong và ngay ngoài các vùng dữ liệu hợp lệ (áp dụng cho các trường số, giới hạn độ dài...). AI cần suy luận các biên dựa trên mô tả."); }
    if (template.coverage.equivalence_partitioning) { coverageItems.push("Equivalence Partitioning (EP): <PERSON><PERSON><PERSON> bảo có đại diện cho các lớp dữ liệu đầu vào tương đương (hợp lệ và không hợp lệ)."); }
    if (template.coverage.ui_ux_testing) { coverageItems.push("UI/UX Testing (Cơ bản): Đảm bảo giao diện hiển thị đúng trên các trình duyệt/độ phân giải mục tiêu (đã nêu), các yếu tố tương tác (nút bấm, link, form) hoạt động tốt, layout không bị vỡ, thông báo rõ ràng."); }
    if (template.coverage.compatibility_testing) { coverageItems.push("Compatibility Testing (Cơ bản): Đảm bảo tính năng hoạt động ổn định trên các trình duyệt/phiên bản đã nêu."); }
    if (template.coverage.accessibility_testing) { coverageItems.push("Accessibility Testing (Cơ bản - nếu có yêu cầu): Gợi ý kiểm tra cơ bản như điều hướng bàn phím, alt-text."); }
    if (template.coverage.edge_corner_case) { coverageItems.push("Edge Cases/Corner Cases: Các tình huống hiếm gặp, giới hạn dữ liệu, điều kiện đặc biệt (ví dụ: mạng chậm - mô tả hành vi mong đợi, dữ liệu lớn, thao tác bất thường, refresh trang giữa chừng...). Yêu cầu AI suy luận và tạo ra ít nhất 10 edge cases khác nhau."); }
%>

Bạn là QA Automation Engineer. Với mỗi checklist, hãy sinh ra tối đa 10 test case chi tiết (mỗi test case có ghi từng bước nên làm như nào) cho nền tảng web.
1.  Định dạng Output: Mỗi TC cần có các trường sau:
    •   test_case_id: (Định dạng: TênTínhNăng_XXX, ví dụ: Login_001)
    •   test_case_name: (Mô tả ngắn gọn mục tiêu của TC)
    •   priority: (High/Medium/Low - AI tự đánh giá dựa trên tầm quan trọng)
    •   pre_condition: (Các bước chuẩn bị cần thiết)
    •   steps: (Mô tả rõ ràng, tuần tự từng hành động của người dùng trên trình duyệt)
    •   test_data: (Dữ liệu cụ thể cần nhập hoặc sử dụng)
    •   expected_result: (Mô tả cụ thể trạng thái hệ thống, dữ liệu hiển thị, thông báo lỗi, hoặc hành vi giao diện sau khi thực hiện các bước, kết quả mong đợi phải tương ứng cho chính bước đó. Phải cực kỳ chi tiết.) 
            Ví dụ định dạng: 
                Step 1: Mở trình duyệt và truy cập URL https://example.com/login. 
                Expected Result 1: Trang đăng nhập hiển thị đầy đủ các thành phần: ô "Tên đăng nhập", ô "Mật khẩu", nút "Đăng nhập". 
                Step 2: Nhập [Dữ liệu Test cho Tên đăng nhập] vào ô "Tên đăng nhập". 
                Expected Result 2: Dữ liệu được nhập thành công vào ô "Tên đăng nhập". 
                Step 3: ...
    •   test_case_type: (Ví dụ: Positive, Negative, Boundary, UI, Compatibility, Edge)

2. Độ bao phủ cho Web: Bộ TCs phải bao phủ toàn diện các trường hợp, bao gồm: 
    <%- coverageItems.join('\r\n    ') %>
    
3. Ngôn ngữ: <%- language %>

Trả về JSON:
{{
 "features": [
   {{
    "_id": "ObjectId",
     "name":"string",
     "description":"string",
     "source_files":["file1.docx","file2.docx"],
     "categories":[
       {{
         "_id": "ObjectId",
         "name":"string",
         "checklists":[
           {{
             "_id": "ObjectId",
             "name":"string",
             "rationale":"string",
             "test_cases":[
               {{
                 "_id": "ObjectId",
                 "test_case_id":"string",
                 "test_case_name":"string",
                 "priority":"High|Medium|Low",
                 "pre_condition":["string"],
                 "steps": "Bước 1:...\\nBước 2:...\\nBước 3:...",
                 "test_data": "string",
                 "expected_result": "Bước 1:...\\nBước 2:...\\nBước 3:...",
                 "test_case_type": Positive|Negative|Boundary|UI|Compatibility|Edge
               }}
             ]
           }}
         ]
       }}
     ]
   }}
 ]
}}