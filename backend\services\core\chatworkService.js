const axios = require('axios');

class ChatworkService {
    constructor() {
        this.apiToken = process.env.CHATWORK_API_TOKEN;
        this.roomId = process.env.CHATWORK_ROOM_ID;
        this.apiUrl = 'https://api.chatwork.com/v2';
        this.isEnabled = Boolean(this.apiToken && this.roomId);
    }

    async sendMessage(message) {
        if (!this.isEnabled) {
            console.log('ChatworkService: Service not enabled (missing API token or room ID)');
            return false;
        }

        try {
            const url = `${this.apiUrl}/rooms/${this.roomId}/messages`;
            const payload = {
                body: message,
                self_unread: 0
            };

            const response = await axios.post(url, payload, {
                headers: {
                    'X-ChatWorkToken': this.apiToken,
                    'Content-Type': 'application/x-www-form-urlencoded'
                },
                timeout: 10000 // 10 seconds timeout
            });

            return response.status === 200;
        } catch (error) {
            console.error('ChatworkService: Failed to send message:', error.message);
            return false;
        }
    }

    async sendErrorNotification(errorMessage, context = {}, fileName = 'error.log', url = null) {
        if (!this.isEnabled) {
            return false;
        }

        try {
            // Add timestamp to message
            const timestamp = this.getFormattedTimestamp();
            const messageWithTimestamp = `[${timestamp}] ${errorMessage}`;
            
            // Get server information
            const serverInfo = this.getServerInfo(context, url);
            
            let msgChatWork = `[code][${fileName}]` + "\n"
                + messageWithTimestamp + "\n"
                + serverInfo
                + "[/code]";

            return await this.sendMessage(msgChatWork);
        } catch (error) {
            console.error('ChatworkService: Failed to send error notification:', error.message);
            return false;
        }
    }

    getFormattedTimestamp() {
        const now = new Date();
        return now.getFullYear() + '-' + 
               String(now.getMonth() + 1).padStart(2, '0') + '-' + 
               String(now.getDate()).padStart(2, '0') + ' ' + 
               String(now.getHours()).padStart(2, '0') + ':' + 
               String(now.getMinutes()).padStart(2, '0') + ':' + 
               String(now.getSeconds()).padStart(2, '0');
    }

    getServerInfo(context = {}, url = null) {
        let info = '';
        
        if (url) {
            info += `\n#Request: ${url}`;
        }
        
        if (context.error) {
            if (context.error instanceof Error) {
                info += `\n#Context: ${context.error.message}`;
            } else if (typeof context.error === 'object' && context.error.message) {
                info += `\n#Context: ${context.error.message}`;
            } else if (typeof context.error === 'string') {
                info += `\n#Context: ${context.error}`;
            }
        }

        return info;
    }
}

const chatworkService = new ChatworkService();

module.exports = chatworkService; 