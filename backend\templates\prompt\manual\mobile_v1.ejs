<%
const coverageItems = [];
if (template.coverage.positive_case) { coverageItems.push("Positive Cases (Happy Paths): <PERSON><PERSON><PERSON> luồng hoạt động chính, thành công."); }
if (template.coverage.negative_case) { coverageItems.push("Negative Cases: <PERSON><PERSON><PERSON><PERSON> li<PERSON> không hợ<PERSON> lệ, sai quy <PERSON>r<PERSON>nh, <PERSON><PERSON> lý lỗi, h<PERSON><PERSON> bỏ thao tác, từ chối cấp quyền."); }
if (template.coverage.boundary_value_analysis) { coverageItems.push("Boundary Value Analysis (BVA) & Equivalence Partitioning (EP): Áp dụng cho các trường nhập liệu có giới hạn hoặc phân loại."); }
if (template.coverage.ui_ux_testing) { coverageItems.push("UI/UX Testing: Hiển thị đúng trên các thiết bị/OS/kích thước màn hình mục ti<PERSON>, d<PERSON> sử dụ<PERSON>, đi<PERSON><PERSON> hướ<PERSON>, ph<PERSON><PERSON>ồ<PERSON>, hỗ tr<PERSON><PERSON> m<PERSON> (Portrait/Landscape)."); }
if (template.coverage.compatibility_testing) { coverageItems.push("Compatibility Testing: Hoạt động ổn định trên các thiết bị và phiên bản OS đã nêu."); }
if (template.coverage.gesture_testing) { coverageItems.push("Gesture Testing: Kiểm tra các thao tác chạm, vuốt, kéo thả, pinch, zoom... nếu có."); }
if (template.coverage.interrupt_testing) { coverageItems.push("Interrupt Testing: Xử lý các gián đoạn như cuộc gọi đến, tin nhắn, thông báo ứng dụng khác, pin yếu, cắm/rút sạc/tai nghe."); }
if (template.coverage.network_condition_testing) { coverageItems.push("Network Condition Testing: Hoạt động của tính năng dưới các điều kiện mạng khác nhau (đã nêu), bao gồm cả chế độ offline và chuyển đổi mạng."); }
if (template.coverage.permission_testing) { coverageItems.push("Permission Testing: Kiểm tra việc xin và xử lý khi người dùng cấp/từ chối quyền truy cập."); }
if (template.coverage.hardware_interaction_testing) { coverageItems.push("Hardware Interaction Testing: Kiểm tra tương tác với Camera, GPS... nếu tính năng có sử dụng."); }
if (template.coverage.installation_update_uninstall_testing) { coverageItems.push("Installation/Update/Uninstall Testing (Cơ bản): Đảm bảo cài đặt, cập nhật, gỡ bỏ không gây lỗi nghiêm trọng liên quan đến tính năng."); }
if (template.coverage.edge_corner_case) { coverageItems.push("Edge Cases/Corner Cases: Các tình huống hiếm gặp (ví dụ: bộ nhớ đầy, tương tác đa nhiệm phức tạp, sử dụng app khi đang sạc...). Yêu cầu AI suy luận."); }
%>
I. Thông tin Đầu vào cho AI

A. Ngữ cảnh Ứng dụng (Application Context):
    • Tên Dự án/Ứng dụng: <%- testScript.detail.mobile_manual.context.project_name %>
    • Loại ứng dụng: <%- testScript.detail.mobile_manual.context.app_type %>
    • Nền tảng mục tiêu: <%- testScript.detail.mobile_manual.context.target_platform %>
    • Mục tiêu chính của ứng dụng: <%- testScript.detail.mobile_manual.context.main_purpose %>
    • Đối tượng người dùng chính: <%- testScript.detail.mobile_manual.context.main_user %>

B. Đặc tả Tính năng (Feature Specifications):
    • Tính năng cần kiểm thử: <%- testScript.detail.mobile_manual.feature_spec.feature %>
    • Luồng nghiệp vụ chính (Main Business Flow): 
        <%- testScript.detail.mobile_manual.feature_spec.main_flow %>
    • Tài liệu tham khảo (User Stories, Requirements, Mockups): <%- testScript.documents.description %>
        <%- testScript.documents?.extract_file_content?.map((file, index) => (index + 1) + '. ' + file).join('\r\n        ') %>
    • Quy tắc nghiệp vụ (Business Rules): 
        <%- testScript.detail.mobile_manual.feature_spec.business_rules %>
    • Dữ liệu đầu vào & Quy tắc xác thực (Inputs & Validation Rules): <%- testScript.detail.mobile_manual.feature_spec.input_rule %>
    • Kết quả/Hành vi mong đợi chính (Expected Outputs/Behavior): <%- testScript.detail.mobile_manual.feature_spec.expected_result %>

C. Yêu cầu Kỹ thuật & Môi trường (Technical & Environment):
    • Link tải Build/Môi trường kiểm thử: <%- testScript.detail.mobile_manual.technical_requirements.test_url %>
    • Phiên bản Hệ điều hành (OS Versions) mục tiêu: <%- testScript.detail.mobile_manual.technical_requirements.os_version %>
    • Loại thiết bị và Kích thước màn hình mục tiêu: <%- testScript.detail.mobile_manual.technical_requirements.target_device %>
    • Điều kiện mạng cần kiểm thử: <%- testScript.detail.mobile_manual.technical_requirements.network_condition %>
    • Quyền truy cập cần thiết (Permissions): <%- testScript.detail.mobile_manual.technical_requirements.permission %>
    • Tương tác phần cứng (Hardware Interaction): <%- testScript.detail.mobile_manual.technical_requirements.interaction %>

D. Mục tiêu & Phạm vi Kiểm thử (Test Goal & Scope):
    • Mục tiêu kiểm thử chính: <%- testScript.detail.mobile_manual.test_scope.script_goal %>
    • Phạm vi Bao gồm (In-scope): <%= coverageItems.join(', ') %>
    • Phạm vi Không bao gồm (Out-of-scope): <%- testScript.detail.mobile_manual.test_scope.exclude_scope %>
    • Loại kiểm thử trọng tâm (Focus Area): <%- testScript.detail.mobile_manual.test_scope.focus_type %>
    • Dữ liệu kiểm thử đặc biệt (Test Data): <%- testScript.detail.mobile_manual.test_scope.special_data %>

II. Prompt Yêu cầu AI và các hướng dẫn chi tiết

Vai trò: Bạn là một Manual QA Manager với hơn 10 năm kinh nghiệm, chuyên sâu về kiểm thử ứng dụng di động (Mobile App Testing), có kiến thức về các kỹ thuật thiết kế test case và các yếu tố đặc thù của môi trường mobile.

Nhiệm vụ: Dựa vào Thông tin Đầu vào cho AI (Mục I) được cung cấp, hãy tạo ra một bộ Test Cases (TCs) thủ công chi tiết bằng <%= language %> để kiểm thử tính năng <%= testScript.detail.mobile_manual.feature_spec.feature %> trên ứng dụng di động <%= testScript.detail.mobile_manual.context.project_name %>.

Yêu cầu chi tiết cho Test Cases:

1.  Định dạng Output: Mỗi TC cần có các trường sau:
    •   test_case_id: (Định dạng: TênTínhNăng_Mobile_XXX)
    •   test_case_name: (Mô tả ngắn gọn mục tiêu của TC)
    •   priority: (High/Medium/Low)
    •   device: (Ghi rõ thiết bị và phiên bản OS áp dụng)
    •   pre_condition: (Điều kiện tiên quyết)
    •   steps: (Mô tả rõ ràng, tuần tự từng hành động trên thiết bị, bao gồm cả thao tác chạm, vuốt, nhập liệu, xoay màn hình...)
    •   test_data: (Dữ liệu cụ thể cần nhập hoặc sử dụng)
    •   expected_result: (Mô tả cụ thể trạng thái ứng dụng, dữ liệu hiển thị, thông báo lỗi, hành vi của thiết bị)
    •   test_case_type: (Ví dụ: Positive, Negative, UI, Compatibility, Interrupt, Network, Edge)

2. Độ bao phủ: Bộ TCs phải bao phủ toàn diện các trường hợp, bao gồm:
    <% coverageItems.forEach(item => { %>
        • <%- item %>
    <% }); %>
3. Ngôn ngữ: Toàn bộ Test Cases phải bằng <%- language %>

4.  Mức độ chi tiết: Các bước thực hiện và kết quả mong đợi phải rõ ràng, đủ chi tiết để tester thực hiện lại.
5.  Tập trung vào: <%- testScript.detail.mobile_manual.test_scope.focus_type %>.

Hãy bắt đầu tạo bộ Test Cases với số lượng Test Case tối ưu do AI tự suy luận để đảm bảo bao phủ hết toàn diện cho tính năng <%- testScript.detail.mobile_manual.feature_spec.feature %>.