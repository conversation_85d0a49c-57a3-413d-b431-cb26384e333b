{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"axios": "^1.10.0", "bcryptjs": "^3.0.2", "cheerio": "^1.1.0", "cors": "^2.8.5", "dotenv": "^16.5.0", "ejs": "^3.1.10", "express": "^4.21.2", "form-data": "^4.0.0", "joi": "^17.13.3", "jsonrepair": "^3.13.0", "jsonwebtoken": "^9.0.2", "mammoth": "^1.9.1", "mongoose": "^7.0.0", "multer": "^2.0.1", "openai": "^5.7.0"}, "devDependencies": {"jest": "^29.0.0", "mongodb-memory-server": "^8.12.1", "nodemon": "^3.0.0", "supertest": "^6.3.3"}, "jest": {"testEnvironment": "node"}}