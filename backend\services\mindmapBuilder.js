const FeatureRepository = require('../repositories/featureRepository');
const CheckItemRepository = require('../repositories/checkItemRepository');
const TestCaseRepository = require('../repositories/testCaseRepository');
const MindmapSnapshotRepository = require('../repositories/mindmapSnapshotRepository');
const SuiteRepository = require('../repositories/suiteRepository');
const { getFileUrl } = require('../utils/common');

async function buildTree(suiteId) {
  const features = await FeatureRepository.findAll({ suite_id: suiteId, del_flag: 0 }, { sort: { order: 1, _id: 1 } });
  const featureIds = features.map(f => f._id);
  const categories = await CheckItemRepository.findAll({ suite_id: suiteId, feature_id: { $in: featureIds }, type: 'category', del_flag: 0 }, { sort: { order: 1, _id: 1 } });
  const catIds = categories.map(c => c._id);
  const checklists = await CheckItemRepository.findAll({ suite_id: suiteId, parent_id: { $in: catIds }, type: 'checklist', del_flag: 0 }, { sort: { order: 1, _id: 1 } });
  const checklistIds = checklists.map(c => c._id);
  const testCases = await TestCaseRepository.findAll({ suite_id: suiteId, check_item_id: { $in: checklistIds }, del_flag: 0, type: 'manual' }, { sort: { order: 1, _id: 1 } });

  const tcsByChecklist = testCases.reduce((acc, tc) => {
    const caseType = Object.keys(tc.detail || {})[0];
    const d = (tc.detail && tc.detail[caseType]) || {};
    const { _id: detailId, ...dWithoutId } = d;
    (acc[tc.check_item_id] ||= []).push({
      _id: String(tc._id),
      ...dWithoutId,
      comment: tc.comment || '',
      status: tc.status,
      review_status: tc.review_status,
      duplicate_of: tc.duplicate_of,
      invalid_reason: tc.invalid_reason
    });
    return acc;
  }, {});

  const checklistsByCategory = checklists.reduce((acc, cl) => {
    (acc[cl.parent_id] ||= []).push({
      _id: String(cl._id),
      name: cl.name,
      rationale: cl.rationale,
      test_cases: tcsByChecklist[cl._id] || []
    });
    return acc;
  }, {});

  const categoriesByFeature = categories.reduce((acc, cat) => {
    (acc[cat.feature_id] ||= []).push({
      _id: String(cat._id),
      name: cat.name,
      checklists: checklistsByCategory[cat._id] || []
    });
    return acc;
  }, {});

  const featuresTree = features.map(f => ({
    _id: String(f._id),
    name: f.name,
    description: f.description,
    source_files: f.source_files || [],
    categories: categoriesByFeature[f._id] || []
  }));

  return { features: featuresTree };
}

async function rebuildSnapshot(suiteId, data = {}) {
  const { title, description } = data ?? {};
  const suite = await SuiteRepository.findById(suiteId);
  const tree = await buildTree(suiteId);
  
  const updatedSnapshot = await MindmapSnapshotRepository.upsertBySuiteId(suiteId, tree, title, description);
  return {
    ...tree,
    title: updatedSnapshot.title,
    description: updatedSnapshot.description,
    field_type: suite?.field_type,
    figma_url: suite?.figma_url,
    documents: {
      main: (suite?.documents?.main || []).map(doc => ({
        ...doc,
        url: getFileUrl(doc.path)
      })),
    },
    images: (suite?.images || []).map(img => ({
      ...img,
      url: getFileUrl(img.path)
    }))
  };
}

module.exports = {
  buildTree,
  rebuildSnapshot
};


