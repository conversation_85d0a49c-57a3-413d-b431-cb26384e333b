const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');
const { Schema } = mongoose;

const TestCaseVersionSchema = new Schema({
  suite_id: {
    type: Schema.Types.ObjectId,
    ref: 'suite',
    required: true
  },
  created_by: {
    type: Schema.Types.ObjectId,
    ref: 'user',
    required: true
  },
  version_number: {
    type: Number,
    min: 1
  },
  version_name: {
    type: String,
    trim: true,
    default: function() {
      return `Version ${this.version_number}`;
    }
  },
  test_cases: {
    type: Object,
    required: true,
    validate: {
      validator: function(v) {
        return v && typeof v === 'object';
      }
    }
  },
  mindmap: {
    type: Object,
    required: true,
    validate: {
      validator: function(v) {
        return v && typeof v === 'object';
      }
    }
  },
  modified_at: {
    type: Date,
    default: Date.now
  },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

TestCaseVersionSchema.plugin(softDeletePlugin);

TestCaseVersionSchema.pre('save', async function(next) {
  if (this.isNew) {
    try {
      const lastVersion = await this.constructor.findOne({
        suite_id: this.suite_id
      }).sort({ version_number: -1 });
      
      this.version_number = lastVersion ? lastVersion.version_number + 1 : 1;
      
      this.version_name = `Version ${this.version_number}`;
    } catch (error) {
      return next(error);
    }
  }
  next();
});

TestCaseVersionSchema.statics.getLatestVersion = function(suiteId) {
  return this.findOne({
    suite_id: suiteId,
    del_flag: 0
  }).sort({ version_number: -1 });
};


module.exports = mongoose.model('test_case_version', TestCaseVersionSchema); 