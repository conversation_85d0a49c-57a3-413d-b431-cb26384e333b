const Jo<PERSON> = require('joi');

exports.createProjectSchema = Joi.object({
    name: Joi.string().required().messages({
        'string.empty': 'Tên dự án không được để trống',
    }),
    description: Joi.string().optional().allow(''),
}).unknown(true);

exports.updateProjectSchema = Joi.object({
    name: Joi.string().optional().messages({
        'string.empty': 'Tên dự án không được để trống',
    }),
    description: Joi.string().optional().allow(''),
}).unknown(true); 