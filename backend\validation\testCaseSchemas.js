const Joi = require('joi');
const TestCaseRepository = require('../repositories/testCaseRepository');
const mongoose = require('mongoose');

exports.createTestCaseSchema = Joi.object({
    suite_id: Joi.string().required().messages({
        'string.empty': 'Suite ID không được để trống',
        'any.required': 'Suite ID là trường bắt buộc'
    }),
    feature_name: Joi.string()
    .messages({
        'string.empty': 'Tên tính năng không được để trống',
        'any.required': 'Tên tính năng là trường bắt buộc'
    })
    .when('feature_id', {
        is: Joi.exist().not(null, ''),
        then: Joi.optional().allow('', null),
        otherwise: Joi.required()
    }),
    category_name: Joi.string()
    .messages({
        'string.empty': 'Tên danh mục không được để trống',
        'any.required': 'Tên danh mục là trường bắt buộc'
    })
    .when('category_id', {
        is: Joi.exist().not(null, ''),
        then: Joi.optional().allow('', null),
        otherwise: Joi.required()
    }),
    checklist_name: Joi.string()
    .messages({
        'string.empty': 'Tên checklist không được để trống',
        'any.required': 'Tên checklist là trường bắt buộc'
    })
    .when('checklist_id', {
        is: Joi.exist().not(null, ''),
        then: Joi.optional().allow('', null),
        otherwise: Joi.required()
    }),
    test_case_id: Joi.string().required().messages({
        'string.empty': 'Test case ID không được để trống',
        'any.required': 'Test case ID là trường bắt buộc'
    }).external(async (value, helpers) => {
        const { suite_id } = helpers.prefs.context;
        
        if (!suite_id) {
            return helpers.message('Suite ID không tồn tại');
        }

        // Validate suite_id format
        if (!mongoose.Types.ObjectId.isValid(suite_id)) {
            return helpers.message('Suite ID không hợp lệ');
        }

        const existingTestCases = await TestCaseRepository.findAll({ 
            suite_id: suite_id,
            del_flag: 0 
        });

        if (existingTestCases.length === 0) {
            return helpers.message('Bộ test case không tồn tại');
        }

        // check exist test case in suite
        const isDuplicate = existingTestCases.some(testCase => {
            const detail = testCase.detail;
            if (detail.web_manual?.test_case_id === value) return true;
            if (detail.mobile_manual?.test_case_id === value) return true;
            if (detail.api_manual?.test_case_id === value) return true;
            return false;
        });

        if (isDuplicate) {
            return helpers.message('Test case ID đã tồn tại');
        }

        return value;
    }),
    test_case_name: Joi.string().required().messages({
        'string.empty': 'Tên test case không được để trống',
        'any.required': 'Tên test case là trường bắt buộc'
    }),
    priority: Joi.string().required().messages({
        'string.empty': 'Độ ưu tiên không được để trống',
        'any.required': 'Độ ưu tiên là trường bắt buộc'
    }),
    steps: Joi.string().required().messages({
        'string.empty': 'Các bước thực hiện không được để trống',
        'any.required': 'Các bước thực hiện là trường bắt buộc'
    }),
    expected_result: Joi.string().required().messages({
        'string.empty': 'Kết quả mong đợi không được để trống',
        'any.required': 'Kết quả mong đợi là trường bắt buộc'
    }),
    test_case_type: Joi.string().required().messages({
        'string.empty': 'Loại test case không được để trống',
        'any.required': 'Loại test case là trường bắt buộc'
    })
}).unknown(true);

