require('dotenv').config();
const OpenAI = require('openai');
const logger = require('./loggerService');
const { jsonrepair } = require('jsonrepair');

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

const parseJsonSafely = (jsonString) => {
  if (!jsonString || typeof jsonString !== 'string') {
    logger.error('Invalid input for JSON parsing');
    return [];
  }

  try {
    return JSON.parse(jsonString);
  } catch (directError) {
    // Continue to next strategy
  }

  try {
    const cleaned = cleanJsonString(jsonString);
    return JSON.parse(cleaned);
  } catch (cleanError) {
    // Continue to next strategy
  }

  try {
    const repaired = jsonrepair(jsonString);
    return JSON.parse(repaired);
  } catch (repairError) {
    // Continue to next strategy
  }

  try {
    const extracted = extractValidJson(jsonString);
    if (extracted) {
      return JSON.parse(extracted);
    }
  } catch (extractError) {
    // Continue to fallback
  }

  logger.info('All JSON parsing strategies failed, returning empty array');
  return [];
};

const cleanJsonString = (str) => {
  if (!str || typeof str !== 'string') {
    return '[]';
  }

  try {
    let cleaned = str
      .replace(/^\uFEFF/, '')
      .replace(/[\u200B-\u200D\uFEFF]/g, '')
      .replace(/```json\s*/g, '')
      .replace(/```\s*/g, '')
      .replace(/\n\s*\n/g, '\n')
      .replace(/,(\s*[}\]])/g, '$1')
      .replace(/([}\]]),\s*([}\]])/g, '$1$2')
      // Fix quote issues
      .replace(/([{,]\s*)([a-zA-Z_][a-zA-Z0-9_]*)\s*:/g, '$1"$2":')
      .replace(/:\s*'([^'\\]*(\\.[^'\\]*)*)'/g, ': "$1"')
      .replace(/\\u([0-9a-fA-F]{4})/g, (match, code) => {
        try {
          return String.fromCharCode(parseInt(code, 16));
        } catch {
          return match;
        }
      })
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // Fix escape sequences
      .replace(/\\([^"\\\/bfnrtu])/g, '\\\\$1')
      .replace(/\\\\/g, '\\')  // Fix double backslashes
      .trim();

    const firstBracket = Math.min(
      cleaned.indexOf('[') === -1 ? Infinity : cleaned.indexOf('['),
      cleaned.indexOf('{') === -1 ? Infinity : cleaned.indexOf('{')
    );

    if (firstBracket !== Infinity && firstBracket > 0) {
      cleaned = cleaned.substring(firstBracket);
    }

    const lastBracket = Math.max(
      cleaned.lastIndexOf(']'),
      cleaned.lastIndexOf('}')
    );

    if (lastBracket !== -1 && lastBracket < cleaned.length - 1) {
      cleaned = cleaned.substring(0, lastBracket + 1);
    }

    return cleaned;
  } catch (error) {
    logger.debug('Error in cleanJsonString:', error.message);
    return str.trim();
  }
};

const extractValidJson = (str) => {
  if (!str || typeof str !== 'string') {
    return null;
  }

  const arrayMatch = str.match(/\[[\s\S]*\]/);
  if (arrayMatch) {
    return arrayMatch[0];
  }

  const objectMatch = str.match(/\{[\s\S]*\}/);
  if (objectMatch) {
    return objectMatch[0];
  }

  return null;
};

const validateOutput = (output) => {
  if (Array.isArray(output)) {
    return output;
  }

  if (output && typeof output === 'object') {
    return [output];
  }

  return [];
};

const getSystemPrompt = () => {
  return `Bạn là một QA Expert chuyên nghiệp với khả năng thiết kế test case toàn diện.

MỤC TIÊU CHÍNH: Tạo ra số lượng test case tối đa với chất lượng cao, bao phủ đầy đủ các kịch bản có thể.

YÊU CẦU OUTPUT FORMAT:
1. CHỈ trả về JSON array hợp lệ: [{"key": "value"}]
2. Nếu không có dữ liệu: trả về []
3. Đảm bảo JSON syntax đúng (quoted keys, no trailing comma)
4. Sử dụng UTF-8 encoding cho ký tự tiếng Việt

YÊU CẦU NỘI DUNG:
- Tạo ra NHIỀU test case nhất có thể để bao phủ toàn diện
- Ưu tiên chất lượng và tính toàn diện của bộ test
- Các field trống: sử dụng "" thay vì "N/A" 
- Field có nhiều bước: xuống dòng bằng \\n
- Ví dụ: "steps": "Bước 1: Đăng nhập\\nBước 2: Chọn menu\\nBước 3: Kiểm tra"

QUAN TRỌNG: Hãy tập trung vào việc tạo ra một bộ test case phong phú và toàn diện, đồng thời đảm bảo JSON format chuẩn.`;
};

const callChatGpt = async (prompt, previous_response_id = null) => {
  try {
    if (!prompt || typeof prompt !== 'string') {
      logger.error('Invalid prompt provided to OpenAI API');
      return {
        id: 'error-invalid-prompt',
        output: []
      };
    }

    logger.info('Calling OpenAI API with prompt length:', prompt.length);

    const response = await openai.responses.create({
      model: 'gpt-4.1',
      input: [
        { role: "system", content: getSystemPrompt() },
        { role: 'user', content: prompt }
      ],
      previous_response_id: previous_response_id,
      store: true,
      temperature: 0
    });

    let rawOutput = response.output_text;

    if (!rawOutput) {
      logger.error('No content received from OpenAI API');
      return {
        id: response.id,
        output: []
      };
    }

    const parsedOutput = parseJsonSafely(rawOutput);

    const validatedOutput = validateOutput(parsedOutput);

    return {
      id: response.id,
      output: validatedOutput
    };

  } catch (error) {
    logger.error('Error calling OpenAI API:', error);

    return {
      id: null,
      output: [],
      error: 'Failed to generate content from OpenAI API'
    };
  }
};


const callOpenAiForExtract = async (prompt, previous_response_id = null) => {
  try {
    if (!prompt || typeof prompt !== 'string') {
      logger.error('Invalid prompt provided to extractDocumentByPrompt');
      return {
        id: 'error-invalid-prompt',
        text: ''
      };
    }

    logger.info('Calling OpenAI (extract) with prompt length:', prompt.length);

    const response = await openai.responses.create({
      model: 'gpt-4.1',
      input: [
        { role: 'user', content: prompt }
      ],
      previous_response_id: previous_response_id,
      temperature: 0,
      store: true
    });

    const text = (response.output_text || '').trim();

    if (!text) {
      logger.error('No content received from OpenAI extract');
      return {
        id: response.id || null,
        text: ''
      };
    }

    return {
      id: response.id || null,
      text
    };
  } catch (error) {
    logger.error('Error in extractDocumentByPrompt:', error);
    return {
      id: null,
      text: '',
      error: 'Failed to extract document from OpenAI'
    };
  }
};

const callOpenAiForExtractWithImages = async (prompt, imagePaths = [], previous_response_id = null) => {
  try {
    if (!prompt || typeof prompt !== 'string') {
      logger.error('Invalid prompt provided to extractDocumentByPromptWithImages');
      return {
        id: 'error-invalid-prompt',
        text: ''
      };
    }

    const content = [
      { type: 'text', text: prompt }
    ];

    const maxImages = 10;
    const limitedImagePaths = imagePaths.slice(0, maxImages);
    
    for (const imagePath of limitedImagePaths) {
      try {
        const fs = require('fs');
        if (fs.existsSync(imagePath)) {
          const imageData = fs.readFileSync(imagePath);
          const base64Image = imageData.toString('base64');
          const ext = require('path').extname(imagePath).toLowerCase();
          const mimeType = ext === '.png' ? 'image/png' : 'image/jpeg';
          
          content.push({
            type: 'image_url',
            image_url: {
              url: `data:${mimeType};base64,${base64Image}`,
              detail: 'low'
            }
          });

        } else {
          logger.info(`Image file not found: ${imagePath}`);
        }
      } catch (imageError) {
        logger.error(`Error processing image ${imagePath}:`, imageError);
      }
    }

    const response = await openai.chat.completions.create({
      model: 'gpt-4.1',
      messages: [
        { role: 'user', content: content }
      ],
      temperature: 0
    });

    const text = response.choices?.[0]?.message?.content || '';

    if (!text) {
      logger.error('No content received from OpenAI extract with images');
      return {
        id: response.id || null,
        text: ''
      };
    }

    return {
      id: response.id || null,
      text: text.trim()
    };
  } catch (error) {
    logger.error('Error in extractDocumentByPromptWithImages:', error);
    return {
      id: null,
      text: '',
      error: 'Failed to extract document with images from OpenAI'
    };
  }
};

module.exports = {
  callChatGpt,
  parseJsonSafely,
  validateOutput,
  cleanJsonString,
  extractValidJson,
  callOpenAiForExtract,
  callOpenAiForExtractWithImages
};