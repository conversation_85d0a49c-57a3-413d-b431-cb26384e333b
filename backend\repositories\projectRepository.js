const BaseRepository = require('./baseRepository');
const Project = require('../models/project');
const Suite = require('../models/suite');
const mongoose = require('mongoose');

class ProjectRepository extends BaseRepository {
  constructor() {
    super(Project);
  }

  async findAllWithTestSuiteCount(options = {}) {
    const { userId, onlyUserProjects = false } = options || {};

    const pipeline = [
      {
        $match: { del_flag: { $ne: 1 } }
      },
      {
        $lookup: {
          from: 'suites',
          localField: '_id',
          foreignField: 'project_id',
          as: 'testSuites',
          pipeline: [
            {
              $match: { del_flag: { $ne: 1 } }
            }
          ]
        }
      },
      {
        $addFields: {
          testSuiteCount: { $size: '$testSuites' }
        }
      }
    ];

    if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      const userObjectId = new mongoose.Types.ObjectId(userId);

      pipeline.push(
        {
          $lookup: {
            from: 'user_projects',
            let: { projectId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$project_id', '$$projectId'] },
                      { $eq: ['$user_id', userObjectId] },
                      { $eq: ['$status', 'active'] },
                      { $ne: ['$del_flag', 1] }
                    ]
                  }
                }
              }
            ],
            as: 'userProjects'
          }
        },
        {
          $addFields: {
            project_role: {
              $cond: [
                { $gt: [{ $size: '$userProjects' }, 0] },
                { $arrayElemAt: ['$userProjects.project_role', 0] },
                null
              ]
            }
          }
        },
        ...(onlyUserProjects
          ? [
              {
                $match: {
                  $expr: {
                    $gt: [{ $size: '$userProjects' }, 0]
                  }
                }
              }
            ]
          : []),
        {
          $project: {
            testSuites: 0,
            userProjects: 0
          }
        }
      );
    } else {
      pipeline.push({
        $project: {
          testSuites: 0
        }
      });
    }

    pipeline.push({
      $sort: { updatedAt: -1 }
    });

    return await this.model.aggregate(pipeline);
  }

  async findAllWithTestSuiteCountByFilters(options = {}) {
    const {
      userId,
      onlyUserProjects = false,
      filters = {}
    } = options || {};

    const matchStage = { del_flag: { $ne: 1 } };

    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null && value !== '') {
        matchStage[key] = value;
      }
    });

    const pipeline = [
      {
        $match: matchStage
      },
      {
        $lookup: {
          from: 'suites',
          localField: '_id',
          foreignField: 'project_id',
          as: 'testSuites',
          pipeline: [
            {
              $match: { del_flag: { $ne: 1 } }
            }
          ]
        }
      },
      {
        $addFields: {
          testSuiteCount: { $size: '$testSuites' }
        }
      }
    ];

    if (userId && mongoose.Types.ObjectId.isValid(userId)) {
      const userObjectId = new mongoose.Types.ObjectId(userId);

      pipeline.push(
        {
          $lookup: {
            from: 'user_projects',
            let: { projectId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$project_id', '$$projectId'] },
                      { $eq: ['$user_id', userObjectId] },
                      { $eq: ['$status', 'active'] },
                      { $ne: ['$del_flag', 1] }
                    ]
                  }
                }
              }
            ],
            as: 'userProjects'
          }
        },
        {
          $lookup: {
            from: 'project_join_requests',
            let: { projectId: '$_id' },
            pipeline: [
              {
                $match: {
                  $expr: {
                    $and: [
                      { $eq: ['$project_id', '$$projectId'] },
                      { $eq: ['$user_id', userObjectId] },
                      { $eq: ['$status', 'pending'] },
                      { $ne: ['$del_flag', 1] }
                    ]
                  }
                }
              }
            ],
            as: 'joinRequests'
          }
        },
        {
          $addFields: {
            project_role: {
              $cond: [
                { $gt: [{ $size: '$userProjects' }, 0] },
                { $arrayElemAt: ['$userProjects.project_role', 0] },
                null
              ]
            },
            has_pending_request: {
              $cond: [
                { $gt: [{ $size: '$joinRequests' }, 0] },
                true,
                false
              ]
            }
          }
        },
        ...(onlyUserProjects
          ? [
              {
                $match: {
                  $expr: {
                    $gt: [{ $size: '$userProjects' }, 0]
                  }
                }
              }
            ]
          : []),
        {
          $project: {
            testSuites: 0,
            userProjects: 0,
            joinRequests: 0
          }
        }
      );
    } else {
      pipeline.push({
        $project: {
          testSuites: 0
        }
      });
    }

    pipeline.push({
      $sort: { updatedAt: -1 }
    });

    return await this.model.aggregate(pipeline);
  }
}

module.exports = new ProjectRepository(); 