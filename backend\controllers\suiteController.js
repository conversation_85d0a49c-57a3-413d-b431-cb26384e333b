const Suite = require('../models/suite');
const Project = require('../models/project');
const TestCaseVersion = require('../models/testCaseVersion');
const TestCase = require('../models/testCase');
const TestCaseRepository = require('../repositories/testCaseRepository');
const TestCaseHistoryRepository = require('../repositories/testCaseHistoryRepository');
const TestCaseVersionRepository = require('../repositories/testCaseVersionRepository');
const TestCaseRegenerateRepository = require('../repositories/testCaseRegenerateRepository');
const userProjectRepository = require('../repositories/userProjectRepository');
const logger = require('../services/core/loggerService');
const MindmapSnapshotRepository = require('../repositories/mindmapSnapshotRepository');
const userProjectService = require('../services/userProjectService');
const config = require('../config/config');
const { getUserLogin, isAdmin, isAdminOrLeader, getFileUrl } = require('../utils/common');

exports.handleSuiteApproval = async (req, res) => {
  try {
    const { suite_id, action, submit_approval, approved_notes, rejected_notes, final_approver_id} = req.body;

    if (!suite_id || !action) {
      return res.status(400).json({ 
        error: 'Dữ liệu đầu vào không hợp lệ.' 
      });
    }

    const suite = await Suite.findById(suite_id);
    if (!suite) {
      return res.status(404).json({ error: 'Bộ test case không tồn tại.' });
    }

    const user_id = suite.user_id ? suite.user_id.toString() : null;

    const user = await getUserLogin(req);

    const userProjectRole = await userProjectService.getProjectRole(req, suite.project_id);
    const isAdminOrManager = config.user.project_full_access_roles.includes(user?.role);
    const hasPermission = isAdminOrManager ||
                         [config.project_role.leader, config.project_role.tester].includes(userProjectRole);
      

    let updateData = {};

    switch (action) {
      case 'submit':
        // Only suite owner or admin can submit for approval
        if (user_id === submit_approval?.approver_id?.toString()) {
          return res.status(400).json({ error: 'Vui lòng chọn người phê duyệt khác.' });
        }
        if (!hasPermission) {
          return res.status(400).json({ error: 'Bạn không có quyền gửi duyệt.' });
        } 
        if ((!submit_approval || !submit_approval.approver_id) && suite.approval_status !== config.suite_approval_status.rejected) {
          return res.status(400).json({ 
            error: 'Nhập thông tin người duyệt.' 
          });
        }
        if (suite.approval_status !== config.suite_approval_status.draft && suite.approval_status !== config.suite_approval_status.rejected) {
          return res.status(400).json({ 
            error: 'Chỉ có thể gửi duyệt bộ test case có trạng thái draft hoặc rejected.'
          });
        }

        // create test case version
        try {
          await handleVersion(suite_id, user_id);
        } catch (versionError) {
          logger.error('Lỗi khi tạo version', { 
            error: versionError.message
          });
          return res.status(500).json({ 
            error: versionError.message 
          });
        }

        if (suite.approval_status === config.suite_approval_status.rejected) {
          updateData = {
            approval_status: config.suite_approval_status.pending,
            submit_approval_time: new Date()
          };
        } else {
          updateData = {
            approval_status: config.suite_approval_status.pending,
            submit_approval: {
              approver_id: submit_approval?.approver_id,
              sender_id: user?._id ? user._id.toString() : null,
              notes: submit_approval?.notes || ''
            },
            submit_approval_time: new Date()
          };
        }
        break;

      case 'withdraw':
        // Only suite owner or admin can withdraw approval request
        if (!hasPermission) {
          return res.status(400).json({ error: 'Bạn không có quyền thu hồi yêu cầu duyệt.' });
        }
        if (suite.approval_status !== config.suite_approval_status.pending) {
          return res.status(400).json({ 
            error: 'Chỉ có thể thu hồi bộ test case đang chờ duyệt.' 
          });
        }
        updateData = {
          approval_status: config.suite_approval_status.draft,
          submit_approval: {
            approver_id: null,
            sender_id: null,
            notes: ''
          },
          approved_notes: null,
          rejected_notes: null,
          submit_approval_time: null
        };
        break;

      case 'approve':
        if (suite.approval_status !== config.suite_approval_status.pending) {
          return res.status(400).json({ 
            error: 'Chỉ có thể duyệt bộ test case đang chờ duyệt.' 
          });
        }
        
        if (!isAdminOrManager && userProjectRole !== config.project_role.leader) {
          return res.status(400).json({ 
            error: 'Bạn không có quyền duyệt bộ test case này.' 
          });
        }
        updateData = {
          approval_status: config.suite_approval_status.approved,
          final_approver_id: final_approver_id,
          approved_notes: approved_notes || ''
        };
        break;

      case 'reject':
        if (suite.approval_status !== config.suite_approval_status.pending) {
          return res.status(400).json({ 
            error: 'Chỉ có thể từ chối bộ test case đang chờ duyệt.' 
          });
        }
        
        if (!isAdminOrManager && userProjectRole !== config.project_role.leader) {
          return res.status(400).json({ 
            error: 'Bạn không có quyền từ chối bộ test case này.' 
          });
        }
        updateData = {
          approval_status: config.suite_approval_status.rejected,
          final_approver_id: final_approver_id,
          rejected_notes: rejected_notes || ''
        };
        break;

      default:
        return res.status(400).json({ 
          error: 'Action không hợp lệ. Chỉ chấp nhận: submit, withdraw, approve, reject' 
        });
    }

    const updatedSuite = await Suite.findByIdAndUpdate(
      suite_id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).populate('submit_approval.approver_id', 'name');

    res.json({
      suite: updatedSuite
    });
  } catch (err) {
    logger.error('Lỗi khi xử lý duyệt bộ test case', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.getSuites = async (req, res) => {
  try {
    const { project_id, approval_status, mindmap_title } = req.query;

    let filter = { del_flag: 0 };


    if (project_id) {
      const project = await Project.findById(project_id);
      if (!project || project.del_flag === 1) {
        return res.status(404).json({ error: 'Project không tồn tại hoặc đã bị xóa' });
      }
      filter.project_id = project_id;
    }
    
    if (approval_status) {
      filter.approval_status = approval_status;
    }

    if (mindmap_title) {
      const mindmapSnapshotsByTitle = await MindmapSnapshotRepository.findAll({
        title: { $regex: mindmap_title.trim(), $options: 'i' },
      });

      const filteredSuiteIds = mindmapSnapshotsByTitle
        .map(snapshot => snapshot.suite_id)
        .filter(id => id != null);

      if (filteredSuiteIds.length === 0) {
        return res.json([]);
      }

      filter._id = { $in: filteredSuiteIds };
    }
    
    const suites = await Suite.find(filter)
      .populate('project_id', 'name')
      .populate('submit_approval.approver_id', 'name')
      .populate('user_id', 'name')
      .populate('final_approver_id', 'name')
      .sort({ updatedAt: -1 });

    const suiteIds = suites.map(suite => suite._id);
    let mindmapTitleMap = {};

    if (suiteIds.length > 0) {
      const mindmapSnapshots = await MindmapSnapshotRepository.findAll({
        suite_id: { $in: suiteIds },
        del_flag: 0
      });

      mindmapTitleMap = mindmapSnapshots.reduce((acc, snapshot) => {
        if (snapshot?.suite_id) {
          acc[snapshot.suite_id.toString()] = snapshot.title || null;
        }
        return acc;
      }, {});
    }

    const suitesWithMindmapTitle = suites.map(suite => {
      const suiteObject = suite.toObject();
      suiteObject.mindmap_title = mindmapTitleMap[suite._id.toString()] || null;
      return suiteObject;
    });

    res.json(suitesWithMindmapTitle);
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách bộ test case', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.getListApproval = async (req, res) => {
  try {
    const { mindmap_title, approval_status } = req.query;
    const userLogin = await getUserLogin(req);

    if (!userLogin?._id) {
      return res.status(401).json({ error: 'Không xác thực được người dùng.' });
    }

    const isAdminOrManager = [
      config.global_role.admin,
      config.global_role.test_manager
    ].includes(userLogin?.role);

    let filter = {
      del_flag: 0
    }

    if (!isAdminOrManager) {
      const userProjects = await userProjectRepository.findAll({
        user_id: userLogin._id
      });

      const projectIds = userProjects
        .map(project => project.project_id)
        .filter(Boolean);

      if (projectIds.length === 0) {
        return res.json([]);
      }
      filter.project_id = { $in: projectIds };
    }

    if (approval_status) {
      filter.approval_status = approval_status;
    } else {
      filter.approval_status = { $ne: 'draft' };
    }

    if (mindmap_title) {
      const mindmapSnapshotsByTitle = await MindmapSnapshotRepository.findAll({
        title: { $regex: mindmap_title.trim(), $options: 'i' },
      });

      const filteredSuiteIds = mindmapSnapshotsByTitle
        .map(snapshot => snapshot.suite_id)
        .filter(id => id != null);

      if (filteredSuiteIds.length === 0) {
        return res.json([]);
      }

      filter._id = { $in: filteredSuiteIds };
    }

    const suites = await Suite.find(filter)
      .populate('project_id', 'name')
      .populate('submit_approval.approver_id', 'name')
      .populate('user_id', 'name')
      .populate('final_approver_id', 'name')
      .sort({ updatedAt: -1 });

    const suiteIds = suites.map(suite => suite._id);
    let mindmapTitleMap = {};

    if (suiteIds.length > 0) {
      const mindmapSnapshots = await MindmapSnapshotRepository.findAll({
        suite_id: { $in: suiteIds },
        del_flag: 0
      });

      mindmapTitleMap = mindmapSnapshots.reduce((acc, snapshot) => {
        if (snapshot?.suite_id) {
          acc[snapshot.suite_id.toString()] = snapshot.title || null;
        }
        return acc;
      }, {});
    }

    const suitesWithMindmapTitle = suites.map(suite => {
      const suiteObject = suite.toObject();
      suiteObject.mindmap_title = mindmapTitleMap[suite._id.toString()] || null;
      return suiteObject;
    });

    res.json(suitesWithMindmapTitle);
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách phê duyệt bộ test case', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.getSuiteById = async (req, res) => {
  try {
    const { id } = req.params;
    const suite = await Suite.findById(id)
      .populate('project_id', 'name')
      .populate('submit_approval.sender_id', 'name')
      .populate('submit_approval.approver_id', 'name')
      .populate('final_approver_id', 'name')
      .populate('user_id', 'name');
    
    if (!suite) {
      return res.status(404).json({ error: 'Không tìm thấy suite' });
    }

    const loginUser = await getUserLogin(req);
    const userProjectRole = await userProjectService.getProjectRole(req, suite.project_id?._id);

    const isAdminOrManager = loginUser?.role === config.global_role.admin || 
                            loginUser?.role === config.global_role.test_manager;

    if (!isAdminOrManager && !userProjectRole) {
      return res.status(403).json({ error: 'Bạn không có quyền xem bộ test case này.' });
    }
    
    res.json(suite);
  } catch (err) {
    logger.error('Lỗi khi lấy chi tiết bộ test case', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

const handleVersion = async (suite_id, user_id) => {
  try {
    const hasHistoryChanges = await TestCaseHistoryRepository.findOne({ 
      suite_id: suite_id,
      test_case_version_id: null
    });

    const hasRegenerateChanges = await TestCaseRegenerateRepository.findOne({ 
      suite_id: suite_id,
      test_case_version_id: null
    });

    if (!hasHistoryChanges && !hasRegenerateChanges) {
      logger.info(`Không có thay đổi nào cho suite ${suite_id}, bỏ qua việc tạo version mới`);
      return;
    }

    const testCases = await TestCaseRepository.findAll({ suite_id });
    const mindmap = await MindmapSnapshotRepository.findOne({ suite_id });
    const suite = await Suite.findById(suite_id);

    // create test case version
    const testCaseVersion = new TestCaseVersion({
      suite_id: suite_id,
      created_by: user_id,
      test_cases: testCases,
      mindmap: {
        ...mindmap?.tree,
        description: mindmap?.description,
        title: mindmap?.title,
        field_type: suite?.field_type,
        figma_url: suite?.figma_url,
        documents: {
          main: (suite?.documents?.main || []).map(doc => ({
            ...doc,
            url: getFileUrl(doc.path)
          })),
          sub: (suite?.documents?.sub || []).map(doc => ({
            ...doc,
            url: getFileUrl(doc.path)
          }))
        },
        images: (suite?.images || []).map(img => ({
          ...img,
          url: getFileUrl(img.path)
        }))
      }
    });

    await testCaseVersion.save();
    
    // update test_case_history records with the new version id
    await TestCaseHistoryRepository.updateMany(
      { 
        suite_id: suite_id,
        test_case_version_id: null
      },
      { 
        test_case_version_id: testCaseVersion._id 
      }
    );

    // update test_case_regenerate records with the new version id
    await TestCaseRegenerateRepository.updateMany(
      { 
        suite_id: suite_id,
        test_case_version_id: null
      },
      { 
        test_case_version_id: testCaseVersion._id 
      }
    );

    logger.info(`Đã tạo version mới ${testCaseVersion._id} cho suite ${suite_id}`);

  } catch (error) {
    logger.error('Lỗi khi xử lý phiên bản test case', error);
    throw new Error('Lỗi khi xử lý phiên bản test case: ' + error.message);
  }
}