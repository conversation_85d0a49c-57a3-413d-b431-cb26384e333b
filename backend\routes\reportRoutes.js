const express = require('express');
const router = express.Router();
const authMiddleware = require('../middleware/auth');
const { requireViewerOrHigher } = require('../middleware/permission');

// Health check
router.get('/health', (req, res) => {
  res.json({ message: 'Reports service is running' });
});

// Get reports (viewer or higher required)
router.get('/', authMiddleware, requireViewerOrHigher, (req, res) => {
  res.json({ 
    message: 'Reports endpoint - Viewer access granted',
    user: {
      role: req.user.role,
      name: req.user.name
    },
    reports: [
      { id: 1, title: 'Test Report 1', date: new Date().toISOString() },
      { id: 2, title: 'Test Report 2', date: new Date().toISOString() }
    ]
  });
});

// Get specific report (viewer or higher required)
router.get('/:id', authMiddleware, requireViewerOrHigher, (req, res) => {
  const { id } = req.params;
  res.json({ 
    message: `Report ${id} - Viewer access granted`,
    user: {
      role: req.user.role,
      name: req.user.name
    },
    report: {
      id: id,
      title: `Test Report ${id}`,
      content: 'This is a sample report content',
      date: new Date().toISOString()
    }
  });
});

module.exports = router;
