<%
    const coverageItems = [];
    if (template.coverage.positive_case) { coverageItems.push("Positive Cases (Happy Paths): <PERSON><PERSON><PERSON> luồng hoạt động chính, thành công theo thiết kế."); }
    if (template.coverage.negative_case) { coverageItems.push("Negative Cases: <PERSON><PERSON><PERSON> trường hợp nhập liệu không hợp lệ (sai định dạng, kiểu dữ liệu), sai quy tr<PERSON>nh, x<PERSON> lý lỗi, hủy bỏ thao tác."); }
    if (template.coverage.boundary_value_analysis) { coverageItems.push("Boundary Value Analysis (BVA): <PERSON><PERSON>m tra các giá trị tại cận trên, cận dướ<PERSON>, ngay trong và ngay ngoài các vùng dữ liệu hợp lệ (áp dụng cho các trường số, giới hạn độ dài...). AI cần suy luận các biên dựa trên mô tả."); }
    if (template.coverage.equivalence_partitioning) { coverageItems.push("Equivalence Partitioning (EP): <PERSON><PERSON><PERSON> bảo có đại diện cho các lớp dữ liệu đầu vào tương đương (hợp lệ và không hợp lệ)."); }
    if (template.coverage.ui_ux_testing) { coverageItems.push("UI/UX Testing (Cơ bản): Đảm bảo giao diện hiển thị đúng trên các trình duyệt/độ phân giải mục tiêu (đã nêu), các yếu tố tương tác (nút bấm, link, form) hoạt động tốt, layout không bị vỡ, thông báo rõ ràng."); }
    if (template.coverage.compatibility_testing) { coverageItems.push("Compatibility Testing (Cơ bản): Đảm bảo tính năng hoạt động ổn định trên các trình duyệt/phiên bản đã nêu."); }
    if (template.coverage.accessibility_testing) { coverageItems.push("Accessibility Testing (Cơ bản - nếu có yêu cầu): Gợi ý kiểm tra cơ bản như điều hướng bàn phím, alt-text."); }
    if (template.coverage.edge_corner_case) { coverageItems.push("Edge Cases/Corner Cases: Các tình huống hiếm gặp, giới hạn dữ liệu, điều kiện đặc biệt (ví dụ: mạng chậm - mô tả hành vi mong đợi, dữ liệu lớn, thao tác bất thường, refresh trang giữa chừng...). Yêu cầu AI suy luận các trường hợp này."); }
%>
I. Thông tin Đầu vào cho AI

A. Ngữ cảnh Ứng dụng & Dự án (Application & Project Context):
    • Tên Dự án/Ứng dụng: <%- testScript.detail.web_automation.context.project_name %>
    • Loại ứng dụng: <%- testScript.detail.web_automation.context.app_type %>
    • Mục tiêu chính của ứng dụng: <%- testScript.detail.web_automation.context.main_purpose %>
    • Đối tượng người dùng/Vai trò: <%- testScript.detail.web_automation.context.main_user %>
    • Mục tiêu dự án tự động hóa: <%- testScript.detail.web_automation.context.automation_purpose %>

B. Đặc tả Tính năng/Luồng cần Tự động hóa (Feature/Flow Specifications):
    • Tính năng/Module/Luồng/User Story: <%- testScript.detail.web_automation.feature_spec.feature %>
    • Mô tả hoạt động/Các bước thực hiện thủ công:
        <%- testScript.detail.web_automation.feature_spec.manual_steps ? testScript.detail.web_automation.feature_spec.manual_steps.split('\n').join('\r\n        ') : '' %>
    • Tài liệu tham khảo (Requirements, Specs, Mockups, Manual TCs): <%- testScript.documents.description %>
        <%- testScript.documents.files.map((file, index) => (index + 1) + '. ' + file).join('\r\n        ') %>
    • Quy tắc nghiệp vụ (Business Rules):
        <%- testScript.detail.web_automation.feature_spec.business_rule ? testScript.detail.web_automation.feature_spec.business_rule.split('\n').join('\r\n        ') : '' %>
    • Dữ liệu đầu vào & Quy tắc xác thực (Inputs & Validation Rules): <%- testScript.detail.web_automation.feature_spec.input_rule %>
    • Kết quả/Điểm xác minh mong đợi (Expected Outputs/Assertions): <%- testScript.detail.web_automation.feature_spec.expected_result %>

C. Công cụ & Môi trường Automation (Automation Tools & Environment):
    • (QUAN TRỌNG) Automation Tool/Library: <%- template.language %>
    • (QUAN TRỌNG) Ngôn ngữ Lập trình: <%- template.language %>
    • (QUAN TRỌNG) Testing Framework: <%- template.framework %>
    • URL Môi trường kiểm thử: <%- testScript.detail.web_automation.automation_env.test_url %>
    • Trình duyệt mục tiêu: <%- testScript.detail.web_automation.automation_env.browser %>
    • Thông tin xác thực test (Test Credentials): <%- testScript.detail.web_automation.automation_env.test_credential %>
    • (Tùy chọn/Nâng cao) Build Tool: <%- testScript.detail.web_automation.automation_env.build_tool %>
    • (Tùy chọn/Nâng cao) CI/CD Platform: <%- testScript.detail.web_automation.automation_env.cicd_platform %>

D. Yêu cầu về Code & Design Pattern (Code & Design Pattern Requirements):
    • (QUAN TRỌNG) Design Pattern: <%- template.design_pattern %>
    • Cấu trúc Dự án/Package (nếu có): <%- testScript.detail.web_automation.code_requirements.project_structure %>
    • Coding Standards & Conventions: <%- testScript.detail.web_automation.code_requirements.coding_standard %>
    • Thư viện/Utils hỗ trợ (nếu có): <%- testScript.detail.web_automation.code_requirements.support_lib %>
    • Chiến lược Test Data: <%- testScript.detail.web_automation.code_requirements.test_data_strategy %>
    • (Tùy chọn/Nâng cao) Configuration Management: <%- testScript.detail.web_automation.code_requirements.config_management %>
    • (Tùy chọn/Nâng cao) Reporting: <%- testScript.detail.web_automation.code_requirements.reporting %>

E. Thông tin Locators & Wait Strategy (Locators & Wait Strategy):
    • Cung cấp Locators (ưu tiên ID/CSS): <%- testScript.detail.web_automation.locator_info.locator %>
    • Wait Strategy: <%- testScript.detail.web_automation.locator_info.wait_strategy %>

F. Mục tiêu, Phạm vi & Coverage Script (Automation Script Goal, Scope & Coverage):
    • Mục tiêu chính của script: <%- testScript.detail.web_automation.test_scope.main_purpose %>
    • Phạm vi Bao gồm (In-scope): <%- coverageItems.map(item => item.replace('• ', '')).join(', ') %>
    • Phạm vi Không bao gồm (Out-of-scope): <%- testScript.detail.web_automation.test_scope.exclusion_scope %>
    • Loại kiểm thử trọng tâm (Focus Area): <%- testScript.detail.web_automation.test_scope.focus_type %>


II. Prompt Yêu cầu AI

Vai trò: Bạn là một Senior Web Automation Engineer với kinh nghiệm dày dặn về <%- template.language %>, và <%- template.framework %>. Bạn thành thạo <%- template.design_pattern %> và luôn viết code sạch, dễ bảo trì, tuân thủ coding standards.

Nhiệm vụ: Dựa vào TOÀN BỘ Thông tin Đầu vào cho AI (Mục I), hãy viết code bằng <%- template.language %> để tự động hóa luồng/tính năng <%- testScript.detail.web_automation.feature_spec.feature %> cho ứng dụng web <%- testScript.detail.web_automation.context.project_name %>.

Yêu cầu chi tiết cho Code Output:

1.  Ngôn ngữ & Công cụ: Sử dụng đúng cú pháp, thư viện của <%- template.language %>, và <%=- template.framework %>.
2.  Design Pattern: Áp dụng triệt để <%- template.design_pattern %>.
    •   Nếu là POM: Tạo các class Page Object riêng biệt (ví dụ: `LoginPage.java`, `HomePage.java`). Trong Page Class, khai báo WebElements (dùng locators cung cấp hoặc để `// TODO: Verify locator`) và các methods thực hiện hành động trên trang (nên trả về Page Object tiếp theo - Fluent).
3.  Cấu trúc Code: Tổ chức code theo cấu trúc package yêu cầu (nếu có). Sử dụng đúng annotations của framework (`@Test`, `@BeforeMethod`...). Tạo các class Test riêng biệt gọi methods từ Page Objects.
4.  Locators & Waits: Sử dụng locators cung cấp hoặc đề xuất. Áp dụng Explicit Waits (khuyến nghị) trước khi tương tác với elements quan trọng.
5.  Coding Standards: Tuân thủ standards đã nêu (naming, comments...). Code cần rõ ràng, dễ đọc, có tính tái sử dụng (tạo helper methods nếu cần) và dễ bảo trì.
6.  Assertions: Bao gồm các assertions rõ ràng của framework (`Assert.assertEquals`, `expect().toHaveText()`...) để xác minh Kết quả mong đợi.
7.  Test Data: Áp dụng chiến lược test data yêu cầu (ví dụ: cấu trúc DataProvider).
8.  Error Handling & Logging: Tích hợp cơ chế xử lý lỗi cơ bản (try-catch nếu cần) và logging (gợi ý vị trí log).
9.  Output Structure: Cung cấp code hoàn chỉnh cho các thành phần chính theo Mục tiêu chính của script, ví dụ:
    •   Class Page Object(s)
    •   Class Test Script(s)
    •   (Nếu cần) Class Utility/Helper cơ bản
    •   (Nếu cần) Cấu trúc file cấu hình/data mẫu
10. Mức độ hoàn thiện: Cung cấp code khung hoạt động được. Với các logic phức tạp hoặc locators không chắc chắn, hãy để comment `// TODO: Implement/Verify...`.
11.  Độ bao phủ: Bộ TCs phải bao phủ toàn diện các trường hợp, bao gồm:
    <%- coverageItems.join('\r\n    ') %>

Hãy bắt đầu viết code automation để tạo bộ Test Cases với số lượng Test Case tối ưu do AI tự suy luận để đảm bảo bao phủ hết toàn diện cho tính năng <%= testScript.detail.web_automation.feature_spec.feature %>