const logger = require('../services/core/loggerService');
const mindmapService = require('../services/mindmapService');
const ProjectRepository = require('../repositories/projectRepository');
const SuiteRepository = require('../repositories/suiteRepository');
const TestCase = require('../models/testCase');
const FeatureRepository = require('../repositories/featureRepository');
const CheckItemRepository = require('../repositories/checkItemRepository');
const TestCaseRepository = require('../repositories/testCaseRepository');
const TestCaseVersion = require('../models/testCaseVersion');
const mindmapBuilder = require('../services/mindmapBuilder');
const MindmapSnapshotRepository = require('../repositories/mindmapSnapshotRepository');
const userProjectService = require('../services/userProjectService');
const config = require('../config');
const { getUserLogin } = require('../utils/common');
const ejs = require('ejs');
const path = require('path');
const mongoose = require('mongoose');
const openaiService = require('../services/core/openaiService');
const pythonApiService = require('../services/core/pythonApiService');
const { getFileUrl } = require('../utils/common');
const { getFakeResponse } = require('../utils/devMode');
const { updateMindmap: updateMindmapService, MindmapUpdateError } = require('../services/mindmapService');
const fs = require('fs');
const FormData = require('form-data');

exports.createFeature = async (req, res) => {
  try {
    // Check DEV_MODE - return fake data if enabled
    const fakeResponse = await getFakeResponse('createFeature');
    if (fakeResponse) {
      return res.status(201).json(fakeResponse);
    }

    if (!req.body.title) {
      return res.status(400).json({ error: 'Tiêu đề là bắt buộc' });
    }
    const { title, description, figma_url, main_document_indices, field_type, files, images } = req.body;

    if (!config.suite.field_type.includes(field_type)) {
      return res.status(400).json({ error: 'field_type không hợp lệ' });
    }

    const suite_id = req.params.suite_id;
    let filesUpload = req.files;
    const imagesUpload = req.images;
    const projectId = req.body.project_id;
    const isUpdate = !!suite_id;
    let existingSuite = null;
    const finalSuiteId = isUpdate ? suite_id : req.suiteId;

    if (isUpdate) {
      existingSuite = await SuiteRepository.findById(suite_id);
      if (!existingSuite) {
        return res.status(400).json({ error: 'suite_id không tồn tại' });
      }
    }

    if (!isUpdate && (!filesUpload || filesUpload.length === 0)) {
      return res.status(400).json({ error: 'Không có tài liệu được cung cấp' });
    }
    if (!projectId) {
      return res.status(400).json({ error: 'project_id is không được để trống' });
    }

    let oldFiles = [];
    if (isUpdate) {
      oldFiles = [
        ...(existingSuite.documents?.main || []),
        ...(existingSuite.documents?.sub || [])
      ];
    }

    const mainIndices = main_document_indices
      ? main_document_indices.split(',').map(idx => parseInt(idx.trim()))
      : [];

    const mainDocuments = [];
    const subDocuments = [];

    filesUpload.forEach((file, index) => {
      const docInfo = {
        original_name: file.originalname,
        file_name: file.filename,
        path: file.path,
        mime_type: file.mimetype,
        size: file.size
      };

      if (this.checkMainDocument(mainIndices, file.fieldname)) {
        mainDocuments.push(docInfo);
      } else {
        subDocuments.push(docInfo);
      }
    });

    // Handle existing files from request body
    if (files && Array.isArray(files) && isUpdate) {
      files.forEach((fileUrl, index) => {
        if (fileUrl) {
          const existingFile = oldFiles.find(oldFile =>
            oldFile.path === fileUrl || oldFile.url === fileUrl
          );

          if (existingFile) {
            const docInfo = {
              original_name: existingFile.original_name,
              file_name: existingFile.file_name,
              path: existingFile.path,
              mime_type: existingFile.mime_type,
              size: existingFile.size
            };

            if (mainIndices.includes(index)) {
              mainDocuments.push(docInfo);
            } else {
              subDocuments.push(docInfo);
            }
          }
        }
      });
    }

    const imagesData = [];

    if (imagesUpload && Array.isArray(imagesUpload)) {
      imagesUpload.forEach(img => {
        imagesData.push({
          original_name: img.originalname,
          file_name: img.filename,
          path: img.path,
          size: img.size
        });
      });
    }

    // Handle existing images from request body
    if (images && Array.isArray(images) && isUpdate) {
      const oldImages = [
        ...(existingSuite.images || [])
      ];

      images.forEach((imageUrl) => {
        if (imageUrl) {
          const existingImage = oldImages.find(oldImage =>
            oldImage.path === imageUrl || oldImage.url === imageUrl
          );

          if (existingImage) {
            const imageInfo = {
              original_name: existingImage.original_name,
              file_name: existingImage.file_name,
              path: existingImage.path,
              size: existingImage.size
            };

            imagesData.push(imageInfo);
          }
        }
      });
    }

    // Call Python API để extract features
    const formData = new FormData();
    formData.append('suite_id', finalSuiteId.toString());

    const filesUploadForAI = [...mainDocuments, ...subDocuments];
    
    // Append all files từ filesUploadForPrompt
    for (const file of filesUploadForAI) {
      if (file.path && fs.existsSync(file.path)) {
        formData.append('files', fs.createReadStream(file.path), {
          filename: file.original_name || file.file_name,
          contentType: file.mime_type
        });
      }
    }
    
    // Append main_document_indices
    const computedMainDocumentIndices = mainDocuments.map((_, idx) => idx).join(',');
    formData.append('main_document_indices', computedMainDocumentIndices);
    
    // Append figma_url if exists
    if (figma_url) {
      formData.append('figma_url', figma_url);
    }
    
    // Append images from imagesData
    for (const image of imagesData) {
      if (image && fs.existsSync(image.path)) {
        formData.append('images', fs.createReadStream(image.path), {
          filename: image.original_name || image.file_name
        });
      }
    }
    
    // Call Python API
    logger.info('[Python API] Calling /extract-features endpoint');
    const pythonResponse = await pythonApiService.postWithFiles('/extract-features', formData);
    
    if (!pythonResponse.success) {
      logger.error('[Python API] Failed to extract features:', pythonResponse.error);
      return res.status(500).json({ 
        error: 'Lỗi khi tạo feature từ Python server',
        details: pythonResponse.error 
      });
    }
    
    const { features } = pythonResponse.data;
   
    const processed = Array.isArray(features) ? features : [];

    let suite;
    if (isUpdate) {
      suite = await SuiteRepository.update(finalSuiteId, {
        project_id: projectId,
        field_type: field_type,
        figma_url: figma_url || null,
        documents: {
          main: mainDocuments,
          sub: subDocuments
        },
        images: imagesData
      });
    } else {
      const userLogin = await getUserLogin(req);
      suite = await SuiteRepository.create({
        _id: finalSuiteId.toString(),
        project_id: projectId,
        previous_response_id: null,
        user_id: userLogin._id,
        field_type: field_type,
        figma_url: figma_url || null,
        documents: {
          main: mainDocuments,
          sub: subDocuments
        },
        images: imagesData
      });
    }

    const project = await ProjectRepository.findById(projectId);
    const savedFeatures = [];
    
    let order = 0;
    if (isUpdate) {
      await FeatureRepository.deleteMany({ suite_id: finalSuiteId });
    }

    for (const f of processed) {
      const savedFeature = await FeatureRepository.create({
        suite_id: suite._id,
        name: f.name || '',
        description: f.description || '',
        source_files: Array.isArray(f.source_files) ? f.source_files : [],
        merged_from: Array.isArray(f.merged_from) ? f.merged_from : [],
        permissions: f.permissions || {},
        ui_components: f.ui_components || {},
        order: order++
      });
      
      savedFeatures.push({
        _id: savedFeature._id,
        name: savedFeature.name,
        description: savedFeature.description,
        source_files: savedFeature.source_files,
        categories: []
      });
    }

    await mindmapBuilder.rebuildSnapshot(suite._id, { title, description });
    
    const response = {
      project_id: projectId,
      suite_id: suite._id,
      project_name: project ? project.name : '',
      features: savedFeatures,
      figma_url: figma_url,
      field_type: field_type,
      documents: {
        main: mainDocuments.map(doc => ({
          ...doc,
          url: getFileUrl(doc.path)
        })),
        sub: subDocuments.map(doc => ({
          ...doc,
          url: getFileUrl(doc.path)
        }))
      },
      images: imagesData.map(img => ({
        ...img,
        url: getFileUrl(img.path)
      }))
    };

    res.status(201).json(response);
  } catch (err) {
    logger.error('Lỗi khi tạo mindmap', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}

exports.createMindmapWithTestCases = async (req, res) => {
  try {
    if (!req.body.title) {
      return res.status(400).json({ error: 'Tiêu đề là bắt buộc' });
    }
    const { title, description, figma_url, main_document_indices, field_type, files, images, language } = req.body;

    if (!config.suite.field_type.includes(field_type)) {
      return res.status(400).json({ error: 'field_type không hợp lệ' });
    }

    const suite_id = req.params.suite_id;
    let filesUpload = req.files;
    const imagesUpload = req.images;
    const projectId = req.body.project_id;
    const isUpdate = !!suite_id;
    let existingSuite = null;
    const finalSuiteId = isUpdate ? suite_id : req.suiteId;

    if (isUpdate) {
      existingSuite = await SuiteRepository.findById(suite_id);
      if (!existingSuite) {
        return res.status(400).json({ error: 'suite_id không tồn tại' });
      }
    }

    if (!isUpdate && (!filesUpload || filesUpload.length === 0)) {
      return res.status(400).json({ error: 'Không có tài liệu được cung cấp' });
    }
    if (!projectId) {
      return res.status(400).json({ error: 'project_id is không được để trống' });
    }

    let oldFiles = [];
    if (isUpdate) {
      oldFiles = [
        ...(existingSuite.documents?.main || []),
        ...(existingSuite.documents?.sub || [])
      ];
    }

    const mainIndices = main_document_indices
      ? main_document_indices.split(',').map(idx => parseInt(idx.trim()))
      : [];

    const mainDocuments = [];
    const subDocuments = [];

    filesUpload.forEach((file, index) => {
      const docInfo = {
        original_name: file.originalname,
        file_name: file.filename,
        path: file.path,
        mime_type: file.mimetype,
        size: file.size
      };

      if (this.checkMainDocument(mainIndices, file.fieldname)) {
        mainDocuments.push(docInfo);
      } else {
        subDocuments.push(docInfo);
      }
    });

    // Handle existing files from request body
    if (files && Array.isArray(files) && isUpdate) {
      files.forEach((fileUrl, index) => {
        if (fileUrl) {
          const existingFile = oldFiles.find(oldFile =>
            oldFile.path === fileUrl || oldFile.url === fileUrl
          );

          if (existingFile) {
            const docInfo = {
              original_name: existingFile.original_name,
              file_name: existingFile.file_name,
              path: existingFile.path,
              mime_type: existingFile.mime_type,
              size: existingFile.size
            };

            if (mainIndices.includes(index)) {
              mainDocuments.push(docInfo);
            } else {
              subDocuments.push(docInfo);
            }
          }
        }
      });
    }

    const imagesData = [];

    if (imagesUpload && Array.isArray(imagesUpload)) {
      imagesUpload.forEach(img => {
        imagesData.push({
          original_name: img.originalname,
          file_name: img.filename,
          path: img.path,
          size: img.size
        });
      });
    }

    // Handle existing images from request body
    if (images && Array.isArray(images) && isUpdate) {
      const oldImages = [
        ...(existingSuite.images || [])
      ];

      images.forEach((imageUrl) => {
        if (imageUrl) {
          const existingImage = oldImages.find(oldImage =>
            oldImage.path === imageUrl || oldImage.url === imageUrl
          );

          if (existingImage) {
            const imageInfo = {
              original_name: existingImage.original_name,
              file_name: existingImage.file_name,
              path: existingImage.path,
              size: existingImage.size
            };

            imagesData.push(imageInfo);
          }
        }
      });
    }

    // Call Python API để tạo test cases
    const formData = new FormData();

    const filesUploadForAI = [...mainDocuments, ...subDocuments, ...imagesData];
    
    // Append all files
    for (const file of filesUploadForAI) {
      if (file.path && fs.existsSync(file.path)) {
        formData.append('files', fs.createReadStream(file.path), {
          filename: file.original_name || file.file_name,
          contentType: file?.mime_type
        });
      }
    }
    
    // Append main_document_indices
    const computedMainDocumentIndices = mainDocuments.map((_, idx) => idx).join(',');
    formData.append('main_document_indices', computedMainDocumentIndices);

    formData.append('model', config.api_ai.model_name);
    formData.append('prompt_version', config.api_ai.prompt_version);
    formData.append('language', language);
    formData.append('use_fallback', 'false');
    
    // Append figma_url if exists
    if (figma_url) {
      formData.append('figma_url', figma_url);
    }
    
    // Call Python API
    logger.info('[Python API] Calling /generate-testcases-from-files-hierarchical endpoint');
    const pythonResponse = await pythonApiService.postWithFiles('/generate-testcases-from-files-hierarchical', formData);
    
    if (!pythonResponse.success) {
      logger.error('[Python API] Failed to create test cases:', pythonResponse.error);
      return res.status(500).json({ 
        error: 'Lỗi khi tạo test case từ Python server',
        details: pythonResponse.error 
      });
    }
    
    const { features_with_test_cases } = pythonResponse.data;
   
    const processed = Array.isArray(features_with_test_cases) ? features_with_test_cases : [];

    if (processed.length === 0) {
      return res.status(400).json({ error: 'Không có dữ liệu test case được trả về từ Python server' });
    }
    const userLogin = await getUserLogin(req);

    let suite;
    if (isUpdate) {
      suite = await SuiteRepository.update(finalSuiteId, {
        project_id: projectId,
        field_type: field_type,
        figma_url: figma_url || null,
        documents: {
          main: mainDocuments,
          sub: subDocuments
        },
        images: imagesData
      });
    } else {
      suite = await SuiteRepository.create({
        _id: finalSuiteId.toString(),
        project_id: projectId,
        user_id: userLogin._id,
        field_type: field_type,
        figma_url: figma_url || null,
        documents: {
          main: mainDocuments,
          sub: subDocuments
        },
        images: imagesData
      });
    }

    // Delete existing data if update
    if (isUpdate) {
      await FeatureRepository.deleteMany({ suite_id: finalSuiteId });
      await CheckItemRepository.deleteMany({ suite_id: finalSuiteId });
      await TestCaseRepository.deleteMany({ suite_id: finalSuiteId, type: 'manual' });
    }

    const project = await ProjectRepository.findById(projectId);
    
    let featureOrder = 0;
    let caseType = 'web_manual';
    if (suite.field_type) {
      const fieldType = suite.field_type.toLowerCase();
      if (config.suite.field_type.includes(fieldType)) {
        caseType = `${fieldType}_manual`;
      }
    }

    // Process features with test cases
    for (const featureData of processed) {
      // Create Feature
      const savedFeature = await FeatureRepository.create({
        suite_id: suite._id,
        name: featureData.name || '',
        description: featureData.description || '',
        source_files: [],
        merged_from: [],
        permissions: {},
        ui_components: {},
        order: featureOrder++
      });

      const categories = Array.isArray(featureData.categories) ? featureData.categories : [];

      let categoryOrder = 0;
      for (const categoryData of categories) {
        const savedCategory = await CheckItemRepository.create({
          suite_id: suite._id,
          feature_id: savedFeature._id,
          parent_id: null,
          type: 'category',
          name: categoryData.name || '',
          rationale: '',
          order: categoryOrder++
        });

        const checklists = Array.isArray(categoryData.checklists) ? categoryData.checklists : [];

        let checklistOrder = 0;
        for (const checklistData of checklists) {
          const savedChecklist = await CheckItemRepository.create({
            suite_id: suite._id,
            feature_id: savedFeature._id,
            parent_id: savedCategory._id,
            type: 'checklist',
            name: checklistData.name || '',
            rationale: checklistData.rationale || '',
            order: checklistOrder++
          });

          // Create Test Cases
          const testCases = Array.isArray(checklistData.test_cases) ? checklistData.test_cases : [];
          let testCaseOrder = 0;
          for (const tc of testCases) {
            const detail = {};
            const baseDetail = {
              test_case_id: tc.test_case_id || '',
              test_case_name: tc.test_case_name || '',
              priority: tc.priority || '',
              pre_condition: Array.isArray(tc.preconditions) ? tc.preconditions.join('\n') : (tc.preconditions || ''),
              steps: typeof tc.steps === 'string' ? tc.steps : (Array.isArray(tc.steps) ? tc.steps.join('\n') : ''),
              test_data: typeof tc.test_data === 'string' ? tc.test_data : (typeof tc.test_data === 'object' ? Object.entries(tc.test_data).map(([k, v]) => `${k}: ${v}`).join('\n') : ''),
              expected_result: typeof tc.expected_result === 'string' ? tc.expected_result : (Array.isArray(tc.expected_result) ? tc.expected_result.join('\n') : ''),
              test_case_type: tc.test_case_type || ''
            };

            if (caseType === 'api_manual') {
              baseDetail.http_method = tc.http_method || '';
              baseDetail.header = typeof tc.header === 'object' ? Object.entries(tc.header).map(([k, v]) => `${k}: ${v}`).join('\n') : (tc.header || '');
              baseDetail.request_body = typeof tc.request_body === 'object' ? JSON.stringify(tc.request_body) : (tc.request_body || '');
            }

            detail[caseType] = baseDetail;

            await TestCase.create({
              suite_id: suite._id,
              feature_id: savedFeature._id,
              category_id: savedCategory._id,
              check_item_id: savedChecklist._id,
              detail,
              order: testCaseOrder++,
              type: 'manual'
            });
          }
        }
      }
    }

    const tree = await mindmapBuilder.rebuildSnapshot(suite._id, { title, description });

    // create test case version
    try {
      const testCases = await TestCaseRepository.findAll({ suite_id: suite._id });

      const testCaseVersion = new TestCaseVersion({
        suite_id: suite._id,
        created_by: userLogin._id,
        test_cases: testCases,
        mindmap: tree
      });

      await testCaseVersion.save();
      logger.info(`Đã tạo test case version ${testCaseVersion._id} cho suite ${suite._id}`);
    } catch (versionError) {
      logger.error('Lỗi khi tạo test case version', versionError, req.originalUrl);
    }
    
    const response = {
      project_id: projectId,
      suite_id: suite._id,
      project_name: project ? project.name : '',
      features: tree.features || [],
      figma_url: figma_url,
      field_type: field_type,
      documents: {
        main: mainDocuments.map(doc => ({
          ...doc,
          url: getFileUrl(doc.path)
        })),
        sub: subDocuments.map(doc => ({
          ...doc,
          url: getFileUrl(doc.path)
        }))
      },
      images: imagesData.map(img => ({
        ...img,
        url: getFileUrl(img.path)
      }))
    };

    res.status(201).json(response);
  } catch (err) {
    logger.error('Lỗi khi tạo feature với test cases', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}

exports.createChecklist = async (req, res) => {
  try {
    // Check DEV_MODE - return fake data if enabled
    const fakeResponse = await getFakeResponse('createChecklist');
    if (fakeResponse) {
      return res.status(201).json(fakeResponse);
    }

    const { suite_id } = req.body;

    const inputData = await mindmapService.buildInputDataForChecklist(suite_id);

    // Call Python API to generate checklists
    const pythonResponse = await pythonApiService.post('/generate-checklists', {
      features_with_categories: inputData
    });
    if (!pythonResponse.success) {
      logger.error('[Python API] Failed to generate checklists:', pythonResponse.error);
      return res.status(500).json({ 
        error: 'Lỗi khi tạo checklist từ Python server',
        details: pythonResponse.error 
      });
    }

    const { features_with_checklists: features } = pythonResponse.data;

    // check features is empty
    if (!features || features.length === 0) {
      return res.status(500).json({ error: 'Failed to generate checklist' });
    }

    await CheckItemRepository.deleteMany({ suite_id: suite_id });

    for (const feature of features) {
      const featureRecord = await FeatureRepository.findById(feature._id);
      if (!featureRecord) continue;
      const categories = Array.isArray(feature.categories) ? feature.categories : [];
      let catOrder = 0;
      for (const category of categories) {
        const cat = await CheckItemRepository.create({
          suite_id: suite_id,
          feature_id: featureRecord._id,
          parent_id: null,
          type: 'category',
          name: category.name || '',
          order: catOrder++
        });
        const checklists = Array.isArray(category.checklists) ? category.checklists : [];
        let clOrder = 0;
        for (const cl of checklists) {
          await CheckItemRepository.create({
            suite_id: suite_id,
            feature_id: featureRecord._id,
            parent_id: cat._id,
            type: 'checklist',
            name: cl.name || '',
            rationale: cl.rationale || '',
            order: clOrder++
          });
        }
      }
    }

    const tree = await mindmapBuilder.rebuildSnapshot(suite_id);
    res.status(201).json(tree);
  } catch (err) {
    logger.error('Lỗi khi tạo checklist', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}

exports.createTestCase = async (req, res) => {
  try {
    // Check DEV_MODE - return fake data if enabled
    const fakeResponse = await getFakeResponse('createTestCase');
    if (fakeResponse) {
      return res.status(201).json(fakeResponse);
    }

    const data = req.body;

    const suite = await SuiteRepository.findById(data.suite_id);

    if (!suite) {
      return res.status(400).json({ error: 'suite_id không tồn tại' });
    }

    let caseType = 'web_manual';
    if (suite.field_type) {
      const fieldType = suite.field_type.toLowerCase();
      if (config.suite.field_type.includes(fieldType)) {
        caseType = `${fieldType}_manual`;
      }
    }

    data.field_type = suite.field_type;

    await TestCaseRepository.deleteMany({ suite_id: data.suite_id, type: 'manual' });

    if (suite.field_type && suite.field_type.toLowerCase() === 'api') {
      const tree = await this.createTestCaseV1(data, caseType, req);
      return res.status(201).json(tree);
    }

    const inputData = await mindmapService.buildInputDataForTestCase(data.suite_id);

    let coverageArray = [];
    if (data.coverage) {
      coverageArray = Object.keys(data.coverage)
        .filter(key => data.coverage[key] === 1)
        .map(key => config.coverage[key])
        .filter(Boolean);
    }

    const pythonResponse = await pythonApiService.post('/generate-test-cases', {
      features_with_checklists: inputData,
      prompt_language: data.prompt_language,
      test_platform: data.field_type,
      test_coverage: coverageArray
    });

    if (!pythonResponse.success) {
      logger.error('Python API error:', pythonResponse);
      return res.status(500).json({ error: 'Failed to generate test cases from Python API', details: pythonResponse.error });
    }
  
    const { features_with_test_cases: featuresWithTestCases, response_ids: responseIds } = pythonResponse.data;

    for (const [index, featureRecord] of featuresWithTestCases.entries()) {
      await FeatureRepository.update(featureRecord._id, { previous_response_id: responseIds[index] });
      const cats = Array.isArray(featureRecord.categories) ? featureRecord.categories : [];
      for (const category of cats) {
        const categoryRecord = await CheckItemRepository.findOne({ suite_id: data.suite_id, feature_id: featureRecord._id, type: 'category', name: category.name });
        if (!categoryRecord) continue;
        const checklists = Array.isArray(category.checklists) ? category.checklists : [];
        for (const checklist of checklists) {
          const checklistRecord = await CheckItemRepository.findOne({ suite_id: data.suite_id, parent_id: categoryRecord._id, type: 'checklist', name: checklist.name });
          if (!checklistRecord) continue;
          const tcs = Array.isArray(checklist.test_cases) ? checklist.test_cases : [];
          for (const tc of tcs) {
            const detail = {};
            const baseDetail = {
              test_case_id: tc.test_case_id || '',
              test_case_name: tc.test_case_name || '',
              priority: tc.priority || '',
              pre_condition: Array.isArray(tc.preconditions) ? tc.preconditions.join('\n') : (tc.preconditions || ''),
              steps: Array.isArray(tc.steps) ? tc.steps.map(step => {
                if (typeof step === 'object' && step !== null) {
                  const key = Object.keys(step)[0];
                  const value = step[key];
                  return `${key}: ${value}`;
                }
                return step;
              }).join('\n') : (tc.steps || ''),
              test_data: typeof tc.test_data === 'object' ? Object.entries(tc.test_data).map(([k, v]) => `${k}: ${v}`).join('\n') : (tc.test_data || ''),
              expected_result: Array.isArray(tc.expected_result) ? tc.expected_result.join('\n') : (tc.expected_result || ''),
              test_case_type: tc.test_case_type || ''
            };

            if (caseType === 'api_manual') {
              baseDetail.http_method = tc.http_method || '';
              baseDetail.header = typeof tc.header === 'object' ? Object.entries(tc.header).map(([k, v]) => `${k}: ${v}`).join('\n') : (tc.header || '');
              baseDetail.request_body = typeof tc.request_body === 'object' ? JSON.stringify(tc.request_body) : (tc.request_body || '');
            }

            detail[caseType] = baseDetail;

            await TestCase.create({
              suite_id: data.suite_id,
              feature_id: featureRecord._id,
              category_id: categoryRecord._id,
              check_item_id: checklistRecord._id,
              detail
            });
          }
        }
      }
    }

    const tree = await mindmapBuilder.rebuildSnapshot(data.suite_id);

    // create test case version
    try {
      const userLogin = await getUserLogin(req);
      const testCases = await TestCaseRepository.findAll({ suite_id: data.suite_id });

      const testCaseVersion = new TestCaseVersion({
        suite_id: data.suite_id,
        created_by: userLogin._id,
        test_cases: testCases,
        mindmap: tree
      });

      await testCaseVersion.save();
      logger.info(`Đã tạo test case version ${testCaseVersion._id} cho suite ${data.suite_id}`);
    } catch (versionError) {
      logger.error('Lỗi khi tạo test case version', versionError, req.originalUrl);
    }

    res.status(201).json(tree);
  } catch (err) {
    logger.error('Lỗi khi tạo test case', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}

exports.createTestCaseV1 = async (data, caseType, req) => {
  try {
    const features = await FeatureRepository.findAll({ suite_id: data.suite_id, del_flag: 0 }, { sort: { order: 1, _id: 1 } });

    let lastOpenaiId = null;
    for (const featureRecord of features) {
      const { id, categories } = await mindmapService.generateTestCaseForFeature(data, featureRecord);
      if (id) {
        await FeatureRepository.update(featureRecord._id, { previous_response_id: id });
        lastOpenaiId = id;
      }

      const cats = Array.isArray(categories) ? categories : [];
      for (const category of cats) {
        const categoryRecord = await CheckItemRepository.findOne({ suite_id: data.suite_id, feature_id: featureRecord._id, type: 'category', name: category.name });
        if (!categoryRecord) continue;
        const checklists = Array.isArray(category.checklists) ? category.checklists : [];
        for (const checklist of checklists) {
          const checklistRecord = await CheckItemRepository.findOne({ suite_id: data.suite_id, parent_id: categoryRecord._id, type: 'checklist', name: checklist.name });
          if (!checklistRecord) continue;
          const tcs = Array.isArray(checklist.test_cases) ? checklist.test_cases : [];
          for (const tc of tcs) {
            const detail = {};
            const baseDetail = {
              test_case_id: tc.test_case_id || '',
              test_case_name: tc.test_case_name || '',
              priority: tc.priority || '',
              pre_condition: Array.isArray(tc.pre_condition) ? tc.pre_condition.join('\n') : (tc.pre_condition || ''),
              steps: Array.isArray(tc.steps) ? tc.steps.join('\n') : (tc.steps || ''),
              test_data: typeof tc.test_data === 'object' ? Object.entries(tc.test_data).map(([k, v]) => `${k}: ${v}`).join('\n') : (tc.test_data || ''),
              expected_result: Array.isArray(tc.expected_result) ? tc.expected_result.join('\n') : (tc.expected_result || ''),
              test_case_type: tc.test_case_type || ''
            };

            if (caseType === 'api_manual') {
              baseDetail.http_method = tc.http_method || '';
              baseDetail.header = typeof tc.header === 'object' ? Object.entries(tc.header).map(([k, v]) => `${k}: ${v}`).join('\n') : (tc.header || '');
              baseDetail.request_body = typeof tc.request_body === 'object' ? JSON.stringify(tc.request_body) : (tc.request_body || '');
            }

            detail[caseType] = baseDetail;

            await TestCase.create({
              suite_id: data.suite_id,
              feature_id: featureRecord._id,
              category_id: categoryRecord._id,
              check_item_id: checklistRecord._id,
              detail
            });
          }
        }
      }

    const tree = await mindmapBuilder.rebuildSnapshot(data.suite_id);

    // create test case version
    try {
      const userLogin = await getUserLogin(req);
      const testCases = await TestCaseRepository.findAll({ suite_id: data.suite_id });

      const testCaseVersion = new TestCaseVersion({
        suite_id: data.suite_id,
        created_by: userLogin._id,
        test_cases: testCases,
        mindmap: tree
      });

      await testCaseVersion.save();
      logger.info(`Đã tạo test case version ${testCaseVersion._id} cho suite ${data.suite_id}`);
    } catch (versionError) {
      logger.error('Lỗi khi tạo test case version', versionError, req.originalUrl);
    }

    return tree;

    }
  } catch (err) {
    logger.error('Lỗi khi tạo test case v2', err, req.originalUrl);
  }
}

exports.getMindmap = async (req, res) => {
  try {
    const { suite_id } = req.query;
    logger.info(`Lấy mindmap cho suite ${suite_id}`);

    if (!mongoose.Types.ObjectId.isValid(suite_id)) {
      return res.status(400).json({ error: 'suite_id không hợp lệ' });
    }
    const suite = await SuiteRepository.findById(suite_id);

    const loginUser = await getUserLogin(req);
    const userProjectRole = await userProjectService.getProjectRole(req, suite?.project_id);

    const isAdminOrManager = loginUser?.role === config.global_role.admin || 
                            loginUser?.role === config.global_role.test_manager;

    if (!isAdminOrManager && !userProjectRole) {
      return res.status(403).json({ error: 'Bạn không có quyền xem bộ test case này.' });
    }

    const snapshot = await MindmapSnapshotRepository.findOne({ suite_id: new mongoose.Types.ObjectId(suite_id) });
    if (snapshot && snapshot.tree) {
      
      return res.status(200).json({
        ...snapshot.tree,
        description: snapshot.description,
        title: snapshot.title,
        field_type: suite?.field_type,
        figma_url: suite?.figma_url,
        documents: {
          main: (suite?.documents?.main || []).map(doc => ({
            ...doc,
            url: getFileUrl(doc.path)
          })),
          sub: (suite?.documents?.sub || []).map(doc => ({
            ...doc,
            url: getFileUrl(doc.path)
          }))
        },
        images: (suite?.images || []).map(img => ({
          ...img,
          url: getFileUrl(img.path)
        }))
      });
    }
    const tree = await mindmapBuilder.rebuildSnapshot(suite_id);
    res.status(200).json(tree);
  } catch (err) {
    logger.error('Lỗi khi lấy mindmap', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}

exports.reviewTestCase = async (req, res) => {
  try {
    // Check DEV_MODE - return fake data if enabled
    const fakeResponse = await getFakeResponse('reviewTestCase');
    if (fakeResponse) {
      return res.status(200).json(fakeResponse);
    }

    const { suite_id } = req.body;
    if (!suite_id) {
      return res.status(400).json({ error: 'suite_id là bắt buộc' });
    }

    const testCases = await TestCase.find({ suite_id, del_flag: 0 }).sort({ _id: 1 }).lean();
    if (!testCases || testCases.length === 0) {
      return res.status(200).json({ test_case_id: [] });
    }

    const templatePath = path.join(__dirname, '../templates/gen_mindmap_prompt/review_testcase.ejs');
    const approvedIds = [];

    for (const tc of testCases) {
      const caseType = Object.keys(tc.detail || {})[0];
      const data = caseType ? (tc.detail[caseType] || {}) : {};

      const tcPayload = { ...data, _id: String(tc._id) };
      const prompt = await ejs.renderFile(templatePath, { tc_info: JSON.stringify(tcPayload) });

      const { text } = await openaiService.callOpenAiForExtract(prompt);
      const normalized = (text || '').trim().toLowerCase();
      const isTrue = normalized === 'true' || normalized.startsWith('true');
      if (isTrue) {
        approvedIds.push(String(tc._id));
      }
    }

    res.status(200).json({ test_case_id: approvedIds });
  } catch (err) {
    logger.error('Lỗi khi review test case', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}

exports.genAutoTest = async (req, res) => {
  try {
    // Check DEV_MODE - return fake data if enabled
    const fakeResponse = await getFakeResponse('genAutoTest');
    if (fakeResponse) {
      return res.status(200).json(fakeResponse);
    }

    const { suite_id, language, framework, design_pattern, test_case_id } = req.body;

    const suite = await SuiteRepository.findById(suite_id);
    if (!suite) {
      return res.status(400).json({ error: 'suite_id không tồn tại' });
    }

    if (!Array.isArray(test_case_id) || test_case_id.length === 0) {
      return res.status(400).json({ error: 'test_case_id phải là mảng chứa ít nhất 1 id' });
    }

    const { results, error } = await mindmapService.generateAutoTest({
      suite_id,
      language,
      framework,
      field_type: suite?.field_type || 'web',
      design_pattern,
      test_case_id
    });

    if (error) {
      return res.status(500).json({ error });
    }

    return res.status(200).json(results);
  } catch (err) {
    logger.error('Lỗi khi gen auto test', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}

exports.updateMindmap = async (req, res) => {
  try {
    const suite = await SuiteRepository.findById(req.body.suite_id);
    if (!suite) {
      return res.status(400).json({ error: 'suite_id không tồn tại' });
    }

    const tree = await updateMindmapService(req.body);
    
    return res.status(200).json(tree);
  } catch (err) {
    if (err instanceof MindmapUpdateError) {
      return res.status(err.status).json(err.payload);
    }

    logger.error('Lỗi khi update mindmap', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}

exports.checkMainDocument = (mainIndices, fieldName) => {
  const fieldnameMatch = fieldName.match(/files\[(\d+)\]/);
  const fileIndex = fieldnameMatch ? parseInt(fieldnameMatch[1]) : null;

  return mainIndices.includes(fileIndex);
}

exports.listFeature = async (req, res) => {
  try {
    const { suite_id } = req.query;

    if (!suite_id) {
      const features = await FeatureRepository.findAll({}, { sort: { order: 1, _id: 1 } });
      return res.status(200).json(features.map(feature => ({ _id: feature._id, name: feature.name })));
    }
    const features = await FeatureRepository.findAll({ suite_id }, { sort: { order: 1, _id: 1 } });
    return res.status(200).json(features.map(feature => ({ _id: feature._id, name: feature.name })));
  } catch (err) {
    logger.error('Lỗi khi lấy list feature', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
},

exports.listChecklist = async (req, res) => {
  try {
    const { feature_id, type, category_id } = req.query;
    if (!feature_id) {
      return res.status(400).json({ error: 'feature_id là bắt buộc' });
    }
    if (!type) {
      return res.status(400).json({ error: 'type là bắt buộc' });
    }
    if (type === 'category') {
      const checklists = await CheckItemRepository.findAll({ feature_id, type }, { sort: { order: 1, _id: 1 } });
      return res.status(200).json(checklists.map(checklist => ({ _id: checklist._id, name: checklist.name, type: checklist.type })));
    }
    if (type === 'checklist') {
      const checklists = await CheckItemRepository.findAll({ feature_id, type, parent_id: category_id }, { sort: { order: 1, _id: 1 } });
      return res.status(200).json(checklists.map(checklist => ({ _id: checklist._id, name: checklist.name, type: checklist.type })));
    }
  } catch (err) {
    logger.error('Lỗi khi lấy list checklists', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}

exports.getAutomationScripts = async (req, res) => {
  try {
    const { suite_id } = req.query;
    if (!suite_id) {
      return res.status(400).json({ error: 'suite_id là bắt buộc' });
    }
    const automationTestCases = await TestCaseRepository.findAll(
      { 
        suite_id, 
        type: 'automation',
        'automation_script.script': { $exists: true, $ne: null }
      },
      { sort: { createdAt: -1 } }
    );
    if (!automationTestCases || automationTestCases.length === 0) {
      return res.status(200).json([]);
    }

    const scripts = await Promise.all(automationTestCases.map(async (tc) => {
      let testCaseName = `Test Case ${tc._id}`;
      
      const detail = tc.detail || {};     
      if (tc.check_item_id) {
        try {
          const manualTestCases = await TestCaseRepository.findAll(
            { 
              suite_id: suite_id,
              check_item_id: tc.check_item_id,
              type: 'manual'
            },
            { sort: { createdAt: 1 } }
          );
          
          if (manualTestCases && manualTestCases.length > 0) {
            const manualTC = manualTestCases[0];
            const manualDetail = manualTC.detail || {};
            const manualCaseType = ['web_manual', 'mobile_manual', 'api_manual'].find(k => manualDetail && manualDetail[k]);
            if (manualCaseType && manualDetail[manualCaseType] && manualDetail[manualCaseType].test_case_name) {
              testCaseName = manualDetail[manualCaseType].test_case_name;
            }
          }
        } catch (err) {
          logger.error('Error finding manual test case:', err);
        }
      }

      return {
        _id: String(tc._id),
        test_case_name: testCaseName,
        automation_script: tc.automation_script,
        generated_time: tc.createdAt || tc.updatedAt
      };
    }));

    return res.status(200).json(scripts);
  } catch (err) {
    logger.error('Lỗi khi lấy automation scripts', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
}