// Core UserService interface
class UserService {
  async createUser(data) { throw new Error('Not implemented'); }
  async getUserByEmail(email) { throw new Error('Not implemented'); }
  async getUserById(id) { throw new Error('Not implemented'); }
  async listUsers() { throw new Error('Not implemented'); }
  async getUsersPaginated(filters, sort, skip, limit) { throw new Error('Not implemented'); }
  async countUsers(filters) { throw new Error('Not implemented'); }
  async updateUser(id, data) { throw new Error('Not implemented'); }
  async deleteUser(id) { throw new Error('Not implemented'); }
  async bulkUpdateUsers(userIds, updates) { throw new Error('Not implemented'); }
  async getUserStats() { throw new Error('Not implemented'); }
  async searchUsers(searchTerm, filters) { throw new Error('Not implemented'); }
  async getUsersByRole(role) { throw new Error('Not implemented'); }
  async getUsersByStatus(status) { throw new Error('Not implemented'); }
}
module.exports = UserService;
