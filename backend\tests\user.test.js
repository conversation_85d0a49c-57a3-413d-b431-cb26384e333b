const request = require('supertest');
const mongoose = require('mongoose');
const { MongoMemoryServer } = require('mongodb-memory-server');
const app = require('../app');
const User = require('../models/user');

let mongoServer;

beforeAll(async () => {
  mongoServer = await MongoMemoryServer.create();
  await mongoose.connect(mongoServer.getUri(), { dbName: 'test' });
});

afterAll(async () => {
  await mongoose.disconnect();
  await mongoServer.stop();
});

describe('User API', () => {
  let adminToken, userToken, adminId, userId;

  it('should register an admin', async () => {
    const res = await request(app)
      .post('/users/register')
      .send({ name: 'Admin', email: '<EMAIL>', password: 'admin123', role: 'admin' });
    expect(res.statusCode).toBe(201);
    expect(res.body.user.role).toBe('admin');
    adminId = res.body.user._id;
  });

  it('should login as admin and get token', async () => {
    const res = await request(app)
      .post('/users/login')
      .send({ email: '<EMAIL>', password: 'admin123' });
    expect(res.body.token).toBeDefined();
    adminToken = res.body.token;
  });

  it('should register a user', async () => {
    const res = await request(app)
      .post('/users/register')
      .send({ name: 'User', email: '<EMAIL>', password: 'user123', role: 'user' });
    expect(res.statusCode).toBe(201);
    userId = res.body.user._id;
  });

  it('should login as user and get token', async () => {
    const res = await request(app)
      .post('/users/login')
      .send({ email: '<EMAIL>', password: 'user123' });
    expect(res.body.token).toBeDefined();
    userToken = res.body.token;
  });

  it('should not allow non-admin to get users', async () => {
    const res = await request(app)
      .get('/users')
      .set('Authorization', `Bearer ${userToken}`);
    expect(res.statusCode).toBe(403);
  });

  it('should allow admin to get users', async () => {
    const res = await request(app)
      .get('/users')
      .set('Authorization', `Bearer ${adminToken}`);
    expect(res.statusCode).toBe(200);
    expect(Array.isArray(res.body)).toBe(true);
  });

  it('should update a user as admin', async () => {
    const res = await request(app)
      .put(`/users/${userId}`)
      .set('Authorization', `Bearer ${adminToken}`)
      .send({ name: 'User Updated' });
    expect(res.statusCode).toBe(200);
    expect(res.body.message).toMatch(/updated/i);
    expect(res.body.user.name).toBe('User Updated');
  });

  it('should delete a user as admin', async () => {
    const res = await request(app)
      .delete(`/users/${userId}`)
      .set('Authorization', `Bearer ${adminToken}`);
    expect(res.statusCode).toBe(200);
  });

  it('should validate registration (missing email)', async () => {
    const res = await request(app)
      .post('/users/register')
      .send({ name: 'NoEmail', password: 'test' });
    expect(res.statusCode).toBe(400);
  });
});
