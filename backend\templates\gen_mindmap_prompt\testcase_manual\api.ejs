<%
const coverageItems = [];
if (template.coverage.positive_case) { coverageItems.push("Positive Cases: <PERSON><PERSON><PERSON> request với dữ liệu hợp lệ, kiểm tra status code 2xx và response body/headers chính xác."); }
if (template.coverage.negative_case) { coverageItems.push("Negative Cases:"); }
if (template.coverage.input_validation) { coverageItems.push("Input Validation: Dữ liệu không hợp lệ (sai ki<PERSON><PERSON>, sai đ<PERSON>nh dạng, ngo<PERSON>i phạm vi, thiếu trư<PERSON> b<PERSON><PERSON> bu<PERSON>, thừa trường không mong muốn, gi<PERSON> trị null/rỗng không hợp lệ) cho tất cả các nguồn input (Query Params, Path Params, Headers, Body)."); }
if (template.coverage.authentication) { coverageItems.push("Authentication: <PERSON>hiế<PERSON> thông tin xác thực, sai thông tin, token hết hạn/không hợp lệ."); }
if (template.coverage.authorization) { coverageItems.push("Authorization: User không có quyền truy cập endpoint hoặc resource cụ thể."); }
if (template.coverage.business_logic) { coverageItems.push("Business Logic Errors: Các trường hợp vi phạm quy tắc nghiệp vụ."); }
if (template.coverage.status_code) { coverageItems.push("Kiểm tra Status Codes lỗi (4xx, 5xx): Đảm bảo trả về đúng mã lỗi và thông báo lỗi có ý nghĩa trong response body."); }
if (template.coverage.boundary_value_analysis) { coverageItems.push("Boundary Value Analysis (BVA): Kiểm tra các giá trị biên cho các trường số, giới hạn độ dài chuỗi, giới hạn số lượng phần tử trong mảng (áp dụng cho Query Params, Path Params, Body)."); }
if (template.coverage.schema_validation) { coverageItems.push("Schema Validation: Đảm bảo cấu trúc response body (JSON, XML) khớp với định nghĩa/schema mong đợi (cả trường hợp thành công và lỗi)."); }
if (template.coverage.header_testing) { coverageItems.push("Header Testing: Kiểm tra request với header thiếu/thừa/sai; Kiểm tra các response header quan trọng (ví dụ: Content-Type, Cache-Control, RateLimit-Remaining)."); }
if (template.coverage.edge_corner_case) { coverageItems.push("Edge Cases/Corner Cases: Các tình huống đặc biệt (ví dụ: payload rất lớn, ký tự đặc biệt/unicode trong input, gọi API liên tục để kiểm tra rate limiting cơ bản...). Yêu cầu AI suy luận và tạo ra ít nhất 10 edge cases khác nhau."); }
%>

Bạn là QA Automation Engineer. Với mỗi checklist thuộc tính năng dưới đây, hãy sinh ra tối đa 5 test case chi tiết (mỗi test case có ghi từng bước nên làm như nào) cho nền tảng <%- test_platform %>.
1.  Định dạng Output: Mỗi TC cần có các trường sau:
  •   test_case_id: (Định dạng: TênEndpoint_API_XXX)
  •   test_case_name: (Mô tả ngắn gọn mục tiêu của TC)
  •   priority: (High/Medium/Low)
  •   pre_condition: (Ví dụ: Có token xác thực hợp lệ với quyền Y)
  •   http_method: (Bao gồm Base URL, Path, Query Params)
  •   header: (Chi tiết các header và giá trị cần gửi)
  •   request_body: (Chi tiết payload JSON/XML... cần gửi, nếu có)
  •   steps: (Chủ yếu là: Gửi request với thông tin trên)
  •   expected_result: (Bao gồm: Mã trạng thái HTTP mong đợi, Mô tả Response Body mong đợi - có thể là cấu trúc chính, giá trị cụ thể hoặc yêu cầu validate schema, Các Response Headers quan trọng cần kiểm tra nếu có)
  •   test_case_type: (Ví dụ: Positive, Negative-Input, Negative-Auth, Boundary, Schema, Edge)

2. Độ bao phủ: Bộ TCs phải bao phủ toàn diện các trường hợp, bao gồm: 
    <%- coverageItems.join('\r\n    ') %>
    
3. Ngôn ngữ: <%- language %>

4. YÊU CẦU OUTPUT FORMAT:
  • Đầu ra phải là JSON hợp lệ 100% theo chuẩn RFC 8259 (không có code, không có comment)
  • Sử dụng UTF-8 encoding cho ký tự tiếng Việt
  • Không sử dụng ký hiệu comment hoặc ghi chú trong JSON.

5. Trả về JSON:
{
  "categories": [
    {
      "_id": "ObjectId",
      "name": "string",
      "checklists": [
        {
          "_id": "ObjectId",
          "name": "string",
          "rationale": "string",
          "test_cases": [
            {
              "_id": "ObjectId",
              "test_case_id": "string",
              "test_case_name": "string",
              "priority": "High|Medium|Low",
              "pre_condition": ["string"],
              "http_method": "string",
              "header": "string",
              "request_body": "string",
              "steps": "Gửi request với thông tin trên",
              "expected_result": "Mã trạng thái HTTP mong đợi, Mô tả Response Body mong đợi, Các Response Headers quan trọng cần kiểm tra nếu có",
              "test_case_type": "Positive|Negative-Input|Negative-Auth|Boundary|Schema|Edge"
            }
          ]
        }
      ]
    }
  ]
}

--- THÔNG TIN TÍNH NĂNG ---
<%- JSON.stringify(featureInput, null, 2) %>


