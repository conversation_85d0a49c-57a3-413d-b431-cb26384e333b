const Joi = require('joi');

exports.createTemplateSchema = Joi.object({
    type: Joi.string().required(),
    name: Joi.string().required().messages({
        'string.empty': 'Tên cấu hình không được để trống',
    }),
    // language_prompt: Joi.string().required().messages({
    //     'string.empty': 'Ngôn ngữ không được để trống',
    // }),


    // language: Joi.string()
    // .when('type', {
    //   is: 2,
    //   then: Joi.required().messages({
    //     'string.empty': 'Ngôn ngữ và công cụ không được để trống',
    //   }),
    //   otherwise: Joi.optional(),
    // }),

    // design_pattern: Joi.string()
    // .when('type', {
    //   is: 2,
    //   then: Joi.required().messages({
    //     'string.empty': 'Design pattern không được để trống',
    //   }),
    //   otherwise: Joi.optional(),
    // }),

    // framework: Joi.string()
    // .when('type', {
    //   is: 2,
    //   then: Joi.required().messages({
    //     'string.empty': 'Framework không được để trống',
    //   }),
    //   otherwise: Joi.optional(),
    // }),

}).unknown(true);

exports.updateTemplateSchema = Joi.object({
    type: Joi.string().optional(),
    name: Joi.string().optional().messages({
        'string.empty': 'Tên cấu hình không được để trống',
    }),
    // language_prompt: Joi.string().optional().messages({
    //     'string.empty': 'Ngôn ngữ không được để trống',
    // }),
    // language: Joi.string()
    //     .when('type', {
    //         is: 2,
    //         then: Joi.optional().messages({
    //             'string.empty': 'Ngôn ngữ và công cụ không được để trống',
    //         }),
    //         otherwise: Joi.optional(),
    //     }),
    // design_pattern: Joi.string()
    //     .when('type', {
    //         is: 2,
    //         then: Joi.optional().messages({
    //             'string.empty': 'Design pattern không được để trống',
    //         }),
    //         otherwise: Joi.optional(),
    //     }),
    // framework: Joi.string()
    //     .when('type', {
    //         is: 2,
    //         then: Joi.optional().messages({
    //             'string.empty': 'Framework không được để trống',
    //         }),
    //         otherwise: Joi.optional(),
    //     }),
}).unknown(true);