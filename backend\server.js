const mongoose = require('mongoose');
const config = require('./config');
const app = require('./app');

// Connect to MongoDB using the new configuration
mongoose.connect(config.mongo.uri, config.mongo.options)
  .then(() => {
    console.log(`MongoDB connected successfully`);
    console.log(`Database: ${config.dbName}`);
    console.log(`Environment: ${config.nodeEnv}`);
    if (config.useAtlas) {
      console.log('Using MongoDB Atlas (cloud)');
    } else {
      console.log('Using local MongoDB');
    }
  })
  .catch((err) => {
    console.error('MongoDB connection error:', err);
    process.exit(1);
  });

// Handle MongoDB connection events
mongoose.connection.on('error', (err) => {
  console.error('MongoDB connection error:', err);
});

mongoose.connection.on('disconnected', () => {
  console.log('MongoDB disconnected');
});

// Graceful shutdown
process.on('SIGINT', async () => {
  try {
    await mongoose.connection.close();
    console.log('MongoDB connection closed through app termination');
    process.exit(0);
  } catch (err) {
    console.error('Error during shutdown:', err);
    process.exit(1);
  }
});

app.listen(config.port, () => {
  console.log(`Server running on http://localhost:${config.port}`);
  console.log(`Environment: ${config.nodeEnv}`);
});
