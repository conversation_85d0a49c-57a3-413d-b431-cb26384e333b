// Simple validation middleware factory
module.exports = (schema) => async (req, res, next) => {
  // Trim string fields before validation
  if (req.body) {
    Object.keys(req.body).forEach(key => {
      if (typeof req.body[key] === 'string') {
        req.body[key] = req.body[key].trim();
      }
    });
  }
  
  try {
    // Validate with context for external validators
    await schema.validateAsync(req.body, { 
      abortEarly: false,
      context: req.body
    });
    next();
  } catch (error) {
    if (error.isJoi && error.details) {
      const errors = error.details.reduce((acc, item) => {
        const field = item.path[0] || 'field';
        if (!acc[field]) {
          acc[field] = [];
        }
        acc[field].push(item.message);
        return acc;
      }, {});
      return res.status(400).json({ 
        message: 'Validation failed',
        errors 
      });
    } else {
      // Fallback for unexpected errors
      return res.status(400).json({ 
        message: 'Validation failed',
        errors: {
          general: [error.message || 'Validation failed']
        }
      });
    }
  }
};
