const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');

const { Schema } = mongoose;

const MindmapSnapshotSchema = new Schema({
  suite_id: { type: Schema.Types.ObjectId, ref: 'suite', unique: true, required: true },
  tree: { type: Object, required: true },
  title: { type: String, required: true },
  description: { type: String, required: false },
  rebuilt_at: { type: Date, default: Date.now },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

MindmapSnapshotSchema.plugin(softDeletePlugin);

MindmapSnapshotSchema.index({ suite_id: 1 });

module.exports = mongoose.model('mindmap_snapshot', MindmapSnapshotSchema);


