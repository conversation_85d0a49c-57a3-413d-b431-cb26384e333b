# Backend

## Overview
Node.js + Express + MongoDB backend for flexible, professional fullstack app.

## Features
- Modular architecture (core, adapters, services)
- JWT authentication with email
- Role-based permissions
- Validation middleware
- Easy config via `.env`
- **MongoDB Atlas cloud support** - Easy switching between local and cloud databases

## Flexibility Points
- Swap database/storage adapters (MongoDB, S3, GCS, etc.)
- Pluggable auth providers
- Configurable via environment variables
- **Flexible database deployment** - Local MongoDB or MongoDB Atlas cloud

## Directory Structure
- `core/` – abstractions & interfaces
- `adapters/` – implementations for storage, auth, etc.
- `services/` – business logic
- `routes/` – API endpoints
- `models/` – Mongoose schemas
- `controllers/` – request handlers
- `middleware/` – auth, validation, etc.

## Database Configuration

### Local MongoDB (Default)
For local development, the app uses a local MongoDB instance:

```bash
# Set in your .env file
USE_MONGO_ATLAS=false
MONGO_URI=mongodb://localhost:27017/gentest
```

### MongoDB Atlas (Cloud)
To use MongoDB Atlas cloud database:

1. **Set up MongoDB Atlas:**
   - Create a free account at [MongoDB Atlas](https://www.mongodb.com/atlas)
   - Create a new cluster
   - Get your connection string from the cluster

2. **Configure environment variables:**
   ```bash
   # Set in your .env file
   USE_MONGO_ATLAS=true
   MONGO_ATLAS_URI=mongodb+srv://username:<EMAIL>/gentest?retryWrites=true&w=majority
   ```

3. **Environment Variables:**
   - `USE_MONGO_ATLAS` - Set to `true` for Atlas, `false` for local
   - `MONGO_URI` - Local MongoDB connection string
   - `MONGO_ATLAS_URI` - MongoDB Atlas connection string
   - `DB_NAME` - Database name (optional, defaults to 'gentest')

## Getting Started
1. Copy `env.example` to `.env` and fill in your values.
2. Choose your database setup (local or Atlas)
3. `npm install`
4. `npm run dev`

## Running Automated Tests

This project uses **jest**, **supertest**, and an in-memory MongoDB for API testing.

- To run all backend tests:

```sh
npm test
```

- Tests are in `tests/` and cover registration, login, CRUD, roles, and validation.
- The test DB is spun up automatically (no need for a real MongoDB instance).

### Adding/Extending Tests
- Add new test files in `tests/` (e.g., `feature.test.js`).
- Use the existing `user.test.js` as a template for new features.
- All new features should have corresponding tests to ensure reliability and easy onboarding for future developers.

## Deployment

### Local Development
```bash
USE_MONGO_ATLAS=false
MONGO_URI=mongodb://localhost:27017/gentest
```

### Production with Atlas
```bash
USE_MONGO_ATLAS=true
MONGO_ATLAS_URI=mongodb+srv://username:<EMAIL>/gentest?retryWrites=true&w=majority
NODE_ENV=production
JWT_SECRET=your-production-secret-key
```
