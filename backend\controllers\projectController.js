const mongoose = require('mongoose');
const ProjectRepository = require('../repositories/projectRepository');
const SuiteRepository = require('../repositories/suiteRepository');
const logger = require('../services/core/loggerService');
const config = require('../config/config');
const userProjectService = require('../services/userProjectService');
const userProjectRepository = require('../repositories/userProjectRepository');
const userRepository = require('../repositories/userRepository');
const projectJoinRequestRepository = require('../repositories/projectJoinRequestRepository');
const common = require('../utils/common');
exports.createProject = async (req, res) => {
  try {
    const userLogin = await common.getUserLogin(req);
    const projectCreatorRoles = config?.user?.project_full_access_roles || ['admin', 'test_manager'];

    if (!userLogin || !projectCreatorRoles.includes(userLogin.role)) {
      return res.status(403).json({ error: 'Bạn không có quyền tạo dự án' });
    }

    const {
      name,
      description = '',
      display_mode = 'private'
    } = req.body || {};

    if (!name) {
      return res.status(400).json({ error: 'Tên dự án là bắt buộc' });
    }

    if (!config.project.display_mode.includes(display_mode)) {
      return res.status(400).json({ error: 'Chế độ hiển thị không hợp lệ' });
    }

    const projectPayload = {
      name,
      description,
      display_mode,
      created_by: String(userLogin._id)
    };

    const project = await ProjectRepository.create(projectPayload);

    res.status(201).json(project);
  } catch (err) {
    logger.error('Lỗi khi tạo dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.getProjects = async (req, res) => {
  try {
    const userLogin = await common.getUserLogin(req);
    const options = {};

    if (userLogin) {
      options.userId = userLogin._id;
      options.onlyUserProjects = userLogin.role === 'user';
    }

    const projects = await ProjectRepository.findAllWithTestSuiteCount(options);

    if (userLogin && (userLogin.role === config.global_role.admin || userLogin.role === config.global_role.test_manager)) {
      const role = userLogin.role;
      const projectsWithRole = projects.map(project => ({
        ...project,
        project_role: role
      }));
      return res.json(projectsWithRole);
    }

    res.json(projects);
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.filterProject = async (req, res) => {
  try {
    const { display_mode, project_name } = req.query || {};

    if (display_mode && !config.project.display_mode.includes(display_mode)) {
      return res.status(400).json({ error: 'Chế độ hiển thị không hợp lệ' });
    }

    const userLogin = await common.getUserLogin(req);
    const filters = {};

    if (display_mode) {
      filters.display_mode = display_mode;
    }

    const projectName = project_name?.trim();
    if (projectName) {
      filters.name = { $regex: projectName, $options: 'i' };
    }

    const options = {
      filters
    };

    if (userLogin) {
      options.userId = userLogin._id;
    }

    const projects = await ProjectRepository.findAllWithTestSuiteCountByFilters(options);

    if (userLogin && (userLogin.role === config.global_role.admin || userLogin.role === config.global_role.test_manager)) {
      const role = userLogin.role;
      const projectsWithRole = projects.map(project => ({
        ...project,
        project_role: role
      }));
      return res.json(projectsWithRole);
    }

    return res.json(projects);
  } catch (err) {
    logger.error('Lỗi khi lọc dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.updateProject = async (req, res) => {
  const { id } = req.params;
  try {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ error: 'Dự án không hợp lệ' });
    }

    const userLogin = await common.getUserLogin(req);
    if (!userLogin) {
      return res.status(401).json({ error: 'Bạn chưa đăng nhập' });
    }

    const privilegedRoles = config.user.project_full_access_roles;
    let canUpdate = privilegedRoles.includes(userLogin.role);

    if (!canUpdate) {
      const roleByProject = await userProjectService.checkRoleByProject(req, id);
      canUpdate = roleByProject?.project_role === config.project_role.leader;
    }

    if (!canUpdate) {
      return res.status(403).json({ error: 'Bạn không có quyền cập nhật dự án' });
    }

    const {
      name,
      description = '',
      display_mode = 'private'
    } = req.body || {};

    if (!name) {
      return res.status(400).json({ error: 'Tên dự án là bắt buộc' });
    }

    if (!config.project.display_mode.includes(display_mode)) {
      return res.status(400).json({ error: 'Chế độ hiển thị không hợp lệ' });
    }

    const updatePayload = {
      name,
      description,
      display_mode
    };

    const project = await ProjectRepository.update(
      id,
      updatePayload,
      { del_flag: { $ne: 1 } }
    );

    if (!project) {
      return res.status(404).json({ error: 'Dự án không tồn tại' });
    }

    res.json(project);
  } catch (err) {
    logger.error('Lỗi khi cập nhật dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.deleteProject = async (req, res) => {
  const { id } = req.params;
  try {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ error: 'Dự án không hợp lệ' });
    }

    const userLogin = await common.getUserLogin(req);
    if (!userLogin) {
      return res.status(401).json({ error: 'Bạn chưa đăng nhập' });
    }

    const allowedRoles = config.user.project_full_access_roles;
    if (!allowedRoles.includes(userLogin.role)) {
      return res.status(403).json({ error: 'Bạn không có quyền xóa dự án' });
    }

    const project = await ProjectRepository.delete(
      id,
      { del_flag: { $ne: 1 } }
    );

    if (!project) {
      return res.status(404).json({ error: 'Dự án không tồn tại' });
    }

    await userProjectRepository.deleteMany({ project_id: id });
    await SuiteRepository.deleteMany({ project_id: id, del_flag: 0 });

    res.json({ message: 'Xóa thành công' });
  } catch (err) {
    logger.error('Lỗi khi xóa dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.getProjectDetail = async (req, res) => {
  const { id } = req.params;
  try {
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return res.status(400).json({ error: 'Dự án không hợp lệ' });
    }

    const { role: globalRole, project_role: projectRole } = await userProjectService.checkRoleByProject(req, id);

    const privilegedRoles = config?.user?.project_full_access_roles || [];
    const isPrivileged = privilegedRoles.includes(globalRole);
    const canView = isPrivileged || Boolean(projectRole);

    if (!canView) {
      return res.status(403).json({ error: 'Bạn không có quyền xem dự án này' });
    }

    const project = await ProjectRepository.findOne({
      _id: id,
      del_flag: { $ne: 1 }
    });

    if (!project) {
      return res.status(404).json({ error: 'Dự án không tồn tại' });
    }

    const projectRoleForUser = projectRole || (isPrivileged ? globalRole : null);

    res.json({
      ...project,
      project_role: projectRoleForUser
    });
  } catch (err) {
    logger.error('Lỗi khi lấy chi tiết dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.addUserToProject = async (req, res) => {
  const { project_id, user_id, project_role } = req.body;
  try {
    const project = await ProjectRepository.findById(project_id);
    if (!project) return res.status(404).json({ error: 'Dự án không tồn tại' });

    const user = await userRepository.findById(user_id);
    if (!user) return res.status(404).json({ error: 'Người dùng không tồn tại' });

    if (!config.user.project_role.includes(project_role)) {
      return res.status(400).json({ error: 'Vai trò không hợp lệ' });
    }

    const existingUserProject = await userProjectService.checkUserInProject(project_id, user_id);
    if (existingUserProject) return res.status(400).json({ error: 'Người dùng đã được thêm vào dự án' });

    const roleByProject = await userProjectService.checkRoleByProject(req, project_id);
    const isManager = config.user.project_full_access_roles.includes(roleByProject?.role);
    if (!isManager && roleByProject?.project_role !== config.project_role.leader) {
      return res.status(400).json({ error: 'Bạn không có quyền thêm người dùng vào dự án' });
    }

    if (!isManager && project_role === config.project_role.leader) {
      return res.status(400).json({ error: 'Bạn không có quyền thêm leader vào dự án' });
    }

    const userProject = await userProjectRepository.create({
      project_id,
      user_id,
      project_role
    });
    res.status(201).json(userProject);
  } catch (err) {
    logger.error('Lỗi khi thêm người dùng vào dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.removeUserProject = async (req, res) => {
  const { user_project_id } = req.body;
  try {
    const userProject = await userProjectRepository.findById(user_project_id);
    if (!userProject) return res.status(404).json({ error: 'Không tìm thấy người dùng trong dự án' });

    const { role, project_role } = await userProjectService.checkRoleByProject(req, userProject.project_id) || {};
    const isManager = config.user.project_full_access_roles.includes(role);

    if (!isManager && project_role !== config.project_role.leader)
      return res.status(400).json({ error: 'Không có quyền xóa thành viên khỏi dự án' });

    if (!isManager && userProject.project_role === config.project_role.leader)
      return res.status(400).json({ error: 'Không có quyền xóa leader khỏi dự án' });

    await userProjectRepository.delete(user_project_id);
    res.status(200).json({ message: 'Xóa thành viên khỏi dự án thành công' });
  } catch (err) {
    logger.error('Lỗi xóa thành viên khỏi dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.changeUserProjectRole = async (req, res) => {
  const { user_project_id, project_role } = req.body;
  try {
    const userProject = await userProjectRepository.findById(user_project_id);
    if (!userProject) return res.status(404).json({ error: 'Không tìm thấy người dùng' });
    if (!config.user.project_change_role_allowed.includes(project_role))
      return res.status(400).json({ error: 'Vai trò không hợp lệ' });

    const { role, project_role: myRole } = await userProjectService.checkRoleByProject(req, userProject.project_id) || {};
    const isManager = config.user.project_full_access_roles.includes(role);

    if (!isManager && myRole !== config.project_role.leader)
      return res.status(400).json({ error: 'Không có quyền thay đổi vai trò thành viên trong dự án' });

    if (!isManager && (userProject.project_role === config.project_role.leader || project_role === config.project_role.leader))
      return res.status(400).json({ error: 'Không có quyền thay đổi vai trò leader' });

    await userProjectRepository.update(user_project_id, { project_role });
    res.json({ message: 'Đổi vai trò thành công' });
  } catch (err) {
    logger.error('Lỗi đổi vai trò', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.requestJoinProject = async (req, res) => {
  const { project_id, requested_role, message } = req.body;
  try {
    if (!mongoose.Types.ObjectId.isValid(project_id)) {
      return res.status(400).json({ error: 'Dự án không hợp lệ' });
    }
    const project = await ProjectRepository.findOne({ _id: project_id, display_mode: 'internal' });
    if (!project) return res.status(404).json({ error: 'Dự án không tồn tại hoặc không hợp lệ' });

    if (!config.user.project_join_request_allowed.includes(requested_role)) {
      return res.status(400).json({ error: 'Vai trò không hợp lệ' });
    }

    const userLogin = await common.getUserLogin(req);
    if (!userLogin) return res.status(401).json({ error: 'Bạn chưa đăng nhập' });


    const userProject = await userProjectService.checkUserInProject(project_id, userLogin._id);
    if (userProject || config.user.project_full_access_roles.includes(userLogin.role)) {
      return res.status(400).json({ error: 'Bạn đã tham gia dự án này' });
    }

    const userRequestJoinProject = await userProjectService.checkUserRequestJoinProject(project_id, userLogin._id);
    if (userRequestJoinProject) return res.status(400).json({ error: 'Bạn đã gửi yêu cầu tham gia dự án này' });

    const projectJoinRequest = await projectJoinRequestRepository.create({ project_id, user_id: userLogin._id, requested_role, message });
    if (!projectJoinRequest) return res.status(500).json({ error: 'Lỗi khi gửi yêu cầu tham gia dự án' });

    res.status(200).json({ message: 'Yêu cầu tham gia dự án đã được gửi thành công' });
  } catch (err) {
    logger.error('Lỗi khi yêu cầu tham gia dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.getProjectJoinRequests = async (req, res) => {
  try {
    const userLogin = await common.getUserLogin(req);
    if (!userLogin) {
      return res.status(401).json({ error: 'Bạn chưa đăng nhập' });
    }

    let filter = { status: 'pending' };

    const isManager = config.user.project_full_access_roles.includes(userLogin.role);

    if (!isManager) {
      const leaderProjects = await userProjectRepository.findAll({
        user_id: userLogin._id,
        project_role: config.project_role.leader,
        del_flag: { $ne: 1 }
      });

      const projectIds = leaderProjects.map(p => p.project_id);

      if (projectIds.length === 0) {
        return res.status(200).json([]);
      }

      filter.project_id = { $in: projectIds };
    }

    const requests = await projectJoinRequestRepository.model.find(filter)
      .populate('user_id', 'name email')
      .populate('project_id', 'name')
      .lean();

    const formattedRequests = requests.map(req => ({
      user_id: req.user_id?._id,
      project_id: req.project_id?._id,
      user_name: req.user_id?.name,
      user_email: req.user_id?.email,
      project_name: req.project_id?.name,
      requested_role: req.requested_role,
      project_join_request_id: req._id,
      message: req.message
    }));

    const validRequests = formattedRequests.filter(req => req.user_id && req.project_id);

    res.status(200).json(validRequests);
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách yêu cầu tham gia dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.approveProjectJoinRequest = async (req, res) => {
  const { project_join_request_id, status } = req.body;
  try {
    if (!config.project_join_request_status_action.includes(status)) {
      return res.status(400).json({ error: 'Trạng thái không hợp lệ' });
    }

    const projectJoinRequest = await projectJoinRequestRepository.findById(project_join_request_id);
    if (!projectJoinRequest) return res.status(404).json({ error: 'Yêu cầu tham gia dự án không tồn tại' });

    if (projectJoinRequest.status !== config.project_join_request_status.pending) {
      return res.status(400).json({ error: 'Yêu cầu này đã được xử lý' });
    }

    await projectJoinRequestRepository.update(project_join_request_id, { status });

    if (status === config.project_join_request_status.approved) {
      const existingUserProject = await userProjectService.checkUserInProject(projectJoinRequest.project_id, projectJoinRequest.user_id);
      if (existingUserProject) return res.status(400).json({ error: 'Người dùng đã tham gia dự án này' });
      await userProjectRepository.create({
        project_id: projectJoinRequest.project_id,
        user_id: projectJoinRequest.user_id,
        project_role: projectJoinRequest.requested_role
      });
      return res.status(200).json({ message: 'Yêu cầu tham gia dự án đã được phê duyệt thành công' });
    }

    return res.status(200).json({ message: 'Yêu cầu tham gia dự án đã bị từ chối' });
  } catch (err) {
    logger.error('Lỗi khi phê duyệt yêu cầu tham gia dự án', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};