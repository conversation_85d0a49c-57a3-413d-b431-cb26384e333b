require('dotenv').config();
const OpenAI = require('openai');
const logger = require('./core/loggerService');
const fs = require('fs');
const path = require('path');
const ejs = require('ejs');
const { callOpenAiForExtract, callOpenAiForExtractWithImages, parseJsonSafely, callChatGpt } = require('./core/openaiService');
const fileService = require('./core/fileService');
const SuiteRepository = require('../repositories/suiteRepository');
const FeatureRepository = require('../repositories/featureRepository');
const CheckItemRepository = require('../repositories/checkItemRepository');
const mindmapBuilder = require('./mindmapBuilder');
const { ObjectId } = require('mongodb');
const config = require('../config');
const TestCaseRepository = require('../repositories/testCaseRepository');

class MindmapUpdateError extends Error {
  constructor(status, payload) {
    super(payload?.error || 'Mindmap update error');
    this.status = status;
    this.payload = payload;
  }
}

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

async function buildExtractPromptFromFiles(files) {
  const fileContents = await readFileContent(files);
  const combinedText = fileContents
    .map((f) => `File: ${f.name}\n\n${f.content}`)
    .join('\n\n---\n\n');

  let templateFileName = 'gen_feature.ejs';
  if (process.env.DEV_MODE === 'true') {
    templateFileName = 'gen_feature_dev.ejs';
  }

  const templatePath = path.join(__dirname, '../templates/gen_mindmap_prompt/', templateFileName);
  const templateString = await ejs.renderFile(templatePath, {}, { async: false });

  const prompt = `${templateString}\n\n\n--- NỘI DUNG CÁC FILE (kèm tên file) ---\n${combinedText}`;
  return prompt;
}

async function buildInputDataForChecklist(suite_id) {
  const features = await FeatureRepository.findAll({ suite_id, del_flag: 0 }, { sort: { order: 1, _id: 1 } });

  const processedFeatures = features.map(feature => {
    const categories = (config.mindmap?.categories || []).map(category => ({
      name: category,
      check_list: []
    }));

    return {
      _id: feature._id,
      name: feature.name,
      description: feature.description || '',
      source_files: Array.isArray(feature.source_files) ? feature.source_files : [],
      categories,
      merged_from: feature.merged_from || [],
      permissions: feature.permissions || {},
      ui_components: feature.ui_components || {}
    };
  });

  return processedFeatures;
}


async function buildPromptForChecklist(suite_id) {
  const features = await FeatureRepository.findAll({ suite_id, del_flag: 0 }, { sort: { order: 1, _id: 1 } });

  const processedFeatures = features.map(feature => {
    const categories = (config.mindmap?.categories || []).map(category => ({
      _id: new ObjectId(),
      name: category,
      check_list: []
    }));

    return {
      _id: feature._id,
      name: feature.name,
      description: feature.description || '',
      source_files: Array.isArray(feature.source_files) ? feature.source_files : [],
      categories
    };
  });

  const templatePath = path.join(__dirname, '../templates/gen_mindmap_prompt/gen_checklist.ejs');
  const templateString = await ejs.renderFile(templatePath, {}, { async: false });

  const prompt = `${templateString}\n\n\n--- NỘI DUNG CÁC FEATURE ---\n${JSON.stringify(processedFeatures, null, 2)}`;
  logger.info('prompt gen checklist', prompt);
  return prompt;
}

async function generateChecklist(suite_id) {
  try {
    const prompt = await buildPromptForChecklist(suite_id);

    const { id, text: result } = await callOpenAiForExtract(prompt);

    const checklist = typeof result === 'string' ? parseJsonSafely(result) : result;
    return { id, checklist };
  } catch (error) {
    logger.error('Error in generateChecklist', error);
    return { id: null, text: '', error: 'Failed to generate checklist' };
  }
}

async function buildPromptForTestCaseOneFeature(data, feature) {
  const { suite_id, prompt_language, field_type, coverage } = data;

  const test_platform = field_type ? `${field_type}` : (field_type || 'web');

  const templateData = {
    template: {
      coverage: coverage || {}
    },
    language: prompt_language || 'tiếng Anh',
    test_platform
  };

  const categories = await CheckItemRepository.findAll({ suite_id, feature_id: feature._id, type: 'category', del_flag: 0 }, { sort: { order: 1, _id: 1 } });
  const catIds = categories.map(c => c._id);
  const checklists = await CheckItemRepository.findAll({ suite_id, parent_id: { $in: catIds }, type: 'checklist', del_flag: 0 }, { sort: { order: 1, _id: 1 } });

  const checklistsByCategory = checklists.reduce((acc, cl) => {
    (acc[cl.parent_id] ||= []).push({
      _id: String(cl._id),
      name: cl.name,
      rationale: cl.rationale || ''
    });

    return acc;
  }, {});

  const featureInput = {
    _id: String(feature._id),
    name: feature.name,
    description: feature.description || '',
    source_files: Array.isArray(feature.source_files) ? feature.source_files : [],
    categories: categories.map(cat => ({
      _id: String(cat._id),
      name: cat.name,
      checklists: checklistsByCategory[cat._id] || []
    }))
  };

  const templateName = data.field_type === 'api' ? 'api.ejs' : 'web.ejs';
  const templatePath = path.join(__dirname, `../templates/gen_mindmap_prompt/testcase_manual/${templateName}`);
  const templateString = await ejs.renderFile(templatePath, { ...templateData, featureInput }, { async: false });

  const prompt = templateString;
  return prompt;
}

async function generateTestCaseForFeature(data, feature) {
  try {
    const prompt = await buildPromptForTestCaseOneFeature(data, feature);
    const { id, text: result } = await callOpenAiForExtract(prompt);
    const parsed = typeof result === 'string' ? parseJsonSafely(result) : result;
    let categories = [];

    if (parsed && Array.isArray(parsed.categories)) {
      categories = parsed.categories;
    } else if (Array.isArray(parsed)) {
      categories = parsed;
    }

    return { id, categories };
  } catch (error) {
    logger.error('Error in generateTestCaseForFeature', error);
    return { id: null, categories: [], error: 'Failed to generate test case for feature' };
  }
}


async function buildInputDataForTestCase(suite_id) {
  const result = await mindmapBuilder.buildTree(suite_id);
  
  const features = result.features.map(feature => ({
    ...feature,
    categories: feature.categories.map(category => ({
      ...category,
      checklists: category.checklists.map(({ test_cases, ...checklist }) => checklist)
    }))
  }));
  
  return features;
}
async function readFileContent(files) {
  const extractOptions = { includeContent: true, maxLevel: 6 };
    const result = await fileService.extractHeadingsFromFiles(files, extractOptions);

    const formatted = (result.results || []).map(item => {
      const name = item.filename || 'document';
      const content = Array.isArray(item.headings)
        ? item.headings.map(h => `${h.title}:\n${h.content || ''}`).join('\n\n')
        : '';
      return { name, content };
    });
    return formatted;
}

async function extractDocument(files) {
  try {
    if (!files) {
      return { id: null, text: '', error: 'No files provided' };
    }
    const prompt = await buildExtractPromptFromFiles(files);

    const { id, text: result } = await callOpenAiForExtract(prompt);

    const parsed = typeof result === 'string' ? parseJsonSafely(result) : result;
    const features = Array.isArray(parsed)
      ? parsed
      : (parsed && typeof parsed === 'object' && Array.isArray(parsed.features) ? parsed.features : []);

    return {id, features};
  } catch (error) {
    logger.error('Error in extractDocument', error);
    return { id: null, text: '', error: 'Failed to extract document' };
  }
}

async function extractDocumentWithImages(files, figmaImagePaths = [], uploadedImagePaths = []) {
  try {
    if (!files && (!figmaImagePaths || figmaImagePaths.length === 0) && (!uploadedImagePaths || uploadedImagePaths.length === 0)) {
      return { id: null, text: '', error: 'No files or images provided' };
    }
    
    const prompt = await buildExtractPromptFromFiles(files);
    
    const allImagePaths = [...(figmaImagePaths || []), ...(uploadedImagePaths || [])];
    
    let result, id;
    if (allImagePaths.length > 0) {
      ({ id, text: result } = await callOpenAiForExtractWithImages(prompt, allImagePaths));
    } else {
      ({ id, text: result } = await callOpenAiForExtract(prompt));
    }

    const parsed = typeof result === 'string' ? parseJsonSafely(result) : result;
    const features = Array.isArray(parsed)
      ? parsed
      : (parsed && typeof parsed === 'object' && Array.isArray(parsed.features) ? parsed.features : []);

    return {id, features};
  } catch (error) {
    logger.error('Error in extractDocumentWithImages', error);
    return { id: null, text: '', error: 'Failed to extract document with images' };
  }
}

function inferAutomationTool(fieldType, language, framework) {
  const normalizedField = (fieldType || '').toLowerCase();
  const normalizedLang = (language || '').toLowerCase();
  if (normalizedField === 'api' || normalizedField === 'api_web_service') {
    return normalizedLang === 'java' ? 'REST Assured' : 'Postman/Newman';
  }
  if (normalizedField === 'mobile' || normalizedField === 'mobile_application') {
    return 'Appium';
  }
  // default web
  if (normalizedLang === 'java') return 'Selenium WebDriver';
  if (normalizedLang === 'javascript' || normalizedLang === 'typescript') return 'Playwright';
  return 'Selenium WebDriver';
}

function substitutePlaceholders(templateString, variables) {
  let result = String(templateString || '');
  Object.entries(variables || {}).forEach(([key, value]) => {
    const pattern = new RegExp(`\\{${key}\\}`, 'g');
    result = result.replace(pattern, String(value ?? ''));
  });
  return result;
}

async function buildPromptForAutoTest(options, testCaseDoc) {
  const { language, framework, field_type, design_pattern } = options || {};
  const automation_tool = inferAutomationTool(field_type, language, framework);

  const templatePath = path.join(__dirname, '../templates/gen_mindmap_prompt/gen_auto_test.ejs');
  const promptHeader = await ejs.renderFile(
    templatePath,
    {
      automation_language: language || 'Java',
      automation_framework: framework || 'JUnit5',
      automation_tool,
      design_pattern: design_pattern || 'POM',
      field_type: field_type || 'web'
    },
    { async: false }
  );

  // Extract the testcase detail (prefer existing detail key)
  const detail = testCaseDoc?.detail || {};
  const caseType = ['web_manual', 'mobile_manual', 'api_manual'].find(k => detail && detail[k]);
  const data = (caseType && detail[caseType]) ? detail[caseType] : {};

  const toArray = (val) => {
    if (Array.isArray(val)) return val;
    if (!val) return [];
    return String(val).split('\n').map(s => s.trim()).filter(Boolean);
  };

  const input_testcase_json = {
    test_case_id: data.test_case_id || String(testCaseDoc?._id || ''),
    test_case_name: data.test_case_name || '',
    priority: data.priority || '',
    pre_condition: toArray(data.pre_condition),
    steps: data.steps || '',
    test_data: data.test_data || '',
    expected_result: data.expected_result || '',
    test_case_type: data.test_case_type || ''
  };

  const finalPrompt = `${promptHeader}\n${JSON.stringify(input_testcase_json, null, 2)}`;
  return finalPrompt;
}

async function generateAutoTest(options) {
  try {
    const ids = Array.isArray(options?.test_case_id) ? options.test_case_id : [];
    if (ids.length === 0) {
      return { results: [] };
    }

    const testCases = await TestCaseRepository.findAll({ _id: { $in: ids } }, { sort: { _id: 1 } });
    if (!Array.isArray(testCases) || testCases.length === 0) {
      return { results: [] };
    }

    const results = [];
    for (const tcDoc of testCases) {
      const prompt = await buildPromptForAutoTest(options, tcDoc);
      const { output } = await callChatGpt(prompt);

      const rawItem = Array.isArray(output) ? (output[0] || {}) : (output || {});
      const scriptBlock = rawItem.automation_script || rawItem || {};

      const normalizedScript = {
        language: scriptBlock.language || options?.language || null,
        framework: scriptBlock.framework || options?.framework || null,
        tool: scriptBlock.tool || inferAutomationTool(options?.field_type, options?.language, options?.framework),
        dependencies: Array.isArray(scriptBlock.dependencies)
          ? scriptBlock.dependencies
          : (scriptBlock.dependencies ? [String(scriptBlock.dependencies)] : []),
        fixtures: Array.isArray(scriptBlock.fixtures)
          ? scriptBlock.fixtures
          : (scriptBlock.fixtures ? [String(scriptBlock.fixtures)] : []),
        notes: scriptBlock.notes || null,
        script: scriptBlock.script || null
      };

      const created = await TestCaseRepository.create({
        suite_id: options?.suite_id || tcDoc.suite_id,
        feature_id: tcDoc.feature_id || null,
        category_id: tcDoc.category_id || null,
        check_item_id: tcDoc.check_item_id || null,
        type: 'automation',
        automation_script: normalizedScript,
        status: 'draft'
      });

      const detail = tcDoc?.detail || {};
      const caseType = ['web_manual', 'mobile_manual', 'api_manual'].find(k => detail && detail[k]);
      const data = (caseType && detail[caseType]) ? detail[caseType] : {};
      const testCaseName = data.test_case_name || String(tcDoc?._id || '');

      results.push({
        _id: String(created?._id || ''),
        test_case_name: testCaseName,
        automation_script: normalizedScript,
        generated_time: created.createdAt
      });
    }

    return { results };
  } catch (error) {
    logger.error('Error in generateAutoTest', error);
    return { results: [], error: 'Failed to generate auto tests' };
  }
}

const findDuplicateValues = values => {
  const seen = new Set();
  const duplicates = new Set();
  for (const value of values) {
    if (seen.has(value)) {
      duplicates.add(value);
    } else {
      seen.add(value);
    }
  }
  return [...duplicates];
};

const getArray = (section, key) => (Array.isArray(section?.[key]) ? section[key] : []);

const hasItems = list => Array.isArray(list) && list.length > 0;

const toStringId = id => (typeof id === 'string' ? id : String(id));

const sortCheckItemsForInsertion = items => {
  const sorted = [...items];
  sorted.sort((a, b) => ((a?.parent_tmp_id || a?.parent_id) ? 1 : -1));
  return sorted;
};

const extractSection = section => ({
  adds: getArray(section, 'add'),
  updates: getArray(section, 'update'),
  deletes: getArray(section, 'delete')
});

const buildTempIdMapEntry = (tempIdToDbId, tmpId, dbId) => {
  if (tmpId) {
    tempIdToDbId[tmpId] = toStringId(dbId);
  }
};

const mapParentId = (item, tempIdToDbId) => item.parent_id || tempIdToDbId[item.parent_tmp_id] || null;

const mapFeatureId = (item, tempIdToDbId) => item.feature_id || tempIdToDbId[item.feature_tmp_id];

const validateFeatureTmpIds = featureAdds => {
  const featureTmpIds = featureAdds.map(feature => feature?.tmp_id).filter(Boolean);
  const duplicates = findDuplicateValues(featureTmpIds);
  if (duplicates.length > 0) {
    throw new MindmapUpdateError(400, {
      error: 'tmp_id của features.add bị trùng',
      dup_tmp_ids: duplicates
    });
  }
  return new Set(featureTmpIds);
};

const validateCheckItemTmpIds = checkItemAdds => {
  const checkItemTmpIds = checkItemAdds.map(item => item?.tmp_id).filter(Boolean);
  const duplicates = findDuplicateValues(checkItemTmpIds);
  if (duplicates.length > 0) {
    throw new MindmapUpdateError(400, {
      error: 'tmp_id của check_items.add bị trùng',
      dup_tmp_ids: duplicates
    });
  }
  return new Set(checkItemTmpIds);
};

const validateFeatureReferences = (checkItemAdds, featureTmpIdSet) => {
  const invalidFeatureRefs = [...new Set(
    checkItemAdds
      .filter(item => item && item.feature_tmp_id && !featureTmpIdSet.has(item.feature_tmp_id))
      .map(item => item.feature_tmp_id)
  )];
  if (invalidFeatureRefs.length > 0) {
    throw new MindmapUpdateError(400, {
      error: 'feature_tmp_id không tồn tại trong features.add',
      invalid_feature_tmp_id: invalidFeatureRefs
    });
  }
};

const validateParentReferences = checkItemAdds => {
  const categoryTmpIdSet = new Set(
    checkItemAdds
      .filter(item => item && item.type === 'category' && item.tmp_id)
      .map(item => item.tmp_id)
  );

  const invalidParentRefs = [...new Set(
    checkItemAdds
      .filter(item => item && item.parent_tmp_id && !categoryTmpIdSet.has(item.parent_tmp_id))
      .map(item => item.parent_tmp_id)
  )];

  if (invalidParentRefs.length > 0) {
    throw new MindmapUpdateError(400, {
      error: 'parent_tmp_id không hợp lệ (không tham chiếu tới category trong check_items.add)',
      invalid_parent_tmp_id: invalidParentRefs
    });
  }
};

const validateTmpIds = (featureAdds, checkItemAdds) => {
  const featureTmpIdSet = validateFeatureTmpIds(featureAdds);
  validateCheckItemTmpIds(checkItemAdds);
  validateFeatureReferences(checkItemAdds, featureTmpIdSet);
  validateParentReferences(checkItemAdds);
};

const handleFeatureDeletes = async (suiteId, featureIds) => {
  if (!hasItems(featureIds)) {
    return;
  }

  await FeatureRepository.deleteMany({ _id: { $in: featureIds }, suite_id: suiteId });
  await CheckItemRepository.deleteMany({ suite_id: suiteId, feature_id: { $in: featureIds } });
  await TestCaseRepository.deleteMany({ suite_id: suiteId, feature_id: { $in: featureIds } });
  logger.info(`Xóa ${featureIds.length} feature`);
};

const collectCategoryCascadeDeletes = async ids => {
  const allIds = [...ids];
  const categoriesToDelete = await CheckItemRepository.findAll({ _id: { $in: allIds }, type: 'category' });

  if (!hasItems(categoriesToDelete)) {
    return allIds;
  }

  const categoryIds = categoriesToDelete.map(category => category._id);
  const childChecklists = await CheckItemRepository.findAll({ parent_id: { $in: categoryIds } });
  childChecklists.forEach(child => allIds.push(toStringId(child._id)));
  return allIds;
};

const handleCheckItemDeletes = async checkItemIds => {
  if (!hasItems(checkItemIds)) {
    return;
  }

  const cascadeIds = await collectCategoryCascadeDeletes(checkItemIds);

  await TestCaseRepository.deleteMany({
    $or: [
      { check_item_id: { $in: cascadeIds } },
      { category_id: { $in: cascadeIds } }
    ]
  });

  await CheckItemRepository.deleteMany({ _id: { $in: cascadeIds } });
  logger.info(`Đã xóa ${checkItemIds.length} check items và dữ liệu liên quan.`);
};

const handleFeatureUpdates = async updates => {
  if (!hasItems(updates)) {
    return;
  }

  for (const feature of updates) {
    const { _id, ...updateData } = feature;
    updateData.description = updateData.description || '';
    updateData.source_files = updateData.source_files || [];
    updateData.merged_from = updateData.merged_from || [];
    updateData.permissions = updateData.permissions || {};
    updateData.ui_components = updateData.ui_components || {};
    await FeatureRepository.update(_id, updateData);
  }
  logger.info(`Đã cập nhật ${updates.length} features.`);
};

const handleCheckItemUpdates = async updates => {
  if (!hasItems(updates)) {
    return;
  }

  for (const item of updates) {
    const { _id, ...updateData } = item;
    await CheckItemRepository.update(_id, updateData);
  }
  logger.info(`Đã cập nhật ${updates.length} check items.`);
};

const handleFeatureAdds = async (suiteId, featureAdds, tempIdToDbId) => {
  if (!hasItems(featureAdds)) {
    return;
  }

  const lastFeature = await FeatureRepository.findOne({ suite_id: suiteId }, { sort: { order: -1 } });
  let order = lastFeature ? lastFeature.order + 1 : 0;

  for (const feature of featureAdds) {
    const newFeature = await FeatureRepository.create({ ...feature, suite_id: suiteId, order: order++ });
    buildTempIdMapEntry(tempIdToDbId, feature.tmp_id, newFeature._id);
  }
  logger.info(`Đã thêm ${featureAdds.length} features mới.`);
};

const buildNewCheckItemData = (item, suiteId, featureId, parentId) => {
  const newItemData = {
    ...item,
    suite_id: suiteId,
    feature_id: featureId,
    parent_id: parentId || null
  };

  delete newItemData.feature_tmp_id;
  delete newItemData.parent_tmp_id;
  delete newItemData.tmp_id;

  return newItemData;
};

const handleCheckItemAdds = async (suiteId, checkItemAdds, tempIdToDbId) => {
  if (!hasItems(checkItemAdds)) {
    return;
  }

  const itemsToAdd = sortCheckItemsForInsertion(checkItemAdds);

  for (const item of itemsToAdd) {
    const featureId = mapFeatureId(item, tempIdToDbId);
    const parentId = mapParentId(item, tempIdToDbId);

    if (!featureId) {
      continue;
    }

    const newItemData = buildNewCheckItemData(item, suiteId, featureId, parentId);
    const newCheckItem = await CheckItemRepository.create(newItemData);
    buildTempIdMapEntry(tempIdToDbId, item.tmp_id, newCheckItem._id);
  }
  logger.info(`Đã thêm ${checkItemAdds.length} check items mới.`);
};

async function updateMindmap({ suite_id: suiteId, features, check_items: checkItems }) {
  const tempIdToDbId = {};
  const featureSection = extractSection(features);
  const checkItemSection = extractSection(checkItems);

  validateTmpIds(featureSection.adds, checkItemSection.adds);

  await handleFeatureDeletes(suiteId, featureSection.deletes);
  await handleCheckItemDeletes(checkItemSection.deletes);
  await handleFeatureUpdates(featureSection.updates);
  await handleCheckItemUpdates(checkItemSection.updates);
  await handleFeatureAdds(suiteId, featureSection.adds, tempIdToDbId);
  await handleCheckItemAdds(suiteId, checkItemSection.adds, tempIdToDbId);

  return mindmapBuilder.rebuildSnapshot(suiteId);
}

module.exports = {
  extractDocument,
  extractDocumentWithImages,
  generateChecklist,
  generateTestCaseForFeature,
  generateAutoTest,
  updateMindmap,
  MindmapUpdateError,
  buildInputDataForChecklist,
  buildInputDataForTestCase
};