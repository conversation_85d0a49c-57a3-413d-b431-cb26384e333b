const userService = require('../adapters/mongooseUserAdapter');
const authService = require('../adapters/jwtAuthAdapter');
const config = require('../config');
const logger = require('../services/core/loggerService');
const mongoose = require('mongoose');
const ProjectRepository = require('../repositories/projectRepository');
const userProjectService = require('../services/userProjectService');
const userProjectRepository = require('../repositories/userProjectRepository');

// In-memory token blacklist (in production, use Redis or database)
const tokenBlacklist = new Set();

// Helper function to create safe user data
const createSafeUser = (user, projectRole = null) => {
  return {
    _id: user._id,
    name: user.name,
    email: user.email,
    role: user.role,
    status: user.status,
    avatar: user.avatar,
    project_role: projectRole,
    lastLogin: user.lastLogin,
    emailVerified: user.emailVerified,
    preferences: user.preferences,
    createdAt: user.createdAt,
    updatedAt: user.updatedAt
  };
};

// Helper function to send error response
const sendErrorResponse = (res, statusCode, message, code = 'ERROR') => {
  return res.status(statusCode).json({
    message,
    code
  });
};

// Helper function to validate role permissions
const validateRolePermissions = (req, targetRole) => {
  const currentUser = req.user;
  
  // Only Admin can create Admin role
  if (targetRole === 'admin' && currentUser.role !== 'admin') {
    return {
      valid: false,
      message: 'Chỉ Admin mới có thể tạo tài khoản Admin',
      code: 'ADMIN_CREATION_FORBIDDEN'
    };
  }
  
  // Only Admin can create Leader role
  if (targetRole === config.global_role.test_manager && currentUser.role !== config.global_role.admin) {
    return {
      valid: false,
      message: 'Chỉ Admin mới có thể tạo tài khoản Test Manager',
      code: 'TEST_MANAGER_CREATION_FORBIDDEN'
    };
  }

  // Only Admin can create Viewer role
  if (targetRole === 'user' && currentUser.role !== 'admin') {
    return {
      valid: false,
      message: 'Chỉ Admin mới có thể tạo tài khoản User',
      code: 'USER_CREATION_FORBIDDEN'
    };
  }
  
  return { valid: true };
};

// Health check
exports.healthCheck = async (req, res) => {
  res.json({ message: 'Server is running' });
};

// Register a new user
exports.register = async (req, res) => {
  try {
    const { name, email, password, role } = req.body;

    if (!config.user.role.includes(role)) {
      return sendErrorResponse(res, 400, 'Vai trò không hợp lệ', 'INVALID_ROLE');
    }
    
    if (role === config.global_role.admin || role === config.global_role.test_manager) {
      return sendErrorResponse(res, 403, 'Không có quyền đăng ký tài khoản này', 'FORBIDDEN');
    }
    
    const existing = await userService.getUserByEmail(email);
    if (existing) {
      return sendErrorResponse(res, 409, 'Email đã được đăng ký', 'EMAIL_EXISTS');
    }
    
    const hashed = await authService.hashPassword(password);
    const user = await userService.createUser({ 
      name, 
      email, 
      password: hashed, 
      role: role || 'user' 
    });
    
    res.status(201).json({ 
      message: 'Đăng ký thành công', 
      user: createSafeUser(user)
    });
  } catch (err) {
    logger.error('Lỗi khi đăng ký', err, req.originalUrl);
    sendErrorResponse(res, 400, 'Đăng ký thất bại', 'REGISTRATION_ERROR');
  }
};

// Login with improved security
exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;
    
    const user = await userService.getUserByEmail(email);
    if (!user) {
      return sendErrorResponse(res, 401, 'Email hoặc mật khẩu không chính xác', 'INVALID_CREDENTIALS');
    }
    
    const valid = await authService.comparePassword(password, user.password);
    if (!valid) {
      return sendErrorResponse(res, 401, 'Email hoặc mật khẩu không chính xác', 'INVALID_CREDENTIALS');
    }
    
    const projectRole = await userProjectService.getHighestProjectRoleByUser(user._id);

    // Generate tokens
    const tokenPayload = { 
      _id: user._id, 
      email: user.email, 
      role: user.role,
      project_role: projectRole ? projectRole : null,
      iat: Math.floor(Date.now() / 1000)
    };
    
    const token = await authService.generateToken(tokenPayload);
    const refreshToken = await authService.generateRefreshToken(tokenPayload);
    
    res.json({ 
      token, 
      refreshToken,
      user: createSafeUser(user, projectRole),
      expiresIn: config.jwtExpiresIn
    });
  } catch (err) {
    logger.error('Lỗi khi đăng nhập', err, req.originalUrl);
    sendErrorResponse(res, 500, 'Đăng nhập thất bại', 'LOGIN_ERROR');
  }
};

// Logout with token blacklist
exports.logout = async (req, res) => {
  try {
    const token = req.token; // From auth middleware
    
    if (token && config.security.enableTokenBlacklist) {
      tokenBlacklist.add(token);
      console.log('Token blacklisted successfully');
    }
    
    res.json({ 
      message: 'Đăng xuất thành công',
      code: 'LOGOUT_SUCCESS' 
    });
  } catch (err) {
    logger.error('Lỗi khi đăng xuất', err, req.originalUrl);
    res.status(500).json({ 
      message: 'Đã có lỗi xảy ra',
      code: 'LOGOUT_ERROR' 
    });
  }
};

// Check if token is blacklisted
exports.isTokenBlacklisted = (token) => {
  return tokenBlacklist.has(token);
};

// Refresh token endpoint
exports.refreshToken = async (req, res) => {
  try {
    const { refreshToken } = req.body;
    
    if (!refreshToken) {
      return res.status(400).json({ 
        message: 'Refresh token là bắt buộc',
        code: 'MISSING_REFRESH_TOKEN' 
      });
    }
    
    // Verify refresh token
    const decoded = await authService.verifyToken(refreshToken);
    
    // Generate new access token
    const newTokenPayload = { 
      _id: decoded._id, 
      email: decoded.email, 
      role: decoded.role,
      project_role: decoded.project_role,
      iat: Math.floor(Date.now() / 1000)
    };
    
    const newToken = await authService.generateToken(newTokenPayload);
    
    res.json({ 
      token: newToken,
      expiresIn: config.jwtExpiresIn
    });
  } catch (err) {
    logger.error('Lỗi khi refresh token', err, req.originalUrl);
    res.status(401).json({ 
      message: 'Refresh token không hợp lệ',
      code: 'INVALID_REFRESH_TOKEN' 
    });
  }
};

// List all users with pagination and filters (admin only)
exports.getUsers = async (req, res) => {
  try {
    // Admin has full access - permission check is handled by middleware
    
    const {
      page = 1,
      limit = 10,
      search = '',
      role = '',
      status = '',
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    const filters = {};
    
    // Build search filter - only search by name
    if (search && search.trim()) {
      filters.name = { $regex: search.trim(), $options: 'i' };
    }
    
    // Add role filter (ignore "all" value)
    if (role && role !== 'all') {
      filters.role = role;
    }
    
    // Add status filter (ignore "all" value)
    if (status && status !== 'all') {
      filters.status = status;
    }


    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Calculate pagination
    const skip = (parseInt(page) - 1) * parseInt(limit);
    const limitNum = parseInt(limit);

    // Get total count for pagination
    const total = await userService.countUsers(filters);
    
    // Get users with pagination
    const users = await userService.getUsersPaginated(filters, sort, skip, limitNum);
    
    // Calculate pagination info
    const totalPages = Math.ceil(total / limitNum);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    const response = {
      users,
      pagination: {
        page: parseInt(page),
        limit: limitNum,
        total,
        totalPages,
        hasNextPage,
        hasPrevPage
      }
    };

    res.json(response);
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách người dùng', err, req.originalUrl);
    res.status(500).json({ 
      message: 'Lấy danh sách người dùng thất bại',
      code: 'GET_USERS_ERROR' 
    });
  }
};

exports.getUserById = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Validate ID parameter
    if (!id || id === 'undefined' || id === 'null') {
      return res.status(400).json({ 
        message: 'ID người dùng không hợp lệ',
        code: 'INVALID_USER_ID' 
      });
    }
    
    // Additional security check: ensure non-admin users can only access their own data
    if (req.user.role !== 'admin' && req.params.id !== req.user._id.toString()) {
      return res.status(403).json({ 
        message: 'Bạn chỉ có thể xem thông tin của chính mình',
        code: 'CANNOT_VIEW_OTHER_USER' 
      });
    }
    
    const user = await userService.getUserById(id);
    if (!user) {
      return sendErrorResponse(res, 404, 'Người dùng không tồn tại', 'USER_NOT_FOUND');
    }
    
    res.json(createSafeUser(user));
  } catch (err) {
    logger.error('Lỗi khi lấy thông tin người dùng', { error: err.message });
    sendErrorResponse(res, 500, 'Lấy thông tin người dùng thất bại', 'GET_USER_ERROR');
  }
};

// Create a new user
exports.createUser = async (req, res) => {
  try {
    const { name, email, password, role } = req.body;
    
    // Validate input
    if (!name || !email || !password) {
      return sendErrorResponse(res, 400, 'Các trường bắt buộc không được để trống', 'MISSING_FIELDS');
    }
    
    // Validate role permissions
    const roleValidation = validateRolePermissions(req, role);
    if (!roleValidation.valid) {
      return sendErrorResponse(res, 400, roleValidation.message, roleValidation.code);
    }
    
    const existing = await userService.getUserByEmail(email);
    if (existing) {
      return sendErrorResponse(res, 409, 'Email đã được đăng ký', 'EMAIL_EXISTS');
    }
    
    const hashed = await authService.hashPassword(password);
    const user = await userService.createUser({ 
      name, 
      email, 
      password: hashed, 
      role: role || 'user' 
    });
    
    res.status(201).json({ 
      message: 'Tạo người dùng thành công', 
      user: createSafeUser(user)
    });
  } catch (err) {
    logger.error('Lỗi khi tạo người dùng', { error: err.message });
    sendErrorResponse(res, 500, 'Tạo người dùng thất bại', 'CREATE_USER_ERROR');
  }
};

// Update user (admin or self)
exports.updateUser = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Validate ID parameter
    if (!id || id === 'undefined' || id === 'null') {
      return res.status(400).json({ 
        message: 'ID người dùng không hợp lệ',
        code: 'INVALID_USER_ID' 
      });
    }
    
    // Permission check is handled by middleware
    // Additional security check: ensure non-admin users can only update themselves
    if (req.user.role !== 'admin' && req.params.id !== req.user._id.toString()) {
      return res.status(403).json({ 
        message: 'Bạn chỉ có thể cập nhật thông tin của chính mình',
        code: 'CANNOT_UPDATE_OTHER_USER' 
      });
    }
    
    const updates = { ...req.body };
    
    // Hash password if provided
    if (updates.password) {
      updates.password = await authService.hashPassword(updates.password);
    }
    
    // Prevent non-admin from changing role
    if (updates.role && req.user.role !== 'admin') {
      delete updates.role;
    }
    
    // Check if email is being updated and if it already exists
    if (updates.email) {
      const existing = await userService.getUserByEmail(updates.email);
      if (existing && existing._id.toString() !== id) {
        return sendErrorResponse(res, 409, 'Email đã được đăng ký', 'EMAIL_EXISTS');
      }
    }

    if (!updates.password) {
      delete updates.password;
    }
    
    const updatedUser = await userService.updateUser(id, updates);
    
    // Return safe user data
    const safeUser = {
      _id: updatedUser._id,
      name: updatedUser.name,
      email: updatedUser.email,
      role: updatedUser.role
    };
    
    res.json({ 
      message: 'Cập nhật hồ sơ thành công', 
      user: safeUser 
    });
  } catch (err) {
    logger.error('Lỗi khi cập nhật người dùng', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Cập nhật hồ sơ thất bại',
      code: 'UPDATE_USER_ERROR' 
    });
  }
};

// Delete user (admin only)
exports.deleteUser = async (req, res) => {
  try {
    // Permission check is handled by middleware
    
    const { id } = req.params;
    
    // Prevent superadmin from deleting themselves
    if (req.user._id === id) {
      return res.status(400).json({ 
        message: 'Không thể xóa tài khoản của mình',
        code: 'CANNOT_DELETE_SELF' 
      });
    }
    
    // Check if user to delete is SuperAdmin
    const userToDelete = await userService.getUserById(id);
    if (userToDelete && userToDelete.role === 'admin') {
      return res.status(400).json({ 
        message: 'Không thể xóa tài khoản Admin',
        code: 'CANNOT_DELETE_ADMIN' 
      });
    }
    
    const deletedUser = await userService.deleteUser(id);
    if (!deletedUser) {
      return res.status(404).json({ 
        message: 'Người dùng không tồn tại',
        code: 'USER_NOT_FOUND' 
      });
    }

    await userProjectRepository.deleteMany({ user_id: id });
    
    // Return safe user data
    const safeUser = {
      _id: deletedUser._id,
      name: deletedUser.name,
      email: deletedUser.email,
      role: deletedUser.role
    };
    
    res.json({ 
      message: 'Xóa người dùng thành công', 
      user: safeUser 
    });
  } catch (err) {
    logger.error('Lỗi khi xóa người dùng', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Xóa người dùng thất bại',
      code: 'DELETE_USER_ERROR' 
    });
  }
};

// Search users by name (admin only)
exports.searchUserByName = async (req, res) => {
  try {
    // Permission check is handled by middleware
    
    const { name } = req.query;
    
    if (!name) {
      return res.status(400).json({ 
        message: 'Tên tìm kiếm là bắt buộc',
        code: 'MISSING_SEARCH_TERM' 
      });
    }
    
    const users = await userService.listUsers();
    const filtered = users.filter(u => 
      u.name.toLowerCase().includes(name.toLowerCase())
    );
    
    // Return safe user data
    const safeUsers = filtered.map(u => ({
      _id: u._id,
      name: u.name,
      email: u.email,
      role: u.role
    }));
    
    res.json(safeUsers);
  } catch (err) {
    logger.error('Lỗi khi tìm kiếm người dùng', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Tìm kiếm người dùng thất bại',
      code: 'SEARCH_USERS_ERROR' 
    });
  }
};

exports.getUsersByProject = async (req, res) => {
  try {
    const { projectId } = req.params;

    if (!projectId || projectId === 'undefined' || projectId === 'null') {
      return res.status(400).json({
        message: 'Project ID không hợp lệ',
        code: 'INVALID_PROJECT_ID'
      });
    }

    const project = await ProjectRepository.findById(projectId);
    if (!project) {
      return res.status(404).json({
        message: 'Dự án không tồn tại',
        code: 'PROJECT_NOT_FOUND'
      });
    }

    const members = await userProjectRepository.findUsersByProjectId(projectId);
    const users = members.map(member => ({
      _id: member.user._id,
      name: member.user.name,
      email: member.user.email,
      role: member.user.role,
      status: member.user.status,
      avatar: member.user.avatar,
      lastLogin: member.user.lastLogin,
      project_role: member.project_role,
      user_project_id: member._id,
      joinedAt: member.createdAt
    }));

    return res.json({
      project_id: projectId,
      total: users.length,
      users
    });
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách người dùng theo dự án', { 
      error: err.message
    });
    return res.status(500).json({
      message: 'Không thể lấy danh sách người dùng của dự án',
      code: 'GET_PROJECT_USERS_ERROR'
    });
  }
};

// Get all admin users
exports.adminUsers = async (req, res) => {
  try {
    const users = await userService.listUsers();
    const adminUsers = users.filter(u => u.role === 'admin');
    const safeUsers = adminUsers.map(u => ({
      _id: u._id,
      name: u.name,
    }));
    res.json(safeUsers);
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách người dùng admin', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Lấy danh sách người dùng admin thất bại',
      code: 'GET_USERS_ERROR' 
    });
  }
};

exports.leaderUsers = async (req, res) => {
  try {
    const users = await userService.listUsers();
    const leaderUsers = users.filter(u => u.role === 'leader');
    const safeUsers = leaderUsers.map(u => ({
      _id: u._id,
      name: u.name,
    }));
    res.json(safeUsers);
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách người dùng leader', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Lấy danh sách người dùng leader thất bại',
      code: 'GET_USERS_ERROR' 
    });
  }
};

// Update user status (admin only)
exports.updateUserStatus = async (req, res) => {
  try {
    // Permission check is handled by middleware
    
    const { id } = req.params;
    
    // Validate ID parameter
    if (!id || id === 'undefined' || id === 'null') {
      return res.status(400).json({ 
        message: 'ID người dùng không hợp lệ',
        code: 'INVALID_USER_ID' 
      });
    }
    
    const { status } = req.body;
    
    if (!['active', 'inactive', 'suspended', 'pending'].includes(status)) {
      return res.status(400).json({ 
        message: 'Trạng thái không hợp lệ',
        code: 'INVALID_STATUS' 
      });
    }
    
    // Prevent admin from changing their own status
    if (req.user._id === id) {
      return res.status(400).json({ 
        message: 'Không thể thay đổi trạng thái của chính mình',
        code: 'CANNOT_CHANGE_OWN_STATUS' 
      });
    }
    
    const updatedUser = await userService.updateUser(id, { 
      status,
      lastModifiedBy: req.user._id
    });
    
    if (!updatedUser) {
      return res.status(404).json({ 
        message: 'Người dùng không tồn tại',
        code: 'USER_NOT_FOUND' 
      });
    }
    
    res.json({ 
      message: 'Cập nhật trạng thái thành công', 
      user: {
        _id: updatedUser._id,
        name: updatedUser.name,
        email: updatedUser.email,
        role: updatedUser.role,
        status: updatedUser.status
      }
    });
  } catch (err) {
    logger.error('Lỗi khi cập nhật trạng thái người dùng', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Cập nhật trạng thái thất bại',
      code: 'UPDATE_STATUS_ERROR' 
    });
  }
};

// Change user password (self or admin)
exports.changePassword = async (req, res) => {
  try {
    const { id } = req.params;
    const { currentPassword, newPassword } = req.body;
    
    // Validate ID parameter
    if (!id || id === 'undefined' || id === 'null') {
      return res.status(400).json({ 
        message: 'ID người dùng không hợp lệ',
        code: 'INVALID_USER_ID' 
      });
    }
    
    // Permission check is handled by middleware
    // Additional security check: ensure non-admin users can only change their own password
    if (req.user.role !== 'admin' && req.params.id !== req.user._id.toString()) {
      return res.status(403).json({ 
        message: 'Bạn chỉ có thể thay đổi mật khẩu của chính mình',
        code: 'CANNOT_CHANGE_OTHER_PASSWORD' 
      });
    }
    
    const user = await userService.getUserById(id);
    if (!user) {
      return res.status(404).json({ 
        message: 'Người dùng không tồn tại',
        code: 'USER_NOT_FOUND' 
      });
    }
    
    // For non-admin/superadmin users, verify current password
    if (req.user.role !== 'admin' && req.user.role !== 'superadmin') {
      const validPassword = await authService.comparePassword(currentPassword, user.password);
      if (!validPassword) {
        return res.status(400).json({ 
          message: 'Mật khẩu hiện tại không chính xác',
          code: 'INVALID_CURRENT_PASSWORD' 
        });
      }
    }
    
    // Hash new password
    const hashedPassword = await authService.hashPassword(newPassword);
    
    // Update password
    await userService.updateUser(id, { 
      password: hashedPassword,
      lastModifiedBy: req.user._id
    });
    
    res.json({ 
      message: 'Đổi mật khẩu thành công' 
    });
  } catch (err) {
    logger.error('Lỗi khi đổi mật khẩu', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Đổi mật khẩu thất bại',
      code: 'CHANGE_PASSWORD_ERROR' 
    });
  }
};

// Get user profile (self or admin)
exports.getUserProfile = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Permission check is handled by middleware
    
    const user = await userService.getUserById(id);
    if (!user) {
      return res.status(404).json({ 
        message: 'Người dùng không tồn tại',
        code: 'USER_NOT_FOUND' 
      });
    }
    
    // Return safe user data with additional profile info
    const safeUser = {
      _id: user._id,
      name: user.name,
      email: user.email,
      role: user.role,
      status: user.status,
      avatar: user.avatar,
      lastLogin: user.lastLogin,
      emailVerified: user.emailVerified,
      preferences: user.preferences,
      createdAt: user.createdAt,
      updatedAt: user.updatedAt
    };
    
    res.json(safeUser);
  } catch (err) {
    logger.error('Lỗi khi lấy thông tin hồ sơ', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Lấy thông tin hồ sơ thất bại',
      code: 'GET_PROFILE_ERROR' 
    });
  }
};

// Update user profile (self or admin)
exports.updateUserProfile = async (req, res) => {
  try {
    const { id } = req.params;
    
    // Validate ID parameter
    if (!id || id === 'undefined' || id === 'null') {
      return res.status(400).json({ 
        message: 'ID người dùng không hợp lệ',
        code: 'INVALID_USER_ID' 
      });
    }
    
    const updates = { ...req.body };
    
    // Permission check is handled by middleware
    // Additional security check: ensure non-admin users can only update themselves
    if (req.user.role !== 'admin' && req.params.id !== req.user._id.toString()) {
      return res.status(403).json({ 
        message: 'Bạn chỉ có thể cập nhật thông tin của chính mình',
        code: 'CANNOT_UPDATE_OTHER_USER' 
      });
    }
    
    // Hash password if provided
    if (updates.password && updates.password.trim().length > 0) {
      updates.password = await authService.hashPassword(updates.password);
    } else {
      // Remove empty password field
      delete updates.password;
    }
    
    // Remove sensitive fields that shouldn't be updated via this endpoint
    delete updates.role; // Role should be updated via separate endpoint
    delete updates.status; // Status should be updated via separate endpoint

    // Check if email is being updated and if it already exists
    if (updates.email) {
      const existing = await userService.getUserByEmail(updates.email);
      if (existing && existing._id.toString() !== id) {
        return sendErrorResponse(res, 409, 'Email đã được đăng ký', 'EMAIL_EXISTS');
      }
    }

    // Add metadata
    updates.lastModifiedBy = req.user._id;
    
    const updatedUser = await userService.updateUser(id, updates);
    
    if (!updatedUser) {
      return res.status(404).json({ 
        message: 'Người dùng không tồn tại',
        code: 'USER_NOT_FOUND' 
      });
    }
    
    // Return safe user data
    const safeUser = {
      _id: updatedUser._id,
      name: updatedUser.name,
      email: updatedUser.email,
      role: updatedUser.role,
      status: updatedUser.status,
      avatar: updatedUser.avatar,
      preferences: updatedUser.preferences,
      updatedAt: updatedUser.updatedAt
    };
    
    res.json({ 
      message: 'Cập nhật hồ sơ thành công', 
      user: safeUser 
    });
  } catch (err) {
    logger.error('Lỗi khi cập nhật hồ sơ', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Cập nhật hồ sơ thất bại',
      code: 'UPDATE_PROFILE_ERROR' 
    });
  }
};

// Bulk operations (admin only)
exports.bulkUpdateUsers = async (req, res) => {
  try {
    // Permission check is handled by middleware
    
    const { userIds, updates } = req.body;
    
    if (!Array.isArray(userIds) || userIds.length === 0) {
      return res.status(400).json({ 
        message: 'Danh sách ID người dùng không hợp lệ',
        code: 'INVALID_USER_IDS' 
      });
    }
    
    // Remove sensitive fields
    delete updates.password;
    delete updates.role;
    
    // Add metadata
    updates.lastModifiedBy = req.user._id;
    
    const result = await userService.bulkUpdateUsers(userIds, updates);
    
    res.json({ 
      message: `Cập nhật thành công ${result.modifiedCount} người dùng`,
      modifiedCount: result.modifiedCount
    });
  } catch (err) {
    logger.error('Lỗi khi cập nhật hàng loạt người dùng', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Cập nhật hàng loạt thất bại',
      code: 'BULK_UPDATE_ERROR' 
    });
  }
};

// Get user statistics (admin only)
exports.getUserStats = async (req, res) => {
  try {
    // Permission check is handled by middleware
    
    const stats = await userService.getUserStats();
    
    res.json(stats);
  } catch (err) {
    logger.error('Lỗi khi lấy thống kê người dùng', { 
      error: err.message
    });
    res.status(500).json({ 
      message: 'Lấy thống kê thất bại',
      code: 'GET_STATS_ERROR' 
    });
  }
};

exports.filterUser = async (req, res) => {
  try {

    const { user_name: rawUserName, role: rawRole, project_id: rawProjectId } = req.query;

    const normalizeStringParam = (value) => {
      if (!value || typeof value !== 'string') {
        return '';
      }

      const trimmed = value.trim();
      if (!trimmed || trimmed === 'undefined' || trimmed === 'null') {
        return '';
      }

      return trimmed;
    };

    const normalizeRoleFilter = (roleParam) => {
      const normalized = normalizeStringParam(roleParam);
      if (!normalized) {
        return [];
      }

      return normalized
        .split(',')
        .map(role => role.trim().toLowerCase())
        .filter(role => role && role !== 'all');
    };

    const userName = normalizeStringParam(rawUserName);
    const projectId = normalizeStringParam(rawProjectId);
    const roleFilters = normalizeRoleFilter(rawRole);

    const matchesRole = (value) => {
      if (!roleFilters.length) {
        return true;
      }

      return roleFilters.includes((value || '').toLowerCase());
    };

    const matchesName = (value) => {
      if (!userName) {
        return true;
      }

      const regex = new RegExp(userName, 'i');
      return regex.test(value || '');
    };

    let safeUsers = [];

    if (projectId) {
      if (!mongoose.Types.ObjectId.isValid(projectId)) {
        return res.status(400).json({
          message: 'Project ID không hợp lệ',
          code: 'INVALID_PROJECT_ID'
        });
      }

      const projectMembers = await userProjectRepository.findUsersByProjectId(projectId);

      safeUsers = projectMembers
        .filter(member => member.user && matchesName(member.user.name) && matchesRole(member.user.role))
        .map(member => {
          const safeUser = createSafeUser(member.user, member.project_role);
          return {
            ...safeUser,
            user_project_id: member._id,
            project_id: member.project_id
          };
        });
    } else {
      const users = await userService.listUsers();
      safeUsers = users
        .filter(user => matchesName(user.name) && matchesRole(user.role))
        .map(user => createSafeUser(user));
    }

    return res.json(safeUsers);
  } catch (err) {
    logger.error('Lỗi khi lọc người dùng', {
      error: err.message
    });
    res.status(500).json({
      message: 'Lọc người dùng thất bại',
      code: 'FILTER_USERS_ERROR'
    });
  }
};

exports.getUserApprovers = async (req, res) => {
  try {
    const { project_id } = req.query;

    if (project_id) {
    const project = await ProjectRepository.findById(project_id);
    if (!project) {
        return res.status(404).json({
          message: 'Dự án không tồn tại',
          code: 'PROJECT_NOT_FOUND'
        });
      }
    }

    const allUsers = await userService.listUsers();
    const adminAndTestManagerUsers = allUsers.filter(
      user => user.role === config.global_role.admin || user.role === config.global_role.test_manager
    );

    const projectMembers = await userProjectRepository.findUsersByProjectId(project_id);
    const leaderUsers = projectMembers
      .filter(member => member.user && member.project_role === config.project_role.leader)
      .map(member => member.user);

    const approverMap = new Map();

    adminAndTestManagerUsers.forEach(user => {
      approverMap.set(user._id.toString(), {
        _id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        avatar: user.avatar,
        project_role: null
      });
    });

    leaderUsers.forEach(user => {
      approverMap.set(user._id.toString(), {
        _id: user._id,
        name: user.name,
        email: user.email,
        role: user.role,
        status: user.status,
        avatar: user.avatar,
        project_role: config.project_role.leader
      });
    });

    const approvers = Array.from(approverMap.values());

    return res.json({
      project_id: project_id,
      total: approvers.length,
      approvers
    });
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách user Approver', {
      error: err.message
    });
    res.status(500).json({
      message: 'Lấy danh sách user Approver thất bại',
      code: 'GET_USER_APPROVERS_ERROR'
    });
  }
};