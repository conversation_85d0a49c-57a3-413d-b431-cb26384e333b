const bcrypt = require('bcryptjs');

// Mock user data for testing
const mockUsers = [
  {
    name: 'Company Admin',
    email: '<EMAIL>',
    password: 'admin123',
    role: 'admin',
    status: 'active',
    emailVerified: true,
    preferences: {
      language: 'vi',
      theme: 'light',
      notifications: {
        email: true,
        push: true
      }
    },
    lastLogin: new Date(),
    createdAt: new Date('2024-01-01'),
    updatedAt: new Date('2024-01-01')
  },
  {
    name: 'Test Leader',
    email: '<EMAIL>',
    password: 'leader123',
    role: 'leader',
    status: 'active',
    emailVerified: true,
    preferences: {
      language: 'vi',
      theme: 'light',
      notifications: {
        email: true,
        push: true
      }
    },
    lastLogin: new Date(Date.now() - 2 * 60 * 60 * 1000), // 2 hours ago
    createdAt: new Date('2024-01-02'),
    updatedAt: new Date('2024-01-02')
  },
  {
    name: 'Senior Tester',
    email: '<EMAIL>',
    password: 'tester123',
    role: 'tester',
    status: 'active',
    emailVerified: true,
    preferences: {
      language: 'vi',
      theme: 'dark',
      notifications: {
        email: true,
        push: false
      }
    },
    lastLogin: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
    createdAt: new Date('2024-01-15'),
    updatedAt: new Date('2024-01-15')
  },
  {
    name: 'John Doe',
    email: '<EMAIL>',
    password: 'user123',
    role: 'tester',
    status: 'active',
    emailVerified: true,
    preferences: {
      language: 'en',
      theme: 'auto',
      notifications: {
        email: false,
        push: true
      }
    },
    lastLogin: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
    createdAt: new Date('2024-02-01'),
    updatedAt: new Date('2024-02-01')
  },
  {
    name: 'Jane Smith',
    email: '<EMAIL>',
    password: 'user123',
    role: 'tester',
    status: 'active',
    emailVerified: false,
    preferences: {
      language: 'vi',
      theme: 'light',
      notifications: {
        email: true,
        push: true
      }
    },
    lastLogin: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000), // 1 week ago
    createdAt: new Date('2024-02-10'),
    updatedAt: new Date('2024-02-10')
  },
  {
    name: 'Bob Johnson',
    email: '<EMAIL>',
    password: 'user123',
    role: 'tester',
    status: 'inactive',
    emailVerified: true,
    preferences: {
      language: 'en',
      theme: 'dark',
      notifications: {
        email: false,
        push: false
      }
    },
    lastLogin: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000), // 1 month ago
    createdAt: new Date('2024-01-20'),
    updatedAt: new Date('2024-01-25')
  },
  {
    name: 'Alice Brown',
    email: '<EMAIL>',
    password: 'user123',
    role: 'tester',
    status: 'suspended',
    emailVerified: true,
    preferences: {
      language: 'vi',
      theme: 'light',
      notifications: {
        email: true,
        push: true
      }
    },
    lastLogin: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000), // 2 weeks ago
    createdAt: new Date('2024-02-05'),
    updatedAt: new Date('2024-02-15')
  },
  {
    name: 'Charlie Wilson',
    email: '<EMAIL>',
    password: 'user123',
    role: 'tester',
    status: 'pending',
    emailVerified: false,
    preferences: {
      language: 'en',
      theme: 'auto',
      notifications: {
        email: true,
        push: true
      }
    },
    lastLogin: null,
    createdAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // 1 day ago
    updatedAt: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000)
  },
  {
    name: 'Diana Prince',
    email: '<EMAIL>',
    password: 'user123',
    role: 'tester',
    status: 'active',
    emailVerified: true,
    preferences: {
      language: 'vi',
      theme: 'dark',
      notifications: {
        email: true,
        push: true
      }
    },
    lastLogin: new Date(Date.now() - 3 * 60 * 60 * 1000), // 3 hours ago
    createdAt: new Date('2024-02-20'),
    updatedAt: new Date('2024-02-20')
  },
  {
    name: 'Eve Adams',
    email: '<EMAIL>',
    password: 'user123',
    role: 'tester',
    status: 'active',
    emailVerified: true,
    preferences: {
      language: 'en',
      theme: 'light',
      notifications: {
        email: false,
        push: true
      }
    },
    lastLogin: new Date(Date.now() - 1 * 60 * 60 * 1000), // 1 hour ago
    createdAt: new Date('2024-02-25'),
    updatedAt: new Date('2024-02-25')
  },
  {
    name: 'Frank Miller',
    email: '<EMAIL>',
    password: 'user123',
    role: 'tester',
    status: 'inactive',
    emailVerified: true,
    preferences: {
      language: 'vi',
      theme: 'auto',
      notifications: {
        email: true,
        push: false
      }
    },
    lastLogin: new Date(Date.now() - 45 * 24 * 60 * 60 * 1000), // 45 days ago
    createdAt: new Date('2024-01-10'),
    updatedAt: new Date('2024-01-30')
  }
];

// Function to hash passwords and prepare data for seeding
async function prepareMockUsers() {
  const hashedUsers = await Promise.all(
    mockUsers.map(async (user) => ({
      ...user,
      password: await bcrypt.hash(user.password, 10)
    }))
  );
  
  return hashedUsers;
}

// Function to get mock users for API testing (without hashing)
function getMockUsersForAPI() {
  return mockUsers.map(user => ({
    ...user,
    password: undefined // Remove password for API responses
  }));
}

module.exports = {
  mockUsers,
  prepareMockUsers,
  getMockUsersForAPI
};
