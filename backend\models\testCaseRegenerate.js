const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');
const { Schema } = mongoose;

const TestCaseRegenerateSchema = new Schema({
  test_case_id: {
    type: Schema.Types.ObjectId,
    ref: 'test_case',
    required: false
  },
  suite_id: {
    type: Schema.Types.ObjectId,
    ref: 'suite',
    required: true
  },
  test_case_version_id: {
    type: Schema.Types.ObjectId,
    ref: 'test_case_version',
    default: null
  },
  edit_reason: {
    type: String,
    trim: true
  },
  edit_detail: {
    type: String,
    trim: true
  },
  refinement_instruction: {
    type: String,
    trim: true
  },
  type: {
    type: Number,
    default: 1
  },
  regenerated_at: {
    type: Date,
    default: Date.now
  },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

TestCaseRegenerateSchema.plugin(softDeletePlugin);

TestCaseRegenerateSchema.statics.getLatestRegeneration = function(testCaseId) {
  return this.findOne({
    test_case_id: testCaseId,
    del_flag: 0
  }).sort({ regenerated_at: -1 });
};

TestCaseRegenerateSchema.statics.getRegenerationHistory = function(testCaseId) {
  return this.find({
    test_case_id: testCaseId,
    del_flag: 0
  }).sort({ regenerated_at: -1 });
};

module.exports = mongoose.model('test_case_regenerate', TestCaseRegenerateSchema); 