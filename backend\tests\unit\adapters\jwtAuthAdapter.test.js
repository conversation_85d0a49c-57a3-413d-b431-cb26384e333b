/**
 * Unit Tests cho JWTAuthAdapter
 * Comprehensive testing theo chuẩn doanh nghiệp
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const JWTAuthAdapter = require('../../../adapters/jwtAuthAdapter');
const { testConfig, resetMocks } = require('../../helpers/testSetup');
const { createMockUser } = require('../../helpers/authHelpers');

// Mock external dependencies
jest.mock('jsonwebtoken');
jest.mock('bcryptjs');
jest.mock('../../../config', () => ({
  jwtSecret: 'test_jwt_secret',
  jwtExpiresIn: '1h',
  jwtRefreshExpiresIn: '7d'
}));

describe('JWTAuthAdapter', () => {
  let authAdapter;
  
  beforeEach(() => {
    resetMocks();
    authAdapter = require('../../../adapters/jwtAuthAdapter');
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  describe('hashPassword', () => {
    it('should hash password successfully', async () => {
      // Arrange
      const password = 'testPassword123';
      const expectedHash = 'hashedPassword123';
      bcrypt.hash.mockResolvedValue(expectedHash);

      // Act
      const result = await authAdapter.hashPassword(password);

      // Assert
      expect(bcrypt.hash).toHaveBeenCalledWith(password, 10);
      expect(result).toBe(expectedHash);
    });

    it('should handle bcrypt hash error', async () => {
      // Arrange
      const password = 'testPassword123';
      const error = new Error('Bcrypt hash failed');
      bcrypt.hash.mockRejectedValue(error);

      // Act & Assert
      await expect(authAdapter.hashPassword(password)).rejects.toThrow('Bcrypt hash failed');
      expect(bcrypt.hash).toHaveBeenCalledWith(password, 10);
    });

    it('should handle empty password', async () => {
      // Arrange
      const password = '';
      bcrypt.hash.mockResolvedValue('hashedEmptyPassword');

      // Act
      const result = await authAdapter.hashPassword(password);

      // Assert
      expect(bcrypt.hash).toHaveBeenCalledWith(password, 10);
      expect(result).toBe('hashedEmptyPassword');
    });
  });

  describe('comparePassword', () => {
    it('should return true for matching password', async () => {
      // Arrange
      const password = 'testPassword123';
      const hash = 'hashedPassword123';
      bcrypt.compare.mockResolvedValue(true);

      // Act
      const result = await authAdapter.comparePassword(password, hash);

      // Assert
      expect(bcrypt.compare).toHaveBeenCalledWith(password, hash);
      expect(result).toBe(true);
    });

    it('should return false for non-matching password', async () => {
      // Arrange
      const password = 'wrongPassword';
      const hash = 'hashedPassword123';
      bcrypt.compare.mockResolvedValue(false);

      // Act
      const result = await authAdapter.comparePassword(password, hash);

      // Assert
      expect(bcrypt.compare).toHaveBeenCalledWith(password, hash);
      expect(result).toBe(false);
    });

    it('should handle bcrypt compare error', async () => {
      // Arrange
      const password = 'testPassword123';
      const hash = 'hashedPassword123';
      const error = new Error('Bcrypt compare failed');
      bcrypt.compare.mockRejectedValue(error);

      // Act & Assert
      await expect(authAdapter.comparePassword(password, hash)).rejects.toThrow('Bcrypt compare failed');
      expect(bcrypt.compare).toHaveBeenCalledWith(password, hash);
    });
  });

  describe('generateToken', () => {
    it('should generate token successfully', async () => {
      // Arrange
      const payload = { _id: 'user123', email: '<EMAIL>', role: 'user' };
      const expectedToken = 'generated.jwt.token';
      jwt.sign.mockReturnValue(expectedToken);

      // Act
      const result = await authAdapter.generateToken(payload);

      // Assert
      expect(jwt.sign).toHaveBeenCalledWith(payload, 'test_jwt_secret', { expiresIn: '1h' });
      expect(result).toBe(expectedToken);
    });

    it('should handle JWT sign error', async () => {
      // Arrange
      const payload = { _id: 'user123', email: '<EMAIL>', role: 'user' };
      const error = new Error('JWT sign failed');
      jwt.sign.mockImplementation(() => { throw error; });

      // Act & Assert
      await expect(authAdapter.generateToken(payload)).rejects.toThrow('JWT sign failed');
    });
  });

  describe('generateRefreshToken', () => {
    it('should generate refresh token successfully', async () => {
      // Arrange
      const payload = { _id: 'user123', email: '<EMAIL>', role: 'user' };
      const expectedToken = 'generated.refresh.token';
      jwt.sign.mockReturnValue(expectedToken);

      // Act
      const result = await authAdapter.generateRefreshToken(payload);

      // Assert
      expect(jwt.sign).toHaveBeenCalledWith(payload, 'test_jwt_secret', { expiresIn: '7d' });
      expect(result).toBe(expectedToken);
    });
  });
});
