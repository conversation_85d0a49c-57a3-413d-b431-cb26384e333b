const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
// const AWS = require('aws-sdk');
const HeadingExtractor = require('./headingExtractor');
const config = require('../../config');
const multer = require('multer');
const logger = require('./loggerService');

const getSuiteModel = () => require('../../models/suite');

async function generateUniqueId() {
  let newId;
  let exists = true;
  
  while (exists) {
    newId = new mongoose.Types.ObjectId();
    const Suite = getSuiteModel();
    const existingRecord = await Suite.findById(newId);
    exists = !!existingRecord;
  }
  
  return newId;
}

// setting multer to upload file
const storage = multer.diskStorage({
  destination: function (req, file, cb) {
    try {
      const uploadPath = path.join(__dirname, '../../uploads', req.suiteId.toString());
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }
      cb(null, uploadPath);
    } catch (error) {
      logger.error('Lỗi khi upload file', error);
      cb(error);
    }
  },
  filename: function (req, file, cb) {
    // Generate unique filename: timestamp-random-extension
    const uniqueSuffix = Date.now() + '-' + Math.random().toString(36).substring(2, 15);
    const ext = path.extname(file.originalname);
    const newFilename = uniqueSuffix + ext;
    cb(null, newFilename);
  }
});

const fileFilter = function (req, file, cb) {
  const allowedMimes = [
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    // 'application/msword',
    'application/pdf'
  ];
  
  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('FILE_TYPE_ERROR'), false);
  }
};

const imageFilter = function (req, file, cb) {
  const allowedMimes = [
    'image/jpeg',
    'image/jpg', 
    'image/png',
    'image/gif',
    'image/webp',
    'image/svg+xml'
  ];
  
  if (allowedMimes.includes(file.mimetype)) {
    cb(null, true);
  } else {
    cb(new Error('IMAGE_TYPE_ERROR'), false);
  }
};

function isFilesField(fieldname) {
  return fieldname === 'files' || /^files\[\d+\]$/.test(fieldname);
}

function isImagesField(fieldname) {
  return fieldname === 'images' || /^images\[\d+\]$/.test(fieldname);
}

const uploadConfig = {
  storage: storage,
  fileFilter: fileFilter,
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
};

// Middleware upload multiple files
const uploadMultipleFiles = multer(uploadConfig).array('files', 10); //max 10 files

async function extractHeadingsFromFiles(files, options = {}) {
  try {
    // Validate files
    if (!files || files.length === 0) {
      throw new Error('NO_FILES');
    }

    // Validate file types
    const invalidFiles = files.filter(file => 
      file.mimetype !== 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    );
    
    if (invalidFiles.length > 0) {
      throw new Error('INVALID_FILE_TYPE');
    }

    const extractOptions = {
      includeContent: options.includeContent !== false,
      maxLevel: parseInt(options.maxLevel) || 6
    };

    const extractor = new HeadingExtractor();

    const filePaths = files.map(file => file.path);

    const extractResult = await extractor.extractMultipleHeadings(filePaths, extractOptions);

    const result = {
      ...extractResult,
      uploadedFiles: files.map(file => ({
        originalName: file.originalname,
        filename: file.filename,
        size: file.size,
        uploadPath: file.path
      }))
    };

    return result;

  } catch (error) {
    logger.error('Lỗi khi extract headings trong fileService:', error);
    throw error;
  }
}

function cleanupFiles(folderPath, delay = 1000) {
  setTimeout(() => {
    try {
      if (fs.existsSync(folderPath)) {
        fs.rmSync(folderPath, { recursive: true, force: true });
        console.log(`Đã xóa thư mục suite: ${folderPath}`);
      }
    } catch (cleanupError) {
      logger.error('Lỗi khi cleanup thư mục suite:', cleanupError);
    }
  }, delay);
}

function handleUploadError(err, req, res, next) {
  const errors = {};

  if (err instanceof multer.MulterError) {
    switch (err.code) {
      case 'LIMIT_FILE_SIZE':
        errors.files = 'File quá lớn. Kích thước tối đa là 10MB.';
        break;
      case 'LIMIT_FILE_COUNT':
        errors.files = 'Số lượng file vượt quá giới hạn. Tối đa 10 files.';
        break;
      case 'LIMIT_UNEXPECTED_FILE':
        errors.files = 'Field name không đúng. Hỗ trợ "files", "images" hoặc dạng chỉ số như files[0], images[1].';
        break;
      default:
        errors.files = 'Lỗi khi upload file.';
    }
    return res.status(400).json({ errors });
  }

  if (err.message === 'FILE_TYPE_ERROR') {
    errors.files = 'Chỉ chấp nhận file DOCX, PDF.';
    return res.status(400).json({ errors });
  }

  if (err.message === 'IMAGE_TYPE_ERROR') {
    errors.images = 'Chỉ chấp nhận file hình ảnh (JPG, PNG, GIF, WEBP, SVG).';
    return res.status(400).json({ errors });
  }

  if (err.message === 'INVALID_FIELD_NAME') {
  errors.files = 'Field name không hợp lệ. Chỉ chấp nhận "files", "images" hoặc dạng chỉ số như files[0], images[0].';
    return res.status(400).json({ errors });
  }

  if (err.message === 'NO_FILES') {
    errors.files = 'Vui lòng chọn ít nhất một file DOCX.';
    return res.status(400).json({ errors });
  }

  if (err.message === 'INVALID_FILE_TYPE') {
    errors.files = 'Một số file không đúng định dạng DOCX.';
    return res.status(400).json({ errors });
  }

  next(err);
}

const createSuiteId = async (req, res, next) => {
  try {
    if (req.params.suite_id) {
      req.suiteId = req.params.suite_id;
    } else {
      req.suiteId = await generateUniqueId();
      logger.info(`Đã tạo suite_id mới: ${req.suiteId}`);
    }
    next();
  } catch (error) {
    logger.error('Lỗi khi tạo suite ID', error, req.originalUrl);
    return res.status(500).json({ 
      errors: { server: 'Lỗi khi tạo suite ID.' }
    });
  }
};

const mindmapStorage = multer.diskStorage({
  destination: function (req, file, cb) {
    try {
      let subFolder;
      if (isFilesField(file.fieldname)) {
        subFolder = 'documents';
      } else if (isImagesField(file.fieldname)) {
        subFolder = 'images';
      } else {
        return cb(new Error('INVALID_FIELD_NAME'), false);
      }
      const uploadPath = path.join(__dirname, '../../uploads', 'extract_mindmap', req.suiteId.toString(), subFolder);
      if (!fs.existsSync(uploadPath)) {
        fs.mkdirSync(uploadPath, { recursive: true });
      }
      cb(null, uploadPath);
    } catch (error) {
      logger.error('Lỗi khi upload file', error);
      cb(error);
    }
  },
  filename: function (req, file, cb) {
    // Generate unique filename: timestamp-random-extension
    const uniqueSuffix = Date.now() + '-' + Math.random().toString(36).substring(2, 15);
    const ext = path.extname(file.originalname);
    const newFilename = uniqueSuffix + ext;
    cb(null, newFilename);
  }
});

const uploadFilesAndImagesForMindmap = multer({
  storage: mindmapStorage,
  fileFilter: function (req, file, cb) {
    if (isFilesField(file.fieldname)) {
      return fileFilter(req, file, cb);
    } else if (isImagesField(file.fieldname)) {
      return imageFilter(req, file, cb);
    }
    cb(new Error('INVALID_FIELD_NAME'), false);
  },
  limits: {
    fileSize: 10 * 1024 * 1024 // 10MB
  }
}).any();

const convertToRelativePath = (absolutePath) => {
  if (!absolutePath) return '';

  const uploadsIndex = absolutePath.indexOf('uploads');
  if (uploadsIndex !== -1) {
    return absolutePath.substring(uploadsIndex).replace(/\\/g, '/');
  }
  
  return absolutePath.replace(/\\/g, '/');
};

const normalizeUploadedFiles = (req, res, next) => {
  try {
    const uploaded = Array.isArray(req.files) ? req.files : [];

    const docs = [];
    const imgs = [];

    for (const f of uploaded) {
      
      f.path = convertToRelativePath(f.path);
      
      if (isFilesField(f.fieldname)) {
        docs.push(f);
      } else if (isImagesField(f.fieldname)) {
        imgs.push(f);
      }
    }

    req.files = docs;
    req.images = imgs;

    next();
  } catch (error) {
    logger.error('Lỗi khi chuẩn hóa uploaded files:', error);
    next(error);
  }
};

const fixFilenameEncoding = (req, res, next) => {
  try {
    if (req.files && Array.isArray(req.files)) {
      req.files = req.files.map(file => {
        if (file.originalname) {
          try {
            if (!/^[\x00-\x7F]*$/.test(file.originalname)) {
              file.originalname = Buffer.from(file.originalname, 'latin1').toString('utf8');
            }
          } catch (decodeError) {
            logger.error('Không thể decode tên file:', file.originalname);
          }
        }
        return file;
      });
    }
    next();
  } catch (error) {
    logger.error('Lỗi khi fix filename encoding:', error);
    next(error);
  }
};

const uploadFilesForExtractHeadings = [
  createSuiteId,
  uploadMultipleFiles,
  handleUploadError
];

const uploadFilesForExtractMindmap = [
  createSuiteId,
  uploadFilesAndImagesForMindmap,
  fixFilenameEncoding,
  normalizeUploadedFiles,
  handleUploadError
];

module.exports = {
  // uploadFile,
  uploadMultipleFiles,
  handleUploadError,
  extractHeadingsFromFiles,
  cleanupFiles,
  uploadFilesForExtractHeadings,
  generateUniqueId,
  uploadFilesForExtractMindmap,
  uploadFilesAndImagesForMindmap,
  normalizeUploadedFiles,
  fixFilenameEncoding
};