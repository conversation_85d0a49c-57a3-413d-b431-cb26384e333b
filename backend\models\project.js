const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');

const projectSchema = new mongoose.Schema({
  name: { type: String, required: true },
  description: { type: String, required: false },
  created_by: { type: String, ref: 'user', required: true },
  display_mode: { type: String, enum: ['private', 'internal'], default: 'private' },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

projectSchema.plugin(softDeletePlugin);

module.exports = mongoose.model('project', projectSchema);