const axios = require('axios');

async function testSimpleAPI() {
  try {
    console.log('🧪 Testing API endpoints...');
    
    // Test 1: Get users without auth (should fail)
    try {
      const usersResponse = await axios.get('http://localhost:3000/users');
      console.log('❌ Get users without auth should fail, but got:', usersResponse.status);
    } catch (error) {
      console.log('✅ Get users without auth correctly failed:', error.response?.status);
    }
    
    // Test 2: Login as admin to get token
    try {
      const loginResponse = await axios.post('http://localhost:3000/users/login', {
        email: '<EMAIL>',
        password: 'admin123'
      });
      
      console.log('✅ Admin login successful');
      const token = loginResponse.data.token;
      
      // Test 3: Get users with auth
      const usersResponse = await axios.get('http://localhost:3000/users', {
        headers: {
          'Authorization': `Bear<PERSON> ${token}`
        }
      });
      
      console.log('✅ Get users with auth successful');
      console.log('📊 Response structure:', {
        hasUsers: !!usersResponse.data.users,
        usersCount: usersResponse.data.users?.length || 0,
        hasPagination: !!usersResponse.data.pagination,
        pagination: usersResponse.data.pagination
      });
      
      if (usersResponse.data.users && usersResponse.data.users.length > 0) {
        console.log('📋 First user:', {
          name: usersResponse.data.users[0].name,
          email: usersResponse.data.users[0].email,
          role: usersResponse.data.users[0].role
        });
      }
      
    } catch (error) {
      console.log('❌ Auth test failed:', error.response?.data || error.message);
    }
    
  } catch (error) {
    console.error('❌ Test failed:', error.message);
  }
}

testSimpleAPI();
