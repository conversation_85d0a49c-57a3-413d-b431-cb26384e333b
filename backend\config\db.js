// MongoDB configuration with Atlas support
const getMongoConfig = () => {
  // Check if we're using Atlas (cloud) or local MongoDB
  const useAtlas = process.env.USE_MONGO_ATLAS === 'true';
  
  if (useAtlas) {
    // MongoDB Atlas configuration
    return {
      uri: process.env.MONGO_ATLAS_URI || process.env.MONGO_URI,
      options: {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        // Atlas-specific options
        retryWrites: true,
        w: 'majority',
        // Connection pool settings for Atlas
        maxPoolSize: 10,
        minPoolSize: 2,
        maxIdleTimeMS: 30000,
        serverSelectionTimeoutMS: 5000,
        socketTimeoutMS: 45000,
      }
    };
  } else {
    // Local MongoDB configuration
    return {
      uri: process.env.MONGO_URI || 'mongodb://localhost:27017/gentest',
      options: {
        useNewUrlParser: true,
        useUnifiedTopology: true,
        // Connection pool settings for local MongoDB
        maxPoolSize: 10,
        minPoolSize: 2,
        maxIdleTimeMS: 60000, // Keep connections alive longer
        serverSelectionTimeoutMS: 5000,
      }
    };
  }
};

module.exports = {
  // MongoDB configuration
  mongo: getMongoConfig(),
  
  // Legacy support - keep the old mongoUri for backward compatibility
  mongoUri: getMongoConfig().uri,
  
  // Database name (extracted from URI or specified separately)
  dbName: process.env.DB_NAME || 'gentest',
  
  // Atlas specific settings
  useAtlas: process.env.USE_MONGO_ATLAS === 'true',
};
