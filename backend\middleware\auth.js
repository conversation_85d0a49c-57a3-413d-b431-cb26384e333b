// Auth middleware using AuthService with improved security
const authService = require('../adapters/jwtAuthAdapter');
const { isTokenBlacklisted } = require('../controllers/userController');
const config = require('../config');

module.exports = async function (req, res, next) {
  try {
    const authHeader = req.headers['authorization'];
    
    if (!authHeader) {
      return res.status(401).json({ 
        message: 'Access denied. No token provided',
        code: 'NO_TOKEN' 
      });
    }
    
    if (!authHeader.startsWith('Bearer ')) {
      return res.status(401).json({ 
        message: 'Access denied. Invalid token format',
        code: 'INVALID_FORMAT' 
      });
    }
    
    const token = authHeader.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ 
        message: 'Access denied. Token missing',
        code: 'TOKEN_MISSING' 
      });
    }
    
    // Check if token is blacklisted (if feature is enabled)
    if (config.security.enableTokenBlacklist && isTokenBlacklisted(token)) {
      return res.status(401).json({ 
        message: 'Access denied. Token has been revoked',
        code: 'TOKEN_REVOKED' 
      });
    }
    
    // Check if token is expired before verification (optimization)
    if (authService.isTokenExpired(token)) {
      return res.status(401).json({ 
        message: 'Access denied. Token expired',
        code: 'TOKEN_EXPIRED' 
      });
    }
    
    const decoded = await authService.verifyToken(token);
    req.user = decoded;
    req.token = token; // Store token for potential blacklist functionality
    
    next();
  } catch (error) {
    console.error('Auth middleware error:', error.message);
    
    if (error.message === 'Token expired') {
      return res.status(401).json({ 
        message: 'Access denied. Token expired',
        code: 'TOKEN_EXPIRED' 
      });
    }
    
    if (error.message === 'Invalid token') {
      return res.status(401).json({ 
        message: 'Access denied. Invalid token',
        code: 'INVALID_TOKEN' 
      });
    }
    
    // Generic error for security reasons
    return res.status(401).json({ 
      message: 'Access denied. Authentication failed',
      code: 'AUTH_FAILED' 
    });
  }
};
