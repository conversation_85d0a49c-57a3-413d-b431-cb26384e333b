const fs = require('fs');
const path = require('path');
const chatworkService = require('./chatworkService');

class LoggerService {
    constructor() {
        this.logsDir = path.join(__dirname, '../../logs');
        this.ensureLogsDirectory();
    }

    ensureLogsDirectory() {
        if (!fs.existsSync(this.logsDir)) {
            fs.mkdirSync(this.logsDir, { recursive: true });
        }
    }

    formatLogEntry(level, message, context = {}) {
        const timestamp = this.getFormattedTimestamp();
        
        let logEntry = `${timestamp} [${level.toUpperCase()}] ${message}`;
        
        if (context && Object.keys(context).length > 0) {
            if (context.error && context.error instanceof Error) {
                logEntry += ' ' + JSON.stringify({
                    ...context,
                    error: {
                        message: context.error.message,
                        stack: context.error.stack,
                        name: context.error.name
                    }
                });
            } else if (context.error && typeof context.error === 'object' && context.error.stack) {
                logEntry += ' ' + JSON.stringify({
                    ...context,
                    error: {
                        message: context.error.message,
                        stack: context.error.stack,
                        name: context.error.name
                    }
                });
            } else {
                logEntry += ' ' + JSON.stringify(context);
            }
        }
        
        return logEntry + '\n';
    }

    writeToFile(filename, content) {
        const filePath = path.join(this.logsDir, filename);
        // Ensure directory exists
        const dir = path.dirname(filePath);
        if (!fs.existsSync(dir)) {
            fs.mkdirSync(dir, { recursive: true });
        }
        fs.appendFileSync(filePath, content);
    }

    getCurrentDateString() {
        return new Date().toISOString().split('T')[0];
    }

    getFormattedTimestamp() {
        const now = new Date();
        return now.getFullYear() + '-' + 
               String(now.getMonth() + 1).padStart(2, '0') + '-' + 
               String(now.getDate()).padStart(2, '0') + ' ' + 
               String(now.getHours()).padStart(2, '0') + ':' + 
               String(now.getMinutes()).padStart(2, '0') + ':' + 
               String(now.getSeconds()).padStart(2, '0');
    }

    error(message, contextOrError = {}, url = null) {
        const dateString = this.getCurrentDateString();
        const filename = `${dateString}/error.log`;
        
        let context = {};
        if (contextOrError instanceof Error) {
            context = { error: contextOrError };
        } else if (contextOrError && typeof contextOrError === 'object') {
            context = contextOrError;
        }
        
        let fullErrorMessage = message;
        if (context.error) {
            if (context.error instanceof Error) {
                fullErrorMessage += ' ' + context.error.stack;
            } else if (typeof context.error === 'object' && context.error.stack) {
                fullErrorMessage += ' ' + context.error.stack;
            } else if (typeof context.error === 'string') {
                fullErrorMessage += ' ' + context.error;
            }
        }
        
        const logEntry = this.formatLogEntry('error', fullErrorMessage, context);
        this.writeToFile(filename, logEntry);
        
        console.error(`${this.getFormattedTimestamp()} [ERROR] ${fullErrorMessage}`, context);
        this.sendErrorToChatwork(fullErrorMessage, context, url);
    }

    logError(message, error, url = null) {
        const context = { error: error };
        this.error(message, context, url);
    }

    warning(message, context = {}) {
        const dateString = this.getCurrentDateString();
        const filename = `${dateString}/warning.log`;
        const logEntry = this.formatLogEntry('warning', message, context);
        this.writeToFile(filename, logEntry);
        
        console.warn(`${this.getFormattedTimestamp()} [WARNING] ${message}`, context);
    }

    info(message, context = {}) {
        const dateString = this.getCurrentDateString();
        const filename = `${dateString}/info.log`;
        const logEntry = this.formatLogEntry('info', message, context);
        this.writeToFile(filename, logEntry);
        
        console.info(`${this.getFormattedTimestamp()} [INFO] ${message}`, context);
    }

    debug(message, context = {}) {
        const dateString = this.getCurrentDateString();
        const filename = `${dateString}/debug.log`;
        const logEntry = this.formatLogEntry('debug', message, context);
        this.writeToFile(filename, logEntry);
        
        console.debug(`${this.getFormattedTimestamp()} [DEBUG] ${message}`, context);
    }

    channel(channelName) {
        const self = this;
        return {
            error: (message, context = {}, url = null) => {
                const dateString = self.getCurrentDateString();
                const filename = `${dateString}/${channelName}-error.log`;
                
                let fullErrorMessage = message;
                if (context.error) {
                    if (context.error instanceof Error) {
                        fullErrorMessage += ' ' + context.error.stack;
                    } else if (typeof context.error === 'object' && context.error.stack) {
                        fullErrorMessage += ' ' + context.error.stack;
                    } else if (typeof context.error === 'string') {
                        fullErrorMessage += ' ' + context.error;
                    }
                }
                
                const logEntry = self.formatLogEntry('error', fullErrorMessage, context);
                self.writeToFile(filename, logEntry);
                
                console.error(`${self.getFormattedTimestamp()} [${channelName}] [ERROR] ${fullErrorMessage}`, context);
                self.sendErrorToChatwork(`[${channelName}] ${fullErrorMessage}`, context, url);
            },
            logError: (message, error, url = null) => {
                const context = { error: error };
                self.channel(channelName).error(message, context, url);
            },
            warning: (message, context = {}) => {
                const dateString = self.getCurrentDateString();
                const filename = `${dateString}/${channelName}-warning.log`;
                const logEntry = self.formatLogEntry('warning', message, context);
                self.writeToFile(filename, logEntry);
                
                console.warn(`${self.getFormattedTimestamp()} [${channelName}] [WARNING] ${message}`, context);
            },
            info: (message, context = {}) => {
                const dateString = self.getCurrentDateString();
                const filename = `${dateString}/${channelName}-info.log`;
                const logEntry = self.formatLogEntry('info', message, context);
                self.writeToFile(filename, logEntry);
                
                console.info(`${self.getFormattedTimestamp()} [${channelName}] [INFO] ${message}`, context);
            },
            debug: (message, context = {}) => {
                const dateString = self.getCurrentDateString();
                const filename = `${dateString}/${channelName}-debug.log`;
                const logEntry = self.formatLogEntry('debug', message, context);
                self.writeToFile(filename, logEntry);
                
                console.debug(`${self.getFormattedTimestamp()} [${channelName}] [DEBUG] ${message}`, context);
            }
        };
    }

    // cleanOldLogs(daysToKeep = 30) {
    //     try {
    //         const directories = fs.readdirSync(this.logsDir);
    //         const cutoffDate = new Date();
    //         cutoffDate.setDate(cutoffDate.getDate() - daysToKeep);

    //         directories.forEach(dir => {
    //             const dirPath = path.join(this.logsDir, dir);
    //             const stats = fs.statSync(dirPath);
                
    //             if (stats.isDirectory() && stats.mtime < cutoffDate) {
    //                 if (/^\d{4}-\d{2}-\d{2}$/.test(dir)) {
    //                     fs.rmSync(dirPath, { recursive: true, force: true });
    //                     this.info(`Deleted old log directory: ${dir}`);
    //                 }
    //             }
    //         });
    //     } catch (error) {
    //         this.error('Failed to clean old logs', error);
    //     }
    // }

    async sendErrorToChatwork(message, context = {}, url = null) {
        try {
            await chatworkService.sendErrorNotification(message, context, 'error.log', url);
        } catch (error) {
            console.error('Failed to send error to Chatwork:', error.message);
        }
    }
}

const logger = new LoggerService();

module.exports = logger; 