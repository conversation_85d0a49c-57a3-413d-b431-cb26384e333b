/**
 * Auth Test Helpers - Utilities cho Auth Module Testing
 * Theo chuẩn doanh nghiệp với comprehensive test data và utilities
 */

const jwt = require('jsonwebtoken');
const bcrypt = require('bcryptjs');
const { testConfig } = require('./testSetup');

/**
 * Tạo mock user data cho testing
 */
const createMockUser = (overrides = {}) => {
  const defaultUser = {
    _id: '507f1f77bcf86cd799439011',
    name: 'Test User',
    email: '<EMAIL>',
    password: 'hashedPassword123',
    role: 'user',
    status: 'active',
    emailVerified: true,
    loginAttempts: 0,
    lockUntil: null,
    createdAt: new Date(),
    updatedAt: new Date()
  };
  
  return { ...defaultUser, ...overrides };
};

/**
 * Tạo multiple mock users với different roles
 */
const createMockUsers = () => {
  return {
    admin: createMockUser({
      _id: '507f1f77bcf86cd799439012',
      name: 'Admin User',
      email: '<EMAIL>',
      role: 'admin'
    }),
    testManager: createMockUser({
      _id: '507f1f77bcf86cd799439013',
      name: 'Test Manager',
      email: '<EMAIL>',
      role: 'test_manager'
    }),
    user: createMockUser({
      _id: '507f1f77bcf86cd799439014',
      name: 'Regular User',
      email: '<EMAIL>',
      role: 'user'
    }),
    inactiveUser: createMockUser({
      _id: '507f1f77bcf86cd799439015',
      name: 'Inactive User',
      email: '<EMAIL>',
      role: 'user',
      status: 'inactive'
    }),
    lockedUser: createMockUser({
      _id: '507f1f77bcf86cd799439016',
      name: 'Locked User',
      email: '<EMAIL>',
      role: 'user',
      loginAttempts: 5,
      lockUntil: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes from now
    })
  };
};

/**
 * Tạo valid JWT token cho testing
 */
const createValidToken = (payload = {}) => {
  const defaultPayload = {
    _id: '507f1f77bcf86cd799439011',
    email: '<EMAIL>',
    role: 'user',
    iat: Math.floor(Date.now() / 1000)
  };
  
  const tokenPayload = { ...defaultPayload, ...payload };
  
  return jwt.sign(tokenPayload, testConfig.jwtSecret, {
    expiresIn: testConfig.jwtExpiresIn
  });
};

/**
 * Tạo expired JWT token cho testing
 */
const createExpiredToken = (payload = {}) => {
  const defaultPayload = {
    _id: '507f1f77bcf86cd799439011',
    email: '<EMAIL>',
    role: 'user',
    iat: Math.floor(Date.now() / 1000) - 7200, // 2 hours ago
    exp: Math.floor(Date.now() / 1000) - 3600  // 1 hour ago (expired)
  };
  
  const tokenPayload = { ...defaultPayload, ...payload };
  
  return jwt.sign(tokenPayload, testConfig.jwtSecret);
};

/**
 * Tạo invalid JWT token cho testing
 */
const createInvalidToken = () => {
  return 'invalid.jwt.token.format';
};

/**
 * Tạo malformed JWT token cho testing
 */
const createMalformedToken = () => {
  return 'malformed.token';
};

/**
 * Hash password cho testing
 */
const hashPassword = async (password) => {
  return await bcrypt.hash(password, testConfig.bcryptSaltRounds);
};

/**
 * Tạo mock request object
 */
const createMockRequest = (overrides = {}) => {
  const defaultRequest = {
    headers: {},
    body: {},
    params: {},
    query: {},
    user: null,
    token: null
  };
  
  return { ...defaultRequest, ...overrides };
};

/**
 * Tạo mock response object với jest spies
 */
const createMockResponse = () => {
  const res = {
    status: jest.fn().mockReturnThis(),
    json: jest.fn().mockReturnThis(),
    send: jest.fn().mockReturnThis(),
    cookie: jest.fn().mockReturnThis(),
    clearCookie: jest.fn().mockReturnThis()
  };
  
  return res;
};

/**
 * Tạo mock next function
 */
const createMockNext = () => {
  return jest.fn();
};

/**
 * Tạo authorization header với Bearer token
 */
const createAuthHeader = (token) => {
  return `Bearer ${token}`;
};

/**
 * Test data cho various scenarios
 */
const testScenarios = {
  validLogin: {
    email: '<EMAIL>',
    password: 'password123'
  },
  invalidEmail: {
    email: 'invalid-email',
    password: 'password123'
  },
  invalidPassword: {
    email: '<EMAIL>',
    password: '123' // too short
  },
  nonExistentUser: {
    email: '<EMAIL>',
    password: 'password123'
  },
  validRegistration: {
    name: 'New User',
    email: '<EMAIL>',
    password: 'password123',
    role: 'user'
  },
  invalidRegistration: {
    name: '', // empty name
    email: 'invalid-email',
    password: '123', // too short
    role: 'invalid_role'
  }
};

module.exports = {
  createMockUser,
  createMockUsers,
  createValidToken,
  createExpiredToken,
  createInvalidToken,
  createMalformedToken,
  hashPassword,
  createMockRequest,
  createMockResponse,
  createMockNext,
  createAuthHeader,
  testScenarios
};
