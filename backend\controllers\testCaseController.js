const TestCase = require('../models/testCase');
const Suite = require('../models/suite');
const TestCaseVersion = require('../models/testCaseVersion');
const TemplateRepository = require('../repositories/templateRepository');
const openaiService = require('../services/core/openaiService');
const config = require('../config');
const ejs = require('ejs');
const path = require('path');
const mongoose = require('mongoose');
const TestCaseRepository = require('../repositories/testCaseRepository');
const SuiteRepository = require('../repositories/suiteRepository');
const FeatureRepository = require('../repositories/featureRepository');
const TestCaseHistory = require('../models/testCaseHistory');
const TestCaseRegenerateRepository = require('../repositories/testCaseRegenerateRepository');
const CheckItemRepository = require('../repositories/checkItemRepository');
const MindmapBuilder = require('../services/mindmapBuilder');
const logger = require('../services/core/loggerService');
const TestCaseHistoryRepository = require('../repositories/testCaseHistoryRepository');
const { getUserLogin } = require('../utils/common');
const pythonApiService = require('../services/core/pythonApiService');
const MindmapSnapshotRepository = require('../repositories/mindmapSnapshotRepository');

const testCaseController = {
    async genenrateTestCase(req, res) {
        // const session = await mongoose.startSession();
        // session.startTransaction();

        try {
            // build prompt
            logger.info('==== START GENERATE TEST CASE ====');
            logger.info('== Start build prompt ==');
            const prompt = await testCaseController.buildPrompt(req, res);
            logger.info('== End build prompt ==');
            
            // call openai
            logger.info('== Start call OpenAI ==');
            const { id, output } = await testCaseController.callOpenAi(req, res, prompt);
            const testCases = output;
            logger.info('== End call OpenAI ==');

            // Create suite record
            let suiteId = req.body.suite_id;
            
            if (suiteId) {
                const existingSuite = await SuiteRepository.findOne({ _id: suiteId });
                if (existingSuite) {
                    suiteId = new mongoose.Types.ObjectId();
                }
            } else {
                suiteId = new mongoose.Types.ObjectId();
            }

            const suiteData = {
                _id: suiteId,
                project_id: req.body.project_id,
                field_type: req.body.field_type,
                objective: req.body.objective,
                user_id: req.body.user_id,
                previous_response_id: id,
                feature_name: (() => {
                    const fieldType = req.body.field_type.split('_')[0];
                    switch (fieldType) {
                        case 'web':
                            return req.body.detail.web_manual.feature_spec.feature;
                        case 'mobile':
                            return req.body.detail.mobile_manual.feature_spec.feature;
                        case 'api':
                            return req.body.detail.api_manual.endpoint_spec.endpoint;
                        default:
                            return '';
                    }
                })(),
                approval_status: 'draft',
                document: {
                    files: req.body.documents?.file_upload || []
                },
                prompt: {
                    input: prompt,
                    output: testCases
                }
            };

            const suite = new Suite(suiteData);
            // await suite.save({ session });
            await suite.save();

            // Save test cases
            const testCasePromises = testCases.map(testCase => {
                const testCaseData = {
                    suite_id: suite._id,
                    detail: {}
                };

                const caseType = `${req.body.field_type.split('_')[0]}_manual`;
                testCaseData.detail[caseType] = {
                    test_case_id: testCase.test_case_id,
                    test_case_name: testCase.test_case_name,
                    priority: testCase.priority,
                    pre_condition: testCase.pre_condition,
                    steps: Array.isArray(testCase.steps) ? testCase.steps.join('\n') : testCase.steps,
                    test_data: typeof testCase.test_data === 'object' ? Object.entries(testCase.test_data).map(([key, value]) => `${key}: ${value}`).join('\n') : testCase.test_data,
                    expected_result: Array.isArray(testCase.expected_result) ? testCase.expected_result.join('\n') : testCase.expected_result,
                    test_case_type: testCase.test_case_type
                };

                if (caseType === 'mobile_manual' && testCase.device) {
                    testCaseData.detail.mobile_manual.device = testCase.device;
                }

                if (caseType === 'api_manual') {
                    testCaseData.detail.api_manual.http_method = testCase.http_method;
                    testCaseData.detail.api_manual.header = testCase.header;
                    testCaseData.detail.api_manual.request_body = testCase.request_body;
                }

                const newTestCase = new TestCase(testCaseData);
                // return newTestCase.save({ session });
                return newTestCase.save();
            });

            const testCasesSaved = await Promise.all(testCasePromises);

            const testCaseVersionData = {
                suite_id: suite._id,
                created_by: req.body.user_id,
                test_cases: testCasesSaved
            };

            const testCaseVersion = new TestCaseVersion(testCaseVersionData);
            await testCaseVersion.save();

            // await session.commitTransaction();
            res.status(200).json(testCasesSaved);
            logger.info('==== End generate test case ====');

        } catch (error) {
            // await session.abortTransaction();
            logger.error('Lỗi khi tạo test cases', error, req.originalUrl);
            res.status(500).json({ 
                message: error.message || 'Lỗi khi tạo test cases'
            });
        } finally {
            // session.endSession();
        }
    },

    async buildPrompt(req, res) {
        try {
            const params = req.body;
            if (!params) {
                return res.status(400).json({ message: 'Dữ liệu không hợp lệ.' });
            }
            const { field_type, objective, template_id, version = 'v2' } = params;
      
            const getTemplatePath = (basePath, version) => {
                return basePath.replace('.ejs', `_${version}.ejs`);
            };

            const templateConfigDefault = {
              'web_application': {
                'test_case_manual': {
                  type: 'web-manual',
                  path: getTemplatePath('../templates/prompt/manual/web.ejs', version)
                },
                'script_automation': {
                  type: 'web-auto', 
                  path: '../templates/prompt/auto/web.ejs'
                }
              },
              'mobile_application': {
                'test_case_manual': {
                  type: 'mobile-manual',
                  path: getTemplatePath('../templates/prompt/manual/mobile.ejs', version)
                }
              },
              'api_web_service': {
                'test_case_manual': {
                  type: 'api-manual',
                  path: getTemplatePath('../templates/prompt/manual/api.ejs', version)
                }
              }
            };

            const templateConfig = templateConfigDefault[field_type]?.[objective];

            if (!templateConfig) {
                return res.status(400).json({ message: 'Không hỗ trợ loại template này.' });
            }
            const { type, path: templatePath } = templateConfig;

            const template = await TemplateRepository.findOne(
                template_id ? 
                { _id: template_id } : 
                { type, is_default: 1 }
            );
            if (!template) {
                return res.status(400).json({ message: 'Không tìm thấy template mặc định.' });
            }
            const prompt = await ejs.renderFile(
                path.join(__dirname, templatePath),
                {
                    testScript: params,
                    template,
                    language: config.prompt_language[template.prompt_language] || 'Tiếng Anh'
                }
            );

            return prompt;

        } catch (error) {
            logger.error('Lỗi khi build prompt', error, req.originalUrl);
            return res.status(400).json({ message: error.message });
        }
    },

    async callOpenAi(req, res, prompt, previous_response_id = null) {
        try {
            if (!prompt) {
                return res.status(400).json({ message: 'Prompt không được để trống.' });
            }

            const { id, text } = await openaiService.callOpenAiForExtract(prompt, previous_response_id);

            const parsed = typeof text === 'string' ? openaiService.parseJsonSafely(text) : text;

            // const parsedOutput = openaiService.parseJsonSafely(text);

            // const validatedOutput = openaiService.validateOutput(parsedOutput);

            if (!id) {
                return res.status(500).json({ message: 'Lỗi khi gọi OpenAI.' });
            }

            return {id, output: parsed};
        } catch (error) {
            logger.error('Lỗi khi call OpenAI', error, req.originalUrl);
            return res.status(400).json({ message: error.message });
        }
    },

    async getListTestCases(req, res) {
        try {
            const { suite_id } = req.query;
            let testCases;
            if (!suite_id) {
                testCases = await TestCaseRepository.findAll();
            } else {
                testCases = await TestCaseRepository.findAll({ suite_id }, { sort: { _id: 1 } });
            }            
            const testCasesWithHistories = await Promise.all(
                testCases.map(async (testCase) => {
                    const histories = await TestCaseHistoryRepository.findAll({ test_case_id: testCase._id }, { sort: { _id: -1 } });
                    return { ...testCase, histories };
                })
            );

            res.json(testCasesWithHistories);
        } catch (error) {
            logger.error('Lỗi khi lấy danh sách test cases', error, req.originalUrl);
            res.status(500).json({ message: 'Lỗi hệ thống' });
        }
    },

    async updateTestCase(req, res) {
        try {
            const { id } = req.params;
            let { detail, edit_reason, affected_field, comment } = req.body;
            edit_reason = edit_reason.trim();
            affected_field = affected_field.trim();

            if (!id) {
                return res.status(400).json({ message: 'ID test case không được để trống.' });
            }

            if (!edit_reason) {
                return res.status(400).json({ message: 'Lý do sửa không được để trống.' });
            }

            if (!detail || Object.keys(detail).length === 0) {
                return res.status(400).json({ message: 'Dữ liệu detail không được để trống.' });
            }

            const existingTestCase = await TestCaseRepository.findByIdWithSuite(id);
            if (!existingTestCase) {
                return res.status(404).json({ message: 'Test case không tồn tại.' });
            }

            const updatedTestCase = await TestCaseRepository.update(id, { detail,comment });            
            
            const editHistory = new TestCaseHistory({
                test_case_id: id,
                suite_id: existingTestCase.suite_id,
                edit_reason,
                affected_field,
                comment,
                type: config.test_case_history_type.update
            });
            await editHistory.save();

            if (!updatedTestCase) {
                return res.status(404).json({ message: 'Không thể cập nhật test case.' });
            }

            await MindmapBuilder.rebuildSnapshot(existingTestCase.suite_id);

            res.status(200).json({
                data: updatedTestCase
            });

        } catch (error) {
            logger.error('Lỗi khi cập nhật test case', error, req.originalUrl);
            res.status(500).json({ 
                message: error.message || 'Lỗi khi cập nhật test case'
            });
        }
    },

    async deleteTestCase(req, res) {
        try {
            const { id } = req.params;

            if (!id) {
                return res.status(400).json({ message: 'ID test case không được để trống.' });
            }

            const existingTestCase = await TestCaseRepository.findById(id);
            if (!existingTestCase) {
                return res.status(404).json({ message: 'Test case không tồn tại.' });
            }

            const deletedTestCase = await TestCaseRepository.delete(id);

            if (!deletedTestCase) {
                return res.status(404).json({ message: 'Không thể xóa test case.' });
            }

            const deleteHistory = new TestCaseHistory({
                test_case_id: id,
                suite_id: existingTestCase.suite_id,
                edit_reason: 'Xóa test case',
                type: config.test_case_history_type.delete
            });
            await deleteHistory.save();

            await MindmapBuilder.rebuildSnapshot(existingTestCase.suite_id);

            res.status(200).json({
                data: deletedTestCase
            });

        } catch (error) {
            logger.error('Lỗi khi xóa test case', error, req.originalUrl);
            res.status(500).json({ 
                message: error.message || 'Lỗi khi xóa test case'
            });
        }
    },

    async regenerateOneTestCase(req, res) {
        try {
            let { test_case_id, edit_reason, edit_detail } = req.body;
            test_case_id = test_case_id.trim();
            edit_reason = edit_reason.trim();
            edit_detail = edit_detail.trim();

            if (!test_case_id) {
                return res.status(400).json({ message: 'ID test case không được để trống.' });
            }

            if (!edit_reason || !edit_detail) {
                return res.status(400).json({ message: 'Lý do sửa và chi tiết thay đổi không được để trống.' });
            }

            logger.info('==== START REGENERATE ONE TEST CASE ====');

            const existingTestCase = await TestCaseRepository.findById(test_case_id);
            if (!existingTestCase) {
                return res.status(404).json({ message: 'Test case không tồn tại.' });
            }

            const feature = await FeatureRepository.findById(existingTestCase.feature_id);
            if (!feature) {
                return res.status(404).json({ message: 'Feature không tồn tại.' });
            }
            const snapshot = await MindmapSnapshotRepository.findOne({ suite_id: existingTestCase.suite_id });
            if (!snapshot) {
                return res.status(404).json({ message: 'Snapshot không tồn tại.' });
            }

            const caseType = Object.keys(existingTestCase.detail)[0];
            const testCaseToRegenerate = existingTestCase.detail[caseType];

            const payload = {
                features_with_test_cases: snapshot.tree.features,
                test_case_id: testCaseToRegenerate?.test_case_id,
                edit_reason,
                edit_detail,
                model: config.api_ai.model_name
            };

            const pythonResponse = await pythonApiService.post('/regenerate-single-test-case', payload);

            if (!pythonResponse.success) {
                const statusCode = pythonResponse.status || 502;
                const message = pythonResponse.data?.message || pythonResponse.error || 'Không thể gọi server Python.';
                return res.status(statusCode).json({ message });
            }

            const pythonData = pythonResponse.data;

            if (!pythonData?.success) {
                return res.status(400).json({ message: pythonData?.message || 'Server Python trả về lỗi.' });
            }

            const regeneratedTestCaseData = Array.isArray(pythonData.regenerated_test_case)
                ? pythonData.regenerated_test_case[0]
                : pythonData.regenerated_test_case;


            if (!regeneratedTestCaseData) {
                return res.status(400).json({ message: 'Server Python không trả về test case hợp lệ. Vui lòng thử lại.' });
            }

            const newDetail = {};
            newDetail[caseType] = {
                test_case_id: regeneratedTestCaseData.test_case_id,
                test_case_name: regeneratedTestCaseData.test_case_name,
                priority: regeneratedTestCaseData.priority,
                pre_condition: Array.isArray(regeneratedTestCaseData.preconditions) ? regeneratedTestCaseData.preconditions.join('\n') : (regeneratedTestCaseData.preconditions || ''),
                steps: Array.isArray(regeneratedTestCaseData.steps) ? regeneratedTestCaseData.steps.map(step => {
                    if (typeof step === 'object' && step !== null) {
                      const key = Object.keys(step)[0];
                      const value = step[key];
                      return `${key}: ${value}`;
                    }
                    return step;
                  }).join('\n') : (regeneratedTestCaseData.steps || ''),
                test_data: typeof regeneratedTestCaseData.test_data === 'object' ? Object.entries(regeneratedTestCaseData.test_data).map(([key, value]) => `${key}: ${value}`).join('\n') : regeneratedTestCaseData.test_data,
                expected_result: Array.isArray(regeneratedTestCaseData.expected_result) ? regeneratedTestCaseData.expected_result.join('\n') : (regeneratedTestCaseData.expected_result || ''),
                test_case_type: regeneratedTestCaseData.test_case_type
            };

            if (caseType === 'mobile_manual' && regeneratedTestCaseData.device) {
                newDetail.mobile_manual.device = regeneratedTestCaseData.device;
            }

            if (caseType === 'api_manual') {
                if (regeneratedTestCaseData.http_method) newDetail.api_manual.http_method = regeneratedTestCaseData.http_method;
                if (regeneratedTestCaseData.header) newDetail.api_manual.header = regeneratedTestCaseData.header;
                if (regeneratedTestCaseData.request_body) newDetail.api_manual.request_body = regeneratedTestCaseData.request_body;
            }

            // Update test case
            const updatedTestCase = await TestCaseRepository.update(test_case_id, { detail: newDetail });

            // save test case regenerate
            const regenerateData = {
                test_case_id: test_case_id,
                suite_id: existingTestCase.suite_id,
                edit_reason,
                edit_detail,
                refinement_instruction: null,
                type: config.regenerate_type.one,
                regenerated_at: new Date()
            };

            await TestCaseRegenerateRepository.create(regenerateData);

            await MindmapBuilder.rebuildSnapshot(existingTestCase.suite_id);

            res.status(200).json(updatedTestCase);

        } catch (error) {
            logger.error('Lỗi khi regenerate test case', error, req.originalUrl);
            res.status(500).json({
                message: error.message || 'Lỗi khi regenerate test case'
            });
        }
    },

    async buildRegeneratePrompt(testCase, regenerateParams) {
        try {
            const { edit_reason, edit_detail } = regenerateParams;

            const caseType = Object.keys(testCase.detail)[0];
            const testCaseData = testCase.detail[caseType];

            // Render template
            const prompt = await ejs.renderFile(
                path.join(__dirname, '../templates/re_gen_prompt/re_gen_one.ejs'),
                {
                    testCaseData,
                    edit_reason,
                    edit_detail
                }
            );

            return prompt;

        } catch (error) {
            logger.error('Lỗi khi build regenerate prompt', error);
            throw error;
        }
    },

    async regenerateAllTestCases(req, res) {
        try {
            let { suite_id, refinement_instruction } = req.body;
            suite_id = suite_id.trim();
            refinement_instruction = refinement_instruction.trim();

            if (!suite_id) {
                return res.status(400).json({ message: 'Suite ID không được để trống.' });
            }

            if (!refinement_instruction) {
                return res.status(400).json({ message: 'Chỉ dẫn tinh chỉnh không được để trống.' });
            }

            logger.info('==== START REGENERATE ALL TEST CASES ====');
            logger.info(`Suite ID: ${suite_id}`);

            const suite = await SuiteRepository.findById(suite_id);
            if (!suite) {
                return res.status(404).json({ message: 'Suite không tồn tại.' });
            }

            const features = await FeatureRepository.findAll({ suite_id });
            if (!features || features.length === 0) {
                return res.status(404).json({ message: 'Feature không tồn tại.' });
            }

            let caseType = 'web_manual';

            for (const feature of features) {
                const existingTestCases = await TestCaseRepository.findAll({ suite_id, feature_id: feature._id });
                if (existingTestCases && existingTestCases.length > 0) {
                    caseType = Object.keys((existingTestCases[0] && existingTestCases[0].detail) || {})[0] || 'web_manual';
                    break;
                }
            }

            const snapshot = await MindmapSnapshotRepository.findOne({ suite_id: suite_id });
            if (!snapshot) {
                return res.status(404).json({ message: 'Snapshot không tồn tại.' });
            }

            const payload = {
                features_with_test_cases: snapshot.tree.features,
                refinement_instruction,
                model: config.api_ai.model_name
            };

            const pythonResponse = await pythonApiService.post('/regenerate-all-test-cases', payload);

            if (!pythonResponse.success) {
                logger.error('Python API error:', pythonResponse);
                return res.status(500).json({ error: 'Failed to regenerate all test cases from Python API', details: pythonResponse.error });
            }

            const { features_with_test_cases } = pythonResponse.data;

            const processed = Array.isArray(features_with_test_cases) ? features_with_test_cases : [];

            if (processed.length === 0) {
                return res.status(400).json({ error: 'Không có dữ liệu test case được trả về từ Python server' });
            }

            await FeatureRepository.deleteMany({ suite_id: suite_id });
            await CheckItemRepository.deleteMany({ suite_id: suite_id });
            await TestCaseRepository.deleteMany({ suite_id: suite_id, type: 'manual' });
            let featureOrder = 0;

            for (const featureData of processed) {
                // Create Feature
                const savedFeature = await FeatureRepository.create({
                  suite_id: suite_id,
                  name: featureData.name || '',
                  description: featureData.description || '',
                  source_files: [],
                  merged_from: [],
                  permissions: {},
                  ui_components: {},
                  order: featureOrder++
                });
          
                const categories = Array.isArray(featureData.categories) ? featureData.categories : [];
          
                let categoryOrder = 0;
                for (const categoryData of categories) {
                  const savedCategory = await CheckItemRepository.create({
                    suite_id: suite_id,
                    feature_id: savedFeature._id,
                    parent_id: null,
                    type: 'category',
                    name: categoryData.name || '',
                    rationale: '',
                    order: categoryOrder++
                  });
          
                  const checklists = Array.isArray(categoryData.checklists) ? categoryData.checklists : [];
          
                  let checklistOrder = 0;
                  for (const checklistData of checklists) {
                    const savedChecklist = await CheckItemRepository.create({
                      suite_id: suite_id,
                      feature_id: savedFeature._id,
                      parent_id: savedCategory._id,
                      type: 'checklist',
                      name: checklistData.name || '',
                      rationale: checklistData.rationale || '',
                      order: checklistOrder++
                    });
          
                    // Create Test Cases
                    const testCases = Array.isArray(checklistData.test_cases) ? checklistData.test_cases : [];
                    let testCaseOrder = 0;
                    for (const tc of testCases) {
                      const detail = {};
                      const baseDetail = {
                        test_case_id: tc.test_case_id || '',
                        test_case_name: tc.test_case_name || '',
                        priority: tc.priority || '',
                        pre_condition: Array.isArray(tc.preconditions) ? tc.preconditions.join('\n') : (tc.preconditions || ''),
                        steps: typeof tc.steps === 'string' ? tc.steps : (Array.isArray(tc.steps) ? tc.steps.join('\n') : ''),
                        test_data: typeof tc.test_data === 'string' ? tc.test_data : (typeof tc.test_data === 'object' ? Object.entries(tc.test_data).map(([k, v]) => `${k}: ${v}`).join('\n') : ''),
                        expected_result: typeof tc.expected_result === 'string' ? tc.expected_result : (Array.isArray(tc.expected_result) ? tc.expected_result.join('\n') : ''),
                        test_case_type: tc.test_case_type || ''
                      };
          
                      if (caseType === 'api_manual') {
                        baseDetail.http_method = tc.http_method || '';
                        baseDetail.header = typeof tc.header === 'object' ? Object.entries(tc.header).map(([k, v]) => `${k}: ${v}`).join('\n') : (tc.header || '');
                        baseDetail.request_body = typeof tc.request_body === 'object' ? JSON.stringify(tc.request_body) : (tc.request_body || '');
                      }
          
                      detail[caseType] = baseDetail;
          
                      await TestCase.create({
                        suite_id: suite_id,
                        feature_id: savedFeature._id,
                        category_id: savedCategory._id,
                        check_item_id: savedChecklist._id,
                        detail,
                        order: testCaseOrder++,
                        type: 'manual'
                      });
                    }
                  }
                }
              }

            // save regenerate history for all test cases
            const regenerateData = {
                suite_id: suite_id,
                refinement_instruction: refinement_instruction,
                regenerated_at: new Date(),
                type: config.regenerate_type.all
            };

            await TestCaseRegenerateRepository.create(regenerateData);


            const tree = await MindmapBuilder.rebuildSnapshot(suite_id);

            res.status(200).json(tree);

            logger.info('==== END REGENERATE ALL TEST CASES ====');

        } catch (error) {
            logger.error('Lỗi khi regenerate all test cases', error, req.originalUrl);
            res.status(500).json({
                message: error.message || 'Lỗi khi regenerate all test cases'
            });
        }
    },

    async buildRegenerateAllPrompt(suite, testCases, refinement_instruction) {
        try {
            const formattedTestCases = testCases.map(testCase => {
                const caseType = Object.keys(testCase.detail)[0];
                const data = testCase.detail[caseType];
                return data;
            });

            // Render template
            const prompt = await ejs.renderFile(
                path.join(__dirname, '../templates/re_gen_prompt/re_gen_all.ejs'),
                {
                    featureName: suite.feature_name,
                    refinement_instruction: refinement_instruction,
                    testCases: formattedTestCases
                }
            );

            return prompt;

        } catch (error) {
            logger.error('Lỗi khi build regenerate all prompt', error);
            throw error;
        }
    },

    async createTestCase(req, res) {
        try {
            const testCase = req.body;
            let feature_id = testCase?.feature_id || null;
            let category_id = testCase?.category_id || null;
            let checklist_id = testCase?.checklist_id || null;
            
            const suite = await SuiteRepository.findById(testCase.suite_id);
            if (!suite) {
                return res.status(404).json({ message: 'Suite không tồn tại.' });
            }

            if (!feature_id && (testCase.checklist_id)) {
                return res.status(400).json({ 
                    message: 'Khi tạo feature mới, category và checklist cũng phải được tạo mới.' 
                });
            }

            if (!category_id && testCase.checklist_id && feature_id) {
                return res.status(400).json({ 
                    message: 'Khi tạo category mới, checklist cũng phải được tạo mới.' 
                });
            }

            if (!feature_id) {
                const lastFeature = await FeatureRepository.findOne({ suite_id: testCase.suite_id }, { sort: { order: -1 } });
                let order = lastFeature ? lastFeature.order + 1 : 0;
                const feature = await FeatureRepository.create({
                    suite_id: testCase.suite_id,
                    name: testCase.feature_name,
                    order: order
                });
                feature_id = feature._id;
                category_id = null;
                checklist_id = null;
            }

            if (!category_id) {
                const lastCategory = await CheckItemRepository.findOne({ suite_id: testCase.suite_id, feature_id: feature_id, type: 'category' }, { sort: { order: -1 } });
                let order = lastCategory ? lastCategory.order + 1 : 0;
                const category = await CheckItemRepository.create({
                    suite_id: testCase.suite_id,
                    feature_id: feature_id,
                    name: testCase.category_name,
                    type: 'category',
                    order: order
                });
                category_id = category._id;
                checklist_id = null;
            }

            if (!checklist_id) {
                const lastChecklist = await CheckItemRepository.findOne({ suite_id: testCase.suite_id, feature_id: feature_id, category_id: category_id, type: 'checklist' }, { sort: { order: -1 } });
                let order = lastChecklist ? lastChecklist.order + 1 : 0;
                const checklist = await CheckItemRepository.create({
                    suite_id: testCase.suite_id,
                    parent_id: category_id,
                    feature_id: feature_id,
                    name: testCase.checklist_name,
                    type: 'checklist',
                    order: order
                });
                checklist_id = checklist._id;
            }

            let caseType = 'web_manual';
            if (suite.field_type) {
            const fieldType = suite.field_type.toLowerCase();
                if (config.suite.field_type.includes(fieldType)) {
                    caseType = `${fieldType}_manual`;
                }
            }
            const detail = {};
            detail[caseType] = {
                test_case_id: testCase.test_case_id,
                test_case_name: testCase?.test_case_name || '',
                priority: testCase?.priority || '',
                pre_condition: testCase?.pre_condition || '',
                steps: testCase?.steps || '',
                test_data: testCase?.test_data || '',
                expected_result: testCase?.expected_result || '',
                test_case_type: testCase?.test_case_type || '',
            };
            if (caseType === 'mobile_manual') {
                detail[caseType].device = testCase?.device || '';
            }
            if (caseType === 'api_manual') {
                detail[caseType].http_method = testCase?.http_method || '';
                detail[caseType].header = testCase?.header || '';
                detail[caseType].request_body = testCase?.request_body || '';
            }
            const newTestCase = await TestCase.create({
                suite_id: testCase.suite_id,
                feature_id: feature_id,
                category_id: category_id,
                check_item_id: checklist_id,
                status: 'ready',
                detail
            });

            const editHistory = new TestCaseHistory({
                test_case_id: newTestCase._id,
                suite_id: testCase.suite_id,
                edit_reason: 'Tạo test case mới',
                type: config.test_case_history_type.create
            });
            await editHistory.save();

            const tree = await MindmapBuilder.rebuildSnapshot(testCase.suite_id);

            return res.status(200).json(tree);
        } catch (error) {
            logger.error('Lỗi khi tạo test case', error);
            return res.status(500).json({ message: error.message || 'Lỗi khi tạo test case' });
        }
    },

    async reviewTestCase(req, res) {
        try {
            const { review_status, duplicate_of, invalid_reason } = req.body;
            const test_case_id = req.params.id;
            if (!mongoose.Types.ObjectId.isValid(test_case_id)) {
                return res.status(400).json({ message: 'Test case ID không hợp lệ.' });
            }
            if (!config.test_case.review_status[review_status]) {
                return res.status(400).json({ message: 'Review status không hợp lệ.' });
            }

            let duplicateOfValue = null;
            let invalidReasonValue = null;
            
            if (review_status === 'duplicate') {
                if (!duplicate_of) {
                    return res.status(400).json({ message: 'Trường test case id không được để trống.' });
                }
                duplicateOfValue = duplicate_of;
                invalidReasonValue = null;
            } else if (review_status === 'invalid') {
                if (!invalid_reason || !invalid_reason.trim()) {
                    return res.status(400).json({ message: 'Lý do tại sao test case không hợp lệ không được để trống.' });
                }
                invalidReasonValue = invalid_reason.trim();
                duplicateOfValue = null;
            } else {
                duplicateOfValue = null;
                invalidReasonValue = null;
            }
            
            const testCase = await TestCaseRepository.findById(test_case_id);
            if (!testCase) {
                return res.status(404).json({ message: 'Test case không tồn tại.' });
            }
            const updatedTestCase = await TestCaseRepository.update(test_case_id, { 
                review_status: review_status, 
                duplicate_of: duplicateOfValue,
                invalid_reason: invalidReasonValue
            });

            await MindmapBuilder.rebuildSnapshot(testCase.suite_id);
            return res.status(200).json({
                data: updatedTestCase,
                message: 'Test case đã được review thành công.'
            });
        } catch (error) {
            logger.error('Lỗi khi review test case', error, req.originalUrl);
            return res.status(500).json({ message: error.message || 'Lỗi khi review test case' });
        }
    },

};

module.exports = testCaseController; 