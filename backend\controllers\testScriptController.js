const TestScript = require('../models/testScript');
const TemplateRepository = require('../repositories/templateRepository');
const fileService = require('../services/core/fileService');
const ejs = require('ejs');
const path = require('path');
const openaiService = require('../services/core/openaiService');
const config = require('../config');
const logger = require('../services/core/loggerService');

const testScriptController = {

  async extractHeadings(req, res) {
    try {
      if (!req.files || req.files.length === 0) {
        return res.status(400).json({ 
          errors: {
            files: 'Vui lòng chọn ít nhất một file DOCX.'
          }
        });
      }

      const options = {
        includeContent: req.body.includeContent,
        maxLevel: req.body.maxLevel
      };

      const result = await fileService.extractHeadingsFromFiles(req.files, options);

      res.status(200).json({
        ...result,
        suite_id: req.suiteId.toString()
      });

    } catch (error) {
      logger.error('Lỗi khi extract file:', error, req.originalUrl);
      
      if (req.files && req.suiteId) {
        const suiteUploadPath = path.join(__dirname, '../uploads', req.suiteId.toString());
        fileService.cleanupFiles(suiteUploadPath, 0);
      }

      // validate file
      if (error.message === 'NO_FILES') {
        return res.status(400).json({ 
          errors: {
            files: 'Vui lòng chọn ít nhất một file DOCX.'
          }
        });
      }

      if (error.message === 'INVALID_FILE_TYPE') {
        return res.status(400).json({ 
          errors: {
            files: 'Một số file không đúng định dạng DOCX.'
          }
        });
      }

      res.status(500).json({ 
        errors: {
          server: error.message || 'Có lỗi xảy ra khi xử lý file'
        }
      });
    }
  },

  async genPrompt(req, res) {
    try {
      const params = req.body;
      const { field_type, objective, template_id } = params;

      const templateConfigDefault = {
        'web_application': {
          'test_case_manual': {
            type: 'web-manual',
            path: '../templates/prompt/manual/web.ejs'
          },
          'script_automation': {
            type: 'web-auto', 
            path: '../templates/prompt/auto/web.ejs'
          }
        },
        'mobile_application': {
          'test_case_manual': {
            type: 'mobile-manual',
            path: '../templates/prompt/manual/mobile.ejs'
          }
        },
        'api_web_service': {
          'test_case_manual': {
            type: 'api-manual',
            path: '../templates/prompt/manual/api.ejs'
          }
        }
      };

      const templateConfig = templateConfigDefault[field_type]?.[objective];
      
      if (!templateConfig) {
        return res.status(400).json({ message: 'Không hỗ trợ loại template này.' });
      }

      const { type, path: templatePath } = templateConfig;

      // Get template
      const template = await TemplateRepository.findOne(
        template_id ? 
        { _id: template_id } : 
        { type, is_default: 1 }
      );

      if (!template) {
        return res.status(404).json({ message: 'Không tìm thấy template mặc định.' });
      }

      const prompt = await ejs.renderFile(
        path.join(__dirname, templatePath),
        {
          testScript: params,
          template,
          language: config.prompt_language[template.prompt_language] || 'Tiếng Anh'
        }
      );

      return res.status(200).json({ prompt });

    } catch (error) {
      logger.error('Lỗi khi tạo prompt', error, req.originalUrl);
      res.status(400).json({ message: error.message });
    }
  },

  async callOpenAi(req, res) {
    try {
      const { prompt } = req.body;

      if (!prompt) {
        return res.status(400).json({ message: 'Prompt không được để trống.' });
      }

      const chatGptResponse = await openaiService.callChatGpt(prompt);

      return res.status(200).json({
        result: chatGptResponse
      });
    } catch (error) {
      logger.error('Lỗi khi call OpenAI', error, req.originalUrl);
      res.status(500).json({ message: error.message });
    }
  }
};

module.exports = testScriptController; 