require('dotenv').config();
const userProjectRepository = require('../repositories/userProjectRepository');
const projectJoinRequestRepository = require('../repositories/projectJoinRequestRepository');
const config = require('../config/config');
const common = require('../utils/common');

async function checkUserInProject(project_id, user_id) {
  const userProject = await userProjectRepository.findOne({ project_id, user_id });
  return userProject ? true : false;
}

async function checkUserRequestJoinProject(project_id, user_id) {
  const projectJoinRequest = await projectJoinRequestRepository.findOne({ project_id, user_id, status: 'pending' });
  return projectJoinRequest ? true : false;
}

async function getHighestProjectRoleByUser(user_id) {
  for (const role of config.user.project_role_priority) {
    const userProject = await userProjectRepository.findOne({ 
      user_id, 
      project_role: role, 
    });

    if (userProject) {
      return role;
    }
  }

  return null;
}

async function checkRoleByProject(req, project_id) {
  const userLogin = await common.getUserLogin(req);
  if (!userLogin) return false;
  const userProject = await userProjectRepository.findOne({ project_id, user_id: userLogin._id });

  return { role: userLogin.role, project_role: userProject ? userProject.project_role : null };
};

async function getProjectRole(req, project_id) {
  const userLogin = await common.getUserLogin(req);
  if (!userLogin) return null;
  const userProject = await userProjectRepository.findOne({ project_id, user_id: userLogin._id });
  return userProject ? userProject.project_role : null;
}

module.exports = { 
  checkUserInProject,
  getHighestProjectRoleByUser,
  checkRoleByProject,
  checkUserRequestJoinProject,
  getProjectRole
};