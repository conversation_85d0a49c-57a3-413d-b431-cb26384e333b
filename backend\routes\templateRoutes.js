const express = require('express');
const router = express.Router();
const templateController = require('../controllers/templateController');
const validate = require('../middleware/validate');
const authMiddleware = require('../middleware/auth');
const { createTemplateSchema, updateTemplateSchema } = require('../validation/templateSchemas');

router.get('/', authMiddleware, templateController.getTemplates);
router.post('/', authMiddleware, validate(createTemplateSchema), templateController.createTemplate);
router.put('/:id', authMiddleware, validate(updateTemplateSchema), templateController.updateTemplate);
router.delete('/:id', authMiddleware, templateController.deleteTemplate);
router.get('/get-list-template', authMiddleware, templateController.getTemplateByType);
router.get('/:id', authMiddleware, templateController.getTemplateDetail);

module.exports = router;
