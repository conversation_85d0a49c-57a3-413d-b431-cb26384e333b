const axios = require('axios');
const logger = require('./loggerService');

class PythonApiService {
  constructor() {
    this.baseURL = process.env.PYTHON_API_URL || 'http://localhost:8000';
    this.timeout = (parseInt(process.env.PYTHON_API_TIMEOUT) || 5) * 60 * 1000; // default 5 minutes
    this.maxRetries = parseInt(process.env.PYTHON_API_MAX_RETRIES) || 3;
    this.retryDelay = parseInt(process.env.PYTHON_API_RETRY_DELAY) || 1000; // 1 second

    // Create axios instance with default config
    this.axiosInstance = axios.create({
      baseURL: this.baseURL,
      timeout: this.timeout,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    });

    // Setup interceptors
    this.setupInterceptors();
  }

  setupInterceptors() {
    // Request interceptor
    this.axiosInstance.interceptors.request.use(
      (config) => {
        logger.info(`[Python API] ${config.method.toUpperCase()} ${config.url}`, {
          params: config.params,
          dataSize: config.data ? JSON.stringify(config.data).length : 0,
        });
        return config;
      },
      (error) => {
        logger.error('[Python API] Request error:', error.message);
        return Promise.reject(error);
      }
    );

    // Response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => {
        logger.info(`[Python API] Response: ${response.status} from ${response.config.url}`, {
          dataSize: response.data ? JSON.stringify(response.data).length : 0,
        });
        return response;
      },
      (error) => {
        if (error.response) {
          logger.error(`[Python API] Response error: ${error.response.status}`, {
            url: error.config?.url,
            data: error.response.data,
          });
        } else if (error.request) {
          logger.error('[Python API] No response received:', {
            url: error.config?.url,
            message: error.message,
          });
        } else {
          logger.error('[Python API] Request setup error:', error.message);
        }
        return Promise.reject(error);
      }
    );
  }

  async retryRequest(requestFn, retries = this.maxRetries) {
    try {
      return await requestFn();
    } catch (error) {
      if (retries > 0 && this.isRetryableError(error)) {
        const delay = this.retryDelay * (this.maxRetries - retries + 1);
        logger.info(`[Python API] Retrying request after ${delay}ms. Retries left: ${retries - 1}`);
        
        await this.sleep(delay);
        return this.retryRequest(requestFn, retries - 1);
      }
      throw error;
    }
  }

  /**
   * Check if error can be retried
   */
  isRetryableError(error) {
    if (!error.response) return true; // Network error
    if (error.code === 'ECONNABORTED') return true; // Timeout
    if (error.response.status >= 500) return true; // Server error
    return false;
  }

  /**
   * Helper sleep function
   */
  sleep(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * GET request
   */
  async get(endpoint, params = {}, options = {}) {
    const requestFn = () => this.axiosInstance.get(endpoint, {
      params,
      ...options,
    });

    try {
      const response = await this.retryRequest(requestFn);
      return {
        success: true,
        data: response.data,
        status: response.status,
      };
    } catch (error) {
      return this.handleError(error, 'GET', endpoint);
    }
  }

  /**
   * POST request
   */
  async post(endpoint, data = {}, options = {}) {
    const requestFn = () => this.axiosInstance.post(endpoint, data, options);

    try {
      const response = await this.retryRequest(requestFn);
      return {
        success: true,
        data: response.data,
        status: response.status,
      };
    } catch (error) {
      return this.handleError(error, 'POST', endpoint);
    }
  }

  /**
   * PUT request
   */
  async put(endpoint, data = {}, options = {}) {
    const requestFn = () => this.axiosInstance.put(endpoint, data, options);

    try {
      const response = await this.retryRequest(requestFn);
      return {
        success: true,
        data: response.data,
        status: response.status,
      };
    } catch (error) {
      return this.handleError(error, 'PUT', endpoint);
    }
  }

  /**
   * DELETE request
   */
  async delete(endpoint, options = {}) {
    const requestFn = () => this.axiosInstance.delete(endpoint, options);

    try {
      const response = await this.retryRequest(requestFn);
      return {
        success: true,
        data: response.data,
        status: response.status,
      };
    } catch (error) {
      return this.handleError(error, 'DELETE', endpoint);
    }
  }

  async postWithFiles(endpoint, formData, options = {}) {
    const requestFn = () => this.axiosInstance.post(endpoint, formData, {
      ...options,
      headers: {
        ...options.headers,
        'Content-Type': 'multipart/form-data',
      },
    });

    try {
      const response = await this.retryRequest(requestFn);
      return {
        success: true,
        data: response.data,
        status: response.status,
      };
    } catch (error) {
      return this.handleError(error, 'POST', endpoint);
    }
  }

  handleError(error, method, endpoint) {
    const errorResponse = {
      success: false,
      error: error.message,
      method,
      endpoint,
    };

    if (error.response) {
      errorResponse.status = error.response.status;
      errorResponse.data = error.response.data;
      errorResponse.errorType = 'RESPONSE_ERROR';
    } else if (error.request) {
      errorResponse.errorType = 'NO_RESPONSE';
      errorResponse.message = 'Python server không phản hồi. Vui lòng kiểm tra kết nối.';
    } else {
      errorResponse.errorType = 'REQUEST_SETUP_ERROR';
    }

    logger.error(`[Python API] ${method} ${endpoint} failed:`, errorResponse);
    return errorResponse;
  }

  /**
   * Health check Python server
   */
  async healthCheck() {
    try {
      const response = await this.axiosInstance.get('/health', {
        timeout: 5000, // 5 seconds timeout cho health check
      });
      
      return {
        success: true,
        status: 'healthy',
        data: response.data,
      };
    } catch (error) {
      return {
        success: false,
        status: 'unhealthy',
        error: error.message,
      };
    }
  }
}

// Export singleton instance
const pythonApiService = new PythonApiService();

module.exports = pythonApiService;

