const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');
const { Schema } = mongoose;

const TemplateSchema = new Schema({
  name: { type: String, required: true, trim: true },
  description: { type: String, trim: true },
  type: { type: String, required: true, trim: true },
  prompt_language: { type: String, required: false, trim: true },
  coverage: {
    positive_case: { type: Number, default: 0 },
    negative_case: { type: Number, default: 0 },
    boundary_value_analysis: { type: Number, default: 0 },
    equivalence_partitioning: { type: Number, default: 0 },
    ui_ux_testing: { type: Number, default: 0 },
    compatibility_testing: { type: Number, default: 0 },
    accessibility_testing: { type: Number, default: 0 },
    edge_corner_case: { type: Number, default: 0 },
    gesture_testing: { type: Number, default: 0 },
    interrupt_testing: { type: Number, default: 0 },
    permission_testing: { type: Number, default: 0 },
    network_condition_testing: { type: Number, default: 0 },
    hardware_interaction_testing: { type: Number, default: 0 },
    installation_update_uninstall_testing: { type: Number, default: 0 },
    input_validation: { type: Number, default: 0 },
    authentication: { type: Number, default: 0 },
    authorization: { type: Number, default: 0 },
    business_logic_errors: { type: Number, default: 0 },
    status_code_errors: { type: Number, default: 0 },
    schema_validation: { type: Number, default: 0 },
    header_testing: { type: Number, default: 0 },


  },
  language: { type: String, required: false, trim: true },
  design_pattern: { type: String, required: false, trim: true },
  framework: { type: String, required: false, trim: true },
  is_default: { type: Number, default: 0 },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });


TemplateSchema.plugin(softDeletePlugin);

module.exports = mongoose.model('template', TemplateSchema);

