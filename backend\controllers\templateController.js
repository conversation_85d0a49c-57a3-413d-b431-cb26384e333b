const TemplateRepository = require('../repositories/templateRepository');
const logger = require('../services/core/loggerService');

// Create template
exports.createTemplate = async (req, res) => {
  try {
    if (req.body.is_default == 1) {
      await TemplateRepository.updateMany({ type: req.body.type }, { is_default: 0 });
    }
    const template = await TemplateRepository.create(req.body);
    res.status(201).json(template);
  } catch (err) {
    logger.error('Lỗi khi tạo template', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

exports.getTemplates = async (req, res) => {
  try {
    const templates = await TemplateRepository.findAll();
    res.json(templates);
  } catch (err) {
    logger.error('Lỗi khi lấy danh sách template', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

// Update template
exports.updateTemplate = async (req, res) => {
  const { id } = req.params;
  try {
    if (req.body.is_default == 1) {
      const templateToUpdate = await TemplateRepository.findById(id);
      const isUpdateDefault = templateToUpdate.is_default != req.body.is_default;
      if (templateToUpdate && isUpdateDefault) {
        const typeForCheck = req.body.type || templateToUpdate.type;
        await TemplateRepository.updateMany(
          { type: typeForCheck, _id: { $ne: id } },
          { is_default: 0 }
        );
      }
    }
    const template = await TemplateRepository.update(id, req.body);
    if (!template) return res.status(404).json({ error: 'Template Không tồn tại' });
    res.json(template);
  } catch (err) {
    logger.error('Lỗi khi cập nhật template', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

// Delete template
exports.deleteTemplate = async (req, res) => {
  const { id } = req.params;
  try {
    const template = await TemplateRepository.delete(id);
    if (!template) return res.status(404).json({ error: 'Template không tồn tại' });
    res.json({ message: 'Xóa thành công' });
  } catch (err) {
    logger.error('Lỗi khi xóa template', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

// Get template detail
exports.getTemplateDetail = async (req, res) => {
  const { id } = req.params;
  try {
    const template = await TemplateRepository.findById(id);
    if (!template) return res.status(404).json({ error: 'Template không tồn tại' });
    res.json(template);
  } catch (err) {
    logger.error('Lỗi khi lấy chi tiết template', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};

// Get template by type
exports.getTemplateByType = async (req, res) => {
  const { type } = req.query;
  let template;
  try {
    if (type) {
      template = await TemplateRepository.findAll({ type: type });
    } else {
      template = await TemplateRepository.findAll();
    }
    if (!template) return res.status(404).json({ error: 'Template không tồn tại' });
    res.json(template);
  } catch (err) {
    logger.error('Lỗi khi lấy template theo type', err, req.originalUrl);
    res.status(500).json({ error: err.message });
  }
};
