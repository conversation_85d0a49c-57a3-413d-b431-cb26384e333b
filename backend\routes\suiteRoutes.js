const express = require('express');
const router = express.Router();
const suiteController = require('../controllers/suiteController');
const auth = require('../middleware/auth');

router.post('/suite-approval', auth, suiteController.handleSuiteApproval);
router.get('/', auth, suiteController.getSuites);
router.get('/approvals', auth, suiteController.getListApproval);
router.get('/:id', auth, suiteController.getSuiteById);

module.exports = router; 