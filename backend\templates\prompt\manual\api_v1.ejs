<%
const coverageItems = [];
if (template.coverage.positive_case) { coverageItems.push("Positive Cases: <PERSON><PERSON><PERSON> request với dữ liệu hợp lệ, kiểm tra status code 2xx và response body/headers chính xác."); }
if (template.coverage.negative_case) { coverageItems.push("Negative Cases:"); }
if (template.coverage.input_validation) { coverageItems.push("Input Validation: Dữ liệu không hợp lệ (sai ki<PERSON><PERSON>, sai đ<PERSON>nh dạng, ngo<PERSON>i phạm vi, thiếu trư<PERSON> b<PERSON><PERSON> bu<PERSON>, thừa trường không mong muốn, gi<PERSON> trị null/rỗng không hợp lệ) cho tất cả các nguồn input (Query Params, Path Params, Headers, Body)."); }
if (template.coverage.authentication) { coverageItems.push("Authentication: <PERSON>hiế<PERSON> thông tin xác thực, sai thông tin, token hết hạn/không hợp lệ."); }
if (template.coverage.authorization) { coverageItems.push("Authorization: User không có quyền truy cập endpoint hoặc resource cụ thể."); }
if (template.coverage.business_logic) { coverageItems.push("Business Logic Errors: Các trường hợp vi phạm quy tắc nghiệp vụ."); }
if (template.coverage.status_code) { coverageItems.push("Kiểm tra Status Codes lỗi (4xx, 5xx): Đảm bảo trả về đúng mã lỗi và thông báo lỗi có ý nghĩa trong response body."); }
if (template.coverage.boundary_value_analysis) { coverageItems.push("Boundary Value Analysis (BVA): Kiểm tra các giá trị biên cho các trường số, giới hạn độ dài chuỗi, giới hạn số lượng phần tử trong mảng (áp dụng cho Query Params, Path Params, Body)."); }
if (template.coverage.schema_validation) { coverageItems.push("Schema Validation: Đảm bảo cấu trúc response body (JSON, XML) khớp với định nghĩa/schema mong đợi (cả trường hợp thành công và lỗi)."); }
if (template.coverage.header_testing) { coverageItems.push("Header Testing: Kiểm tra request với header thiếu/thừa/sai; Kiểm tra các response header quan trọng (ví dụ: Content-Type, Cache-Control, RateLimit-Remaining)."); }
if (template.coverage.edge_corner_case) { coverageItems.push("Edge Cases/Corner Cases: Các tình huống đặc biệt (ví dụ: payload rất lớn, ký tự đặc biệt/unicode trong input, gọi API liên tục để kiểm tra rate limiting cơ bản...). Yêu cầu AI suy luận."); }
%>
I. Thông tin Đầu vào cho AI

A. Ngữ cảnh API (API Context):
    • Tên Dự án/Hệ thống/Dịch vụ: <%- testScript.detail.api_manual.context.project_name %>
    • Mục đích chính của API: <%- testScript.detail.api_manual.context.api_purpose %>
    • Loại API: <%- testScript.detail.api_manual.context.api_type %>
    • Phiên bản API: <%- testScript.detail.api_manual.context.api_version %>

B. Đặc tả Endpoint (Endpoint Specifications):
    • Endpoint cần kiểm thử: <%- testScript.detail.api_manual.endpoint_spec.endpoint %>
    • URL cơ sở (Base URL) môi trường Test: <%- testScript.detail.api_manual.endpoint_spec.base_url %>
    • Đường dẫn Endpoint (Endpoint Path): <%- testScript.detail.api_manual.endpoint_spec.endpoint_path %>
    • Phương thức HTTP (HTTP Method): <%- testScript.detail.api_manual.endpoint_spec.http_method %>
    • Tài liệu API tham khảo: <%- testScript.detail.api_manual.endpoint_spec.api_docs %>
        <%- testScript.documents?.extract_file_content?.map((file, index) => (index + 1) + '. ' + file).join('\r\n        ') %>
    • Headers (Tiêu đề yêu cầu): <%- testScript.detail.api_manual.endpoint_spec.header %>
    • Tham số Truy vấn (Query Parameters): <%- testScript.detail.api_manual.endpoint_spec.query_param %>
    • Tham số Đường dẫn (Path Parameters): <%- testScript.detail.api_manual.endpoint_spec.path_param %>
    • Body Request (Nội dung yêu cầu): <pre><%- testScript.detail.api_manual.endpoint_spec.request_body %></pre>
    • Cấu trúc Response mong đợi (Expected Response Body Structure): <pre><%- testScript.detail.api_manual.endpoint_spec.expected_response %></pre>
    • Mã trạng thái HTTP mong đợi (Expected Status Codes): <%- testScript.detail.api_manual.endpoint_spec.expected_status %>

C. Yêu cầu Kỹ thuật & Nghiệp vụ (Technical & Business Requirements):
    • Cơ chế xác thực (Authentication): <%- testScript.detail.api_manual.technical_requirements.auth_mechanism %>
    • Cơ chế phân quyền (Authorization): <%- testScript.detail.api_manual.technical_requirements.permission_mechanism %>
    • Quy tắc nghiệp vụ liên quan: <%- testScript.detail.api_manual.technical_requirements.business_rule %>
    • Giới hạn truy cập (Rate Limiting): <%- testScript.detail.api_manual.technical_requirements.access_limit %>

D. Mục tiêu & Phạm vi Kiểm thử (Test Goal & Scope):
    • Mục tiêu kiểm thử chính: <%- testScript.detail.api_manual.test_scope.script_goal %>
    • Phạm vi Bao gồm (In-scope): <%= coverageItems.join(', ') %>
    • Phạm vi Không bao gồm (Out-of-scope): <%- testScript.detail.api_manual.test_scope.exclude_scope %>
    • Loại kiểm thử trọng tâm (Focus Area): <%- testScript.detail.api_manual.test_scope.focus_type %>
    • Dữ liệu kiểm thử đặc biệt (Test Data): <%- testScript.detail.api_manual.test_scope.special_data %>

II. Prompt Yêu cầu AI

Vai trò: Bạn là một Manual QA Manager với hơn 10 năm kinh nghiệm, chuyên sâu về kiểm thử API (API Testing), hiểu rõ các phương thức HTTP, cấu trúc dữ liệu (JSON/XML), mã trạng thái HTTP, và các kỹ thuật thiết kế test case liên quan đến API.

Nhiệm vụ: Dựa vào Thông tin Đầu vào cho AI (Mục I) được cung cấp, hãy tạo ra một bộ Test Cases (TCs) thủ công chi tiết bằng <%= language %> để kiểm thử API Endpoint <%= testScript.detail.api_manual.endpoint_spec.endpoint %> (<%= testScript.detail.api_manual.endpoint_spec.http_method %> <%= testScript.detail.api_manual.endpoint_spec.endpoint_path %>).

Yêu cầu chi tiết cho Test Cases:

1.  Định dạng Output: Mỗi TC cần có các trường sau:
    •   test_case_id: (Định dạng: TênEndpoint_API_XXX)
    •   test_case_name: (Mô tả ngắn gọn mục tiêu của TC)
    •   priority: (High/Medium/Low)
    •   pre_condition: (Ví dụ: Có token xác thực hợp lệ với quyền Y)
    •   http_method: (Bao gồm Base URL, Path, Query Params)
    •   header: (Chi tiết các header và giá trị cần gửi)
    •   request_body: (Chi tiết payload JSON/XML... cần gửi, nếu có)
    •   steps: (Chủ yếu là: Gửi request với thông tin trên)
    •   expected_result: (Bao gồm: Mã trạng thái HTTP mong đợi, Mô tả Response Body mong đợi - có thể là cấu trúc chính, giá trị cụ thể hoặc yêu cầu validate schema, Các Response Headers quan trọng cần kiểm tra nếu có)
    •   test_case_type: (Ví dụ: Positive, Negative-Input, Negative-Auth, Boundary, Schema, Edge)

2. Độ bao phủ: Bộ TCs phải bao phủ toàn diện các trường hợp, bao gồm:
    <% coverageItems.forEach(item => { %>
        • <%- item %>
    <% }); %>
3. Ngôn ngữ: Toàn bộ Test Cases phải bằng <%- language %>

4.  Mức độ chi tiết: Mô tả rõ ràng request cần gửi và các yếu tố cần kiểm tra trong response.
5.  Tập trung vào: <%- testScript.detail.api_manual.test_scope.focus_type %>.

Hãy bắt đầu tạo bộ Test Cases với số lượng Test Case tối ưu do AI tự suy luận để đảm bảo bao phủ hết toàn diện cho Tên Dự án/Hệ thống/Dịch vụ: <%- testScript.detail.api_manual.context.project_name %>.