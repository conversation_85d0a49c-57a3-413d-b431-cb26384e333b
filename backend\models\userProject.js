const mongoose = require('mongoose');

const softDeletePlugin = require('../plugins/softDeletePlugin');

const userProjectSchema = new mongoose.Schema({
  project_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'project',
    required: true
  },
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user',
    required: true
  },
  project_role: {
    type: String,
    enum: ['leader', 'tester', 'viewer'],
    required: true
  },
  status: {
    type: String,
    enum: ['active', 'left', 'removed'],
    default: 'active'
  },
  del_flag: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

userProjectSchema.index({ project_id: 1, user_id: 1 });
userProjectSchema.index({ user_id: 1, project_role: 1 });
userProjectSchema.index({ project_id: 1 });

userProjectSchema.plugin(softDeletePlugin);

module.exports = mongoose.model('user_project', userProjectSchema);

