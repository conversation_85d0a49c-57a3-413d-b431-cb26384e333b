const BaseRepository = require('./baseRepository');
const MindmapSnapshot = require('../models/mindmapSnapshot');

class MindmapSnapshotRepository extends BaseRepository {
  constructor() {
    super(MindmapSnapshot);
  }

  async upsertBySuiteId(suiteId, tree, title, description) {
    return this.model.findOneAndUpdate(
      { suite_id: suiteId },
      { tree, title, description, rebuilt_at: new Date() },
      { upsert: true, new: true }
    ).lean();
  }
}

module.exports = new MindmapSnapshotRepository();


