<%
    const coverageItems = [];
    if (template.coverage.positive_case) { coverageItems.push("Positive Cases (Happy Paths): <PERSON><PERSON><PERSON> luồng hoạt động chính, thành công theo thiết kế."); }
    if (template.coverage.negative_case) { coverageItems.push("Negative Cases: <PERSON><PERSON><PERSON> trường hợp nhập liệu không hợp lệ (sai định dạng, kiểu dữ liệu), sai quy tr<PERSON>nh, x<PERSON> lý lỗi, hủy bỏ thao tác."); }
    if (template.coverage.boundary_value_analysis) { coverageItems.push("Boundary Value Analysis (BVA): <PERSON><PERSON>m tra các giá trị tại cận trên, cận dướ<PERSON>, ngay trong và ngay ngoài các vùng dữ liệu hợp lệ (áp dụng cho các trường số, giới hạn độ dài...). AI cần suy luận các biên dựa trên mô tả."); }
    if (template.coverage.equivalence_partitioning) { coverageItems.push("Equivalence Partitioning (EP): <PERSON><PERSON><PERSON> bảo có đại diện cho các lớp dữ liệu đầu vào tương đương (hợp lệ và không hợp lệ)."); }
    if (template.coverage.ui_ux_testing) { coverageItems.push("UI/UX Testing (Cơ bản): Đảm bảo giao diện hiển thị đúng trên các trình duyệt/độ phân giải mục tiêu (đã nêu), các yếu tố tương tác (nút bấm, link, form) hoạt động tốt, layout không bị vỡ, thông báo rõ ràng."); }
    if (template.coverage.compatibility_testing) { coverageItems.push("Compatibility Testing (Cơ bản): Đảm bảo tính năng hoạt động ổn định trên các trình duyệt/phiên bản đã nêu."); }
    if (template.coverage.accessibility_testing) { coverageItems.push("Accessibility Testing (Cơ bản - nếu có yêu cầu): Gợi ý kiểm tra cơ bản như điều hướng bàn phím, alt-text."); }
    if (template.coverage.edge_corner_case) { coverageItems.push("Edge Cases/Corner Cases: Các tình huống hiếm gặp, giới hạn dữ liệu, điều kiện đặc biệt (ví dụ: mạng chậm - mô tả hành vi mong đợi, dữ liệu lớn, thao tác bất thường, refresh trang giữa chừng...). Yêu cầu AI suy luận và tạo ra ít nhất 10 edge cases khác nhau."); }
%>
I. Thông tin Đầu vào cho AI

A. Ngữ cảnh Ứng dụng & Dự án (Application & Project Context):
    • Tên Dự án/Ứng dụng: <%- testScript.detail.web_manual.context.project_name %>
    • Loại ứng dụng: <%- testScript.detail.web_manual.context.app_type %>
    • Mục tiêu chính của ứng dụng: <%- testScript.detail.web_manual.context.main_purpose %>
    • Đối tượng người dùng chính: <%- testScript.detail.web_manual.context.main_user %>

B. Đặc tả Tính năng (Feature Specifications):
    • Tính năng cần kiểm thử: <%- testScript.detail.web_manual.feature_spec.feature ? testScript.detail.web_manual.feature_spec.feature.split('\n').join('\r\n        ') : '' %>
    • Luồng nghiệp vụ chính (Main Business Flow): 
        <%- testScript.detail.web_manual.feature_spec.main_flow ? testScript.detail.web_manual.feature_spec.main_flow.split('\n').join('\r\n        ') : '' %>
    • Tài liệu tham khảo (Requirements, Specs, Mockups, Manual TCs): <%- testScript.documents.description %>
        <%- testScript.documents?.extract_file_content?.map((file, index) => (index + 1) + '. ' + file).join('\r\n        ') %>
    • Quy tắc nghiệp vụ (Business Rules):
        <%- testScript.detail.web_manual.feature_spec.business_rules ? testScript.detail.web_manual.feature_spec.business_rules.split('\n').join('\r\n        ') : '' %>
    • Dữ liệu đầu vào & Quy tắc xác thực (Inputs & Validation Rules): <%- testScript.detail.web_manual.feature_spec.input_rule ? testScript.detail.web_manual.feature_spec.input_rule.split('\n').join('\r\n        ') : '' %>
    • Kết quả/Hành vi mong đợi chính (Expected Outputs/Behavior): <%- testScript.detail.web_manual.feature_spec.expected_results ? testScript.detail.web_manual.feature_spec.expected_results.split('\n').join('\r\n        ') : '' %>

C. Yêu cầu Kỹ thuật & Môi trường (Technical & Environment):
    • URL/Môi trường kiểm thử: <%- testScript.detail.web_manual.technical_requirements.test_url %>
    • Trình duyệt và Phiên bản mục tiêu: <%- testScript.detail.web_manual.technical_requirements.browser %>
    • Độ phân giải màn hình/Viewports: <%- testScript.detail.web_manual.technical_requirements.viewport %>
    • Điểm tích hợp (Integration Points): <%- testScript.detail.web_manual.technical_requirements.integration_point %>

D. Mục tiêu & Phạm vi Kiểm thử (Test Goal & Scope):
    • Mục tiêu kiểm thử chính: <%- testScript.detail.web_manual.test_scope.script_goal %>
    • Phạm vi Không bao gồm (Out-of-scope): <%- testScript.detail.web_manual.test_scope.exclude_scope %>
    • Loại kiểm thử trọng tâm (Focus Area): <%- testScript.detail.web_manual.test_scope.focus_type %>
    • Dữ liệu kiểm thử đặc biệt (Test Data): <%- testScript.detail.web_manual.test_scope.special_data %> 

II. Prompt Yêu cầu AI và các hướng dẫn chi tiết

Vai trò: Bạn là một Manual QA Manager với hơn 10 năm kinh nghiệm, chuyên sâu về kiểm thử ứng dụng web, có kiến thức về các kỹ thuật thiết kế test case như BVA và EP. Bạn là một chuyên gia trong việc tạo ra các bộ test case cực kỳ chi tiết và có độ bao phủ tối đa.
Nhiệm vụ: Dựa vào Thông tin Đầu vào cho AI (Mục I) được cung cấp, hãy tạo ra một bộ Test Cases (TCs) thủ công chi tiết bằng <%= language %> để kiểm thử tính năng <%= testScript.detail.web_manual.feature_spec.feature %> trên ứng dụng web <%= testScript.detail.web_manual.context.project_name %>.
Yêu cầu cốt lõi: Hãy tư duy một cách toàn diện và sáng tạo để sinh ra số lượng test case lớn nhất có thể, đảm bảo không bỏ sót bất kỳ trường hợp nào, từ những luồng cơ bản nhất đến những kịch bản phức tạp và hiếm gặp nhất.

Yêu cầu chi tiết cho Test Cases:

1.  Định dạng Output: Mỗi TC cần có các trường sau:
    •   test_case_id: (Định dạng: TênTínhNăng_XXX, ví dụ: Login_001)
    •   test_case_name: (Mô tả ngắn gọn mục tiêu của TC)
    •   priority: (High/Medium/Low - AI tự đánh giá dựa trên tầm quan trọng)
    •   pre_condition: (Các bước chuẩn bị cần thiết)
    •   steps: (Mô tả rõ ràng, tuần tự từng hành động của người dùng trên trình duyệt)
    •   test_data: (Dữ liệu cụ thể cần nhập hoặc sử dụng)
    •   expected_result: (Mô tả cụ thể trạng thái hệ thống, dữ liệu hiển thị, thông báo lỗi, hoặc hành vi giao diện sau khi thực hiện các bước, kết quả mong đợi phải tương ứng cho chính bước đó. Phải cực kỳ chi tiết.) 
            Ví dụ định dạng: 
                Step 1: Mở trình duyệt và truy cập URL <%- testScript.detail.web_manual.technical_requirements.test_url %>. 
                Expected Result 1: Trang đăng nhập hiển thị đầy đủ các thành phần: ô "Tên đăng nhập", ô "Mật khẩu", nút "Đăng nhập". 
                Step 2: Nhập [Dữ liệu Test cho Tên đăng nhập] vào ô "Tên đăng nhập". 
                Expected Result 2: Dữ liệu được nhập thành công vào ô "Tên đăng nhập". 
                Step 3: ...
    •   test_case_type: (Ví dụ: Positive, Negative, Boundary, UI, Compatibility, Edge)

2. Độ bao phủ cho Web: Bộ TCs phải bao phủ toàn diện các trường hợp, bao gồm:
    <%- coverageItems.join('\r\n    ') %>
3. Ngôn ngữ: <%- language %>

4.  Mức độ chi tiết: Các bước thực hiện và kết quả mong đợi phải rõ ràng, đủ chi tiết để một tester khác có thể thực hiện lại mà không cần hỏi thêm.
5.  Tập trung vào: <%- testScript.detail.web_manual.test_scope.focus_type %>.

Hãy bắt đầu tạo bộ Test Cases với số lượng Test Case tối đa và đầy đủ nhất do AI tự suy luận để đảm bảo bao phủ hết toàn diện cho tính năng <%- testScript.detail.web_manual.feature_spec.feature %>.