const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');
const { Schema } = mongoose;

// Schemas for different test types
const webManualSchema = new Schema({
  context: {
    project_name: String,
    app_type: String, 
    main_purpose: String,
    main_user: String
  },
  feature_spec: {
    feature: String,
    main_flow: String,
    business_rules: String,
    input_rule: String,
    expected_results: String
  },
  technical_requirements: {
    test_url: String,
    browser: String,
    viewport: String,
    integration_point: String
  },
  test_scope: {
    script_goal: String,
    exclude_scope: String,
    focus_type: String,
    special_data: String
  }
});

const webAutomationSchema = new Schema({
  context: {
    project_name: String,
    app_type: String,
    main_user: String,
    main_purpose: String,
    automation_purpose: String
  },
  feature_spec: {
    feature: String,
    manual_steps: String,
    business_rule: String,
    input_rule: String,
    expected_result: String
  },
  automation_env: {
    test_url: String,
    browser: String,
    test_credential: String,
    build_tool: String,
    cicd_platform: String
  },
  code_requirements: {
    project_structure: String,
    coding_standard: String,
    support_lib: String,
    test_data_strategy: String,
    config_management: String,
    reporting: String
  },
  locator_info: {
    locator: String,
    wait_strategy: String
  },
  test_scope: {
    main_purpose: String,
    exclusion_scope: String,
    focus_type: String
  }
});

const mobileManualSchema = new Schema({
  context: {
    project_name: String,
    app_type: String,
    main_purpose: String,
    target_platform: String,
    main_user: String
  },
  feature_spec: {
    feature: String,
    main_flow: String,
    business_rule: String,
    input_rule: String,
    expected_result: String
  },
  technical_requirements: {
    test_url: String,
    os_version: String,
    target_device: String,
    network_condition: String,
    permission: String,
    interaction: String
  },
  test_scope: {
    script_goal: String,
    exclude_scope: String,
    focus_type: String,
    special_data: String
  }
});

const mobileAutomationSchema = new Schema({
  context: {
    project_name: String,
    app_type: String,
    target_platform: String,
    main_purpose: String,
    main_user: String,
    automation_purpose: String
  },
  feature_spec: {
    feature: String,
    manual_step: String,
    business_rule: String,
    input_rule: String,
    expected_result: String
  },
  automation_env: {
    build_url: String,
    device: String,
    appium_server: String,
    test_credential: String,
    device_management: String
  },
  code_requirement: {
    project_structure: String,
    coding_standard: String,
    support_lib: String,
    test_data_strategy: String
  },
  locator_info: {
    locators: String,
    wait_strategy: String
  },
  test_scope: {
    script_goal: String,
    exclude_scope: String,
    focus_type: String
  }
});

const apiManualSchema = new Schema({
  context: {
    project_name: String,
    api_type: String,
    api_version: String,
    api_purpose: String
  },
  endpoint_spec: {
    endpoint: String,
    base_url: String,
    endpoint_path: String,
    http_method: String,
    api_docs: String,
    header: String,
    query_param: String,
    path_param: String,
    request_body: String,
    expected_response: String,
    expected_status: String
  },
  technical_requirements: {
    auth_mechanism: String,
    permission_mechanism: String,
    business_rule: String,
    access_limit: String
  },
  test_scope: {
    script_goal: String,
    exclude_scope: String,
    focus_type: String,
    special_data: String
  }
});

const apiAutomationSchema = new Schema({
  context: {
    project_name: String,
    main_purpose: String,
    api_type: String,
    api_version: String,
    automation_purpose: String
  },
  endpoint_spec: {
    test_endpoint: String,
    endpoint_path: String,
    http_method: String,
    manual_step: String,
    api_docs: String,
    business_rule: String,
    input_validation: String,
    expected_result: String
  },
  automation_env: {
    test_base_url: String,
    test_credential: String,
    build_tool: String
  },
  code_requirements: {
    project_structure: String,
    coding_standard: String,
    support_lib: String,
    test_data_strategy: String,
    schema_validation: String,
    contract_testing: String
  },
  test_scope: {
    script_goal: String,
    exclude_scope: String,
    focus_type: String
  }
});

const performanceSchema = new Schema({
  context: {
    project_name: String,
    target_type: String,
    architecture: String
  },
  business_flow: {
    feature: String,
    main_transactions: String,
    performance_requirements: {
      expected_load: {
        concurrent_user: String,
        throughput: String,
        total_transactions: String
      },
      acceptance_criteria: {
        response_time: String,
        throughput: String,
        error_rate: String,
        resource_usage: String
      }
    },
    load_model: String,
    estimated_duration: String
  },
  environment: {
    test_env: String,
    planned_tools: String,
    monitoring_tools: String
  },
  test_scope: {
    script_goal: String,
    focus_type: String,
    inclusion_scope: String,
    exclusion_scope: String,
    risk_factor: String
  }
});

const securitySchema = new Schema({
  context: {
    project_name: String,
    target_type: String,
    business_domain: String,
    data_sensitivity: String,
    compliance_requirement: String
  },
  architecture: {
    main_technology: String,
    auth_method: String,
    permission_model: String,
    sensitive_data_storage: String,
    data_communication: String,
    third_party_integration: String
  },
  security_features: {
    auth_detail: String,
    session_management: String,
    input_control: String,
    access_control: String,
    csrf_protection: String,
    security_header: String,
    logging_monitoring: String
  },
  threat_model: {
    critical_assets: String,
    potential_threat: String,
    known_attack_vector: String
  },
  test_scope: {
    script_goal: String,
    focus_feature: String,
    vulnerability_type: String
  }
});

const TestScriptSchema = new Schema({
  field_type: {
    type: String,
      enum: ['web_application', 'mobile_application', 'api_web_service', 'performance', 'security'],
      required: true
  },
  objective: {
    type: String,
    enum: ['test_case_manual', 'script_automation'],
    required: true
  },
  detail: {
    web_manual: webManualSchema,
    web_automation: webAutomationSchema,
    mobile_manual: mobileManualSchema,
    mobile_automation: mobileAutomationSchema,
    api_manual: apiManualSchema,
    api_automation: apiAutomationSchema,
    performance: performanceSchema,
    security: securitySchema
  },
  documents: {
    description: String,
    files: [{
      file_path: String,
      file_name: String
    }]
  },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

TestScriptSchema.plugin(softDeletePlugin);

module.exports = mongoose.model('test_script', TestScriptSchema);

