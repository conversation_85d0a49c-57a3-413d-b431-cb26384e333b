function softDeletePlugin(schema) {
  function applyScope(next) {
    const query = this;

    const filter = query.getFilter ? query.getFilter() : query.getQuery(); // fallback cho findById
    const includeDeleted = filter.includeDeleted || query.options?.includeDeleted;

    if (!includeDeleted) {
      query.setQuery({ del_flag: 0, ...filter });
    } else {
      if (filter.includeDeleted) {
        delete filter.includeDeleted;
        query.setQuery(filter);
      }
    }

    next();
  }

  schema.pre('find', applyScope);
  schema.pre('findOne', applyScope);
  schema.pre('findAll', applyScope);
  schema.pre('count', applyScope);
  schema.pre('countDocuments', applyScope);
  schema.pre('findOneAndUpdate', applyScope);
  schema.pre('exists', applyScope);
  schema.pre('updateMany', applyScope);

  schema.pre('findById', function (next) {
    
    const filter = this.getQuery();
    const includeDeleted = this.options?.includeDeleted;

    if (!includeDeleted) {
      this.setQuery({ del_flag: 0, ...filter });
    }

    next();
  });

  schema.query.withTrashed = function () {
    this.setOptions({ includeDeleted: true });
    return this;
  };

  schema.query.onlyTrashed = function () {
    this.setQuery({ ...this.getQuery(), del_flag: 1 });
    return this;
  };

}

module.exports = softDeletePlugin;