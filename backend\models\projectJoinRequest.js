const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');

const projectJoinRequestSchema = new mongoose.Schema({
  project_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'project',
    required: true
  },
  user_id: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user',
    required: true
  },
  requested_role: {
    type: String,
    enum: ['tester', 'viewer'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected'],
    default: 'pending'
  },
  reviewed_by: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'user',
    default: null
  },
  reviewed_at: {
    type: Date,
    default: null
  },
  reviewed_note: {
    type: String,
    default: null
  },
  message: {
    type: String,
    default: null
  },
  del_flag: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

projectJoinRequestSchema.index({ project_id: 1, user_id: 1, status: 1 });
projectJoinRequestSchema.index({ project_id: 1 });
projectJoinRequestSchema.index({ user_id: 1 });

projectJoinRequestSchema.plugin(softDeletePlugin);

module.exports = mongoose.model('project_join_request', projectJoinRequestSchema);


