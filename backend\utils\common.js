/**
 * Common utility functions for backend
 */

const authService = require('../adapters/jwtAuthAdapter');

const formatDateTime = (timestamp) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    return date.toLocaleDateString('vi-VN') + ' ' + date.toLocaleTimeString('vi-VN', { 
        hour: '2-digit', 
        minute: '2-digit' 
    });
};

// get token from request
const getTokenFromRequest = (req) => {
    const authHeader = req.headers['authorization'];
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
        return null;
    }
    return authHeader.split(' ')[1];
};

// get user login
const getUserLogin = async (req) => {
    const token = getTokenFromRequest(req);
    if (!token) return null;
    return await authService.getUserLogin(token);
};

const formatDate = (timestamp) => {
    if (!timestamp) return '';
    
    const date = new Date(timestamp);
    return date.toLocaleDateString('vi-VN');
};

const isAdmin = (user) => {
    const userObj = user && user.user ? user.user : user;
    return !!(userObj && userObj.role === 'admin');
};

const isUser = (user) => {
    const userObj = user && user.user ? user.user : user;
    return !!(userObj && userObj.role === 'user');
};

const isLeader = (user) => {
    const userObj = user && user.user ? user.user : user;
    return !!(userObj && userObj.role === 'leader');
};

const isAdminOrLeader = (user) => {
    const userObj = user && user.user ? user.user : user;
    return !!(userObj && (userObj.role === 'admin' || userObj.role === 'leader'));
};

const getFileUrl = (relativePath) => {
    if (!relativePath) return '';
    
    const normalizedPath = relativePath.replace(/\\/g, '/');
    
    const baseUrl = process.env.BASE_URL || `http://localhost:${process.env.PORT || 3000}`;
    
    const cleanBaseUrl = baseUrl.replace(/\/$/, '');
    
    const cleanPath = normalizedPath.startsWith('/') ? normalizedPath : `/${normalizedPath}`;
    
    return `${cleanBaseUrl}${cleanPath}`;
};

const getRelativePath = (absolutePath) => {
    if (!absolutePath) return '';
    const uploadsIndex = absolutePath.indexOf('uploads');
    if (uploadsIndex !== -1) {
        return absolutePath.substring(uploadsIndex).replace(/\\/g, '/');
    }
    
    return absolutePath.replace(/\\/g, '/');
};


module.exports = {
    formatDateTime,
    formatDate,
    isAdmin,
    isUser,
    isLeader,
    isAdminOrLeader,
    getUserLogin,
    getTokenFromRequest,
    getFileUrl,
    getRelativePath
}; 