const mongoose = require('mongoose');
const softDeletePlugin = require('../plugins/softDeletePlugin');
const { Schema } = mongoose;

const webManualCaseSchema = new Schema({
  test_case_id: String,
  test_case_name: String,
  priority: String,
  pre_condition: String,
  steps: String,
  test_data: String,
  expected_result: String,
  test_case_type: String
});

const mobileManualCaseSchema = new Schema({
  test_case_id: String,
  test_case_name: String,
  priority: String,
  device: String,
  pre_condition: String,
  steps: String,
  test_data: String,
  expected_result: String,
  test_case_type: String
});

const apiManualCaseSchema = new Schema({
  test_case_id: String,
  test_case_name: String,
  priority: String,
  pre_condition: String,
  http_method: String,
  header: String,
  request_body: String,
  steps: String,
  test_data: String,
  expected_result: String,
  test_case_type: String
});

const TestCaseSchema = new Schema({
  suite_id: { type: Schema.Types.ObjectId, ref: 'suite', required: true },
  feature_id: { type: Schema.Types.ObjectId, ref: 'feature', required: false, default: null },
  category_id: { type: Schema.Types.ObjectId, ref: 'check_item', required: false, default: null },
  check_item_id: { type: Schema.Types.ObjectId, ref: 'check_item', required: false, default: null },
  detail: {
    web_manual: webManualCaseSchema,
    mobile_manual: mobileManualCaseSchema,
    api_manual: apiManualCaseSchema
  },
  status: { type: String, enum: ['draft', 'ready'], default: 'draft' },
  review_status: { type: String, enum: ['pending', 'usable', 'update', 'invalid', 'duplicate'], default: 'usable' },
  duplicate_of: { type: String, default: null },
  invalid_reason: { type: String, default: null },
  order: { type: Number, default: 0 },
  type: { type: String, enum: ['automation', 'manual'], default: 'manual' },

  automation_script: {
    language: { type: String, default: null },
    framework: { type: String, default: null },
    tool: { type: String, default: null },
    dependencies: [{ type: String }],
    fixtures: [{ type: String }],
    notes: { type: String, default: null },
    script: { type: String, default: null }
  },
  comment: { type: String, default: null },
  del_flag: { type: Number, default: 0 }
}, { timestamps: true });

TestCaseSchema.plugin(softDeletePlugin);

TestCaseSchema.index({ suite_id: 1, check_item_id: 1 });
TestCaseSchema.index({ suite_id: 1, category_id: 1 });
TestCaseSchema.index({ suite_id: 1, feature_id: 1 });

module.exports = mongoose.model('test_case', TestCaseSchema);