const Jo<PERSON> = require('joi');

exports.registerSchema = Joi.object({
  name: Joi.string().min(2).max(50).required().trim().messages({
    'string.empty': 'Tên người dùng không được để trống',
    'string.min': 'Tên người dùng phải có ít nhất 2 ký tự',
    'string.max': 'Tên người dùng không được vượt quá 50 ký tự',
  }),
  email: Joi.string().email().required().trim().messages({
    'string.empty': 'Email không được để trống',
    'string.email': 'Email không hợp lệ',
  }),
  password: Joi.string().min(6).max(100).required().trim().messages({
    'string.empty': 'Mật khẩu không được để trống',
    'string.min': '<PERSON><PERSON><PERSON> khẩu phải có ít nhất 6 ký tự',
    'string.max': 'Mật khẩu không được vượt quá 100 ký tự',
  }),
  role: Joi.string().valid('user').optional().messages({
    'any.only': 'Vai trò phải là user'
  })
});

exports.loginSchema = Joi.object({
  email: Joi.string().email().required().trim().messages({
    'string.empty': 'Email không được để trống',
    'string.email': 'Email không hợp lệ',
  }),
  password: Joi.string().min(6).max(100).required().trim().messages({
    'string.empty': 'Mật khẩu không được để trống',
  })
});

exports.createUserSchema = Joi.object({
  name: Joi.string().min(2).max(50).required().trim().messages({
    'string.empty': 'Tên người dùng không được để trống',
    'string.min': 'Tên người dùng phải có ít nhất 2 ký tự',
    'string.max': 'Tên người dùng không được vượt quá 50 ký tự',
  }),
  email: Joi.string().email().required().trim().messages({
    'string.empty': 'Email không được để trống',
    'string.email': 'Email không hợp lệ',
  }),
  password: Joi.string().min(6).max(100).required().trim().messages({
    'string.empty': 'Mật khẩu không được để trống',
    'string.min': 'Mật khẩu phải có ít nhất 6 ký tự',
    'string.max': 'Mật khẩu không được vượt quá 100 ký tự',
  }),
  role: Joi.string().valid('user', 'test_manager', 'admin').default('user').messages({
    'any.only': 'Vai trò phải là user, test_manager hoặc admin'
  }),
  status: Joi.string().valid('active', 'inactive', 'suspended', 'pending').default('active')
});

exports.updateUserSchema = Joi.object({
  name: Joi.string().min(2).max(50).optional().trim().messages({
    'string.min': 'Tên người dùng phải có ít nhất 2 ký tự',
    'string.max': 'Tên người dùng không được vượt quá 50 ký tự',
  }),
  email: Joi.string().email().optional().trim().messages({
    'string.email': 'Email không hợp lệ',
  }),
  password: Joi.string().min(6).max(100).optional().allow('').trim().messages({
    'string.min': 'Mật khẩu phải có ít nhất 6 ký tự',
    'string.max': 'Mật khẩu không được vượt quá 100 ký tự',
  }),
  role: Joi.string().valid('user', 'test_manager', 'admin').optional().messages({
    'any.only': 'Vai trò phải là user, test_manager hoặc admin'
  }),
  status: Joi.string().valid('active', 'inactive', 'suspended', 'pending').optional(),
  avatar: Joi.string().uri().optional().allow(null),
  preferences: Joi.object({
    language: Joi.string().valid('vi', 'en').optional(),
    theme: Joi.string().valid('light', 'dark', 'auto').optional(),
    notifications: Joi.object({
      email: Joi.boolean().optional(),
      push: Joi.boolean().optional()
    }).optional()
  }).optional()
});

exports.updateUserProfileSchema = Joi.object({
  name: Joi.string().min(2).max(50).optional().trim().messages({
    'string.min': 'Tên người dùng phải có ít nhất 2 ký tự',
    'string.max': 'Tên người dùng không được vượt quá 50 ký tự',
  }),
  email: Joi.string().email().optional().trim().messages({
    'string.email': 'Email không hợp lệ',
  }),
  password: Joi.string().min(6).max(100).optional().allow('').trim().messages({
    'string.min': 'Mật khẩu phải có ít nhất 6 ký tự',
    'string.max': 'Mật khẩu không được vượt quá 100 ký tự',
  }),
  avatar: Joi.string().uri().optional().allow(null),
  preferences: Joi.object({
    language: Joi.string().valid('vi', 'en').optional(),
    theme: Joi.string().valid('light', 'dark', 'auto').optional(),
    notifications: Joi.object({
      email: Joi.boolean().optional(),
      push: Joi.boolean().optional()
    }).optional()
  }).optional()
});

exports.changePasswordSchema = Joi.object({
  currentPassword: Joi.string().min(6).max(100).required().trim().messages({
    'string.empty': 'Mật khẩu hiện tại không được để trống',
  }),
  newPassword: Joi.string().min(6).max(100).required().trim().messages({
    'string.empty': 'Mật khẩu mới không được để trống',
    'string.min': 'Mật khẩu mới phải có ít nhất 6 ký tự',
    'string.max': 'Mật khẩu mới không được vượt quá 100 ký tự',
  })
});

exports.updateUserStatusSchema = Joi.object({
  status: Joi.string().valid('active', 'inactive', 'suspended', 'pending').required().messages({
    'string.empty': 'Trạng thái không được để trống',
    'any.only': 'Trạng thái phải là active, inactive, suspended hoặc pending'
  })
});

exports.bulkUpdateUsersSchema = Joi.object({
  userIds: Joi.array().items(Joi.string().required()).min(1).required().messages({
    'array.min': 'Phải chọn ít nhất 1 người dùng',
    'any.required': 'Danh sách ID người dùng là bắt buộc'
  }),
  updates: Joi.object({
    status: Joi.string().valid('active', 'inactive', 'suspended', 'pending').optional(),
    preferences: Joi.object({
      language: Joi.string().valid('vi', 'en').optional(),
      theme: Joi.string().valid('light', 'dark', 'auto').optional(),
      notifications: Joi.object({
        email: Joi.boolean().optional(),
        push: Joi.boolean().optional()
      }).optional()
    }).optional()
  }).required().messages({
    'any.required': 'Dữ liệu cập nhật là bắt buộc'
  })
});
