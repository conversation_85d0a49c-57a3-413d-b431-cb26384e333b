const mammoth = require('mammoth');
const fs = require('fs');
const path = require('path');
const cheerio = require('cheerio');
const logger = require('./loggerService');

class HeadingExtractor {
    constructor() {
        this.headings = [];
        this.content = '';
        this.htmlContent = '';
    }

    async extractHeadings(filePath, options = {}) {
        const {
            includeContent = true,
            maxLevel = 6,
            filename = path.basename(filePath)
        } = options;

        try {
            const htmlResult = await mammoth.convertToHtml({ path: filePath });
            this.htmlContent = htmlResult.value;
            
            const textResult = await mammoth.extractRawText({ path: filePath });
            this.content = textResult.value;

            this.parseHeadings(maxLevel);

            const resultData = {
                success: true,
                filename,
                filePath,
                totalHeadings: this.headings.length,
                headings: this.headings
            };

            return resultData;

        } catch (error) {
            logger.error('Lỗi khi trích xuất headings:', error);
            return {
                success: false,
                error: error.message,
                filename,
                filePath
            };
        }
    }

    async extractMultipleHeadings(filePaths, options = {}) {
        const results = [];
        let totalHeadings = 0;
        let successCount = 0;
        let failCount = 0;

        for (const filePath of filePaths) {
            const result = await this.extractHeadings(filePath, options);
            results.push(result);
            
            if (result.success) {
                totalHeadings += result.totalHeadings;
                successCount++;
            } else {
                failCount++;
            }
        }

        return {
            success: true,
            totalFiles: filePaths.length,
            successCount,
            failCount,
            totalHeadings,
            results
        };
    }

    parseHeadings(maxLevel) {
        this.headings = [];
        
        const $ = cheerio.load(this.htmlContent);
        
        $('h1, h2, h3, h4, h5, h6').each((index, element) => {
            const $element = $(element);
            const tagName = element.tagName.toLowerCase();
            const level = parseInt(tagName.charAt(1));
            const text = $element.text().trim();

            if (level <= maxLevel && text) {
                const content = this.extractContentAfterHeading($, element);
                
                this.headings.push({
                    level,
                    title: text,
                    lineNumber: index + 1,
                    id: this.generateHeadingId(text),
                    content: content
                });
            }
        });

        if (this.headings.length === 0) {
            this.parseHeadingsFromText(maxLevel);
        }
    }

    parseHeadingsFromText(maxLevel) {
        const lines = this.content.split('\n');
        lines.forEach((line, index) => {
            const trimmedLine = line.trim();
            if (!trimmedLine) return;

            // Detect heading patterns
            const headingMatch = this.detectHeading(trimmedLine);
            if (headingMatch) {
                const { level, text } = headingMatch;
                if (level <= maxLevel) {
                    this.headings.push({
                        level,
                        title: text.trim(),
                        lineNumber: index + 1,
                        id: this.generateHeadingId(text),
                        content: this.extractContentAfterHeadingFromText(lines, index)
                    });
                }
            }
        });
    }

    detectHeading(line) {
        const pattern1 = /^(\d+)\.\s+(.+)$/;
        const match1 = line.match(pattern1);
        if (match1) {
            const level = this.getHeadingLevel(match1[1]);
            return { level, text: match1[2] };
        }

        const pattern2 = /^(\d+)\.(\d+)\.\s+(.+)$/;
        const match2 = line.match(pattern2);
        if (match2) {
            const level = this.getHeadingLevel(match2[1], match2[2]);
            return { level, text: match2[3] };
        }

        const pattern3 = /^(\d+)\.(\d+)\.(\d+)\.\s+(.+)$/;
        const match3 = line.match(pattern3);
        if (match3) {
            const level = this.getHeadingLevel(match3[1], match3[2], match3[3]);
            return { level, text: match3[4] };
        }

        if (line === line.toUpperCase() && line.length > 3 && line.length < 100) {
            return { level: 1, text: line };
        }

        return null;
    }

    getHeadingLevel(num1, num2, num3) {
        if (num3) return 3;
        if (num2) return 2;
        return 1;
    }

    generateHeadingId(text) {
        return text
            .toLowerCase()
            .replace(/[^a-z0-9\s-]/g, '')
            .replace(/\s+/g, '-')
            .replace(/-+/g, '-')
            .trim();
    }

    extractContentAfterHeading($, currentElement) {
        const content = [];
        let nextElement = $(currentElement).next();
        
        while (nextElement.length > 0) {
            const tagName = nextElement.prop('tagName');
            
            if (tagName && tagName.toLowerCase().match(/^h[1-6]$/)) {
                break;
            }
            
            const text = nextElement.text().trim();
            if (text) {
                content.push(text);
            }
            
            nextElement = nextElement.next();
        }
        
        return content.join('\n').trim();
    }

    extractContentAfterHeadingFromText(lines, headingIndex) {
        const content = [];
        let i = headingIndex + 1;
        
        while (i < lines.length) {
            const line = lines[i].trim();
            if (!line) {
                i++;
                continue;
            }
            
            if (this.detectHeading(line)) {
                break;
            }
            
            content.push(line);
            i++;
        }
        
        return content.join('\n').trim();
    }

    // generateTOC() {
    //     let toc = '# Mục lục\n\n';
        
    //     this.headings.forEach(heading => {
    //         const indent = '  '.repeat(heading.level - 1);
    //         const link = `[${heading.text}](#${heading.id})`;
    //         toc += `${indent}- ${link}\n`;
    //     });
        
    //     return toc;
    // }
}

module.exports = HeadingExtractor; 