const express = require('express');
const router = express.Router();
const testCaseController = require('../controllers/testCaseController');
const authMiddleware = require('../middleware/auth');
const validate = require('../middleware/validate');
const { createTestCaseSchema } = require('../validation/testCaseSchemas');

// Create test script
router.post('/gen-test-case', authMiddleware, testCaseController.genenrateTestCase);
router.get('/list-test-case', authMiddleware, testCaseController.getListTestCases);
router.post('/create-test-case', authMiddleware, validate(createTestCaseSchema), testCaseController.createTestCase);
router.put('/:id', authMiddleware, testCaseController.updateTestCase);
router.delete('/:id', authMiddleware, testCaseController.deleteTestCase);
router.post('/regenerate-one', authMiddleware, testCaseController.regenerateOneTestCase);
router.post('/regenerate-all', authMiddleware, testCaseController.regenerateAllTestCases);
router.post('/review-test-case/:id', authMiddleware, testCaseController.reviewTestCase);

module.exports = router;