const express = require('express');
const cors = require('cors');
const path = require('path');
const userRoutes = require('./routes/userRoutes');
const templateRoutes = require('./routes/templateRoutes');
const testScriptRoutes = require('./routes/testScriptRoutes');
const projectRoutes = require('./routes/projectRoutes');
const testCaseRoutes = require('./routes/testCaseRoutes');
const suiteRoutes = require('./routes/suiteRoutes');
const testCaseVersionRoutes = require('./routes/testCaseVersionRoutes');
const mindmapRoutes = require('./routes/mindmapRoutes');
const fileRoutes = require('./routes/fileRoutes');

const app = express();


app.set('view engine', 'ejs');
app.set('views', path.join(__dirname, 'templates'));

// CORS configuration with fallback options
const corsOptions = {
  origin: function (origin, callback) {
    // Allow requests with no origin (like mobile apps or curl requests)
    if (!origin) return callback(null, true);
    
    const allowedOrigins = [
      'http://localhost:5173', // Vite default dev server
      'http://localhost:3000', // Alternative dev port
      'http://localhost:8080', // Alternative dev port
      'http://localhost',      // Frontend container (port 80)
      'http://localhost:80',   // Frontend container explicit
      'http://127.0.0.1:5173',
      'http://127.0.0.1:3000',
      'http://127.0.0.1:8080',
      'http://127.0.0.1',      // Frontend container
      'http://127.0.0.1:80',   // Frontend container explicit
      process.env.VITE_FRONTEND_URL,
      process.env.VITE_BACKEND_URL,
      process.env.CORS_ORIGIN
    ].filter(Boolean);
    
    // If no specific origins are configured, allow all in development
    if (allowedOrigins.length === 0 && process.env.NODE_ENV === 'development') {
      return callback(null, true);
    }
    
    if (allowedOrigins.includes(origin)) {
      callback(null, true);
    } else {
      console.warn(`CORS blocked request from origin: ${origin}`);
      callback(new Error('Not allowed by CORS'));
    }
  },
  methods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
  allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With'],
  credentials: true, // Allow cookies and authentication headers
  optionsSuccessStatus: 200 // Some legacy browsers choke on 204
};

app.use(cors(corsOptions));

// Static file serving disabled - files served via protected /files endpoints
app.use(express.json());

// Health check endpoint (for Docker, monitoring, etc.)
app.get('/health', (req, res) => {
  res.status(200).json({ 
    status: 'healthy',
    service: 'gentest-backend',
    timestamp: new Date().toISOString(),
    uptime: process.uptime()
  });
});

app.use('/users', userRoutes);
app.use('/templates', templateRoutes);
app.use('/projects', projectRoutes);
app.use('/test-script', testScriptRoutes);
app.use('/test-case', testCaseRoutes);
app.use('/suites', suiteRoutes);
app.use('/test-case-versions', testCaseVersionRoutes);
app.use('/mindmaps', mindmapRoutes);
app.use('/files', fileRoutes);

module.exports = app;
