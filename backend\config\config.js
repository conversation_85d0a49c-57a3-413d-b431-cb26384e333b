// Centralized backend config loader
require('dotenv').config();

// JWT Secret validation
const getJWTSecret = () => {
  const secret = process.env.JWT_SECRET;
  
  // In production, ensure JWT_SECRET is set and strong
  if (process.env.NODE_ENV === 'production') {
    if (!secret) {
      throw new Error('JWT_SECRET must be set in production environment');
    }
    if (secret.length < 32) {
      throw new Error('JWT_SECRET must be at least 32 characters long in production');
    }
  }
  
  // Generate a strong default secret for development (not recommended for production)
  const defaultSecret = 'dev-secret-' + Math.random().toString(36).substring(2, 15) + 
                       Math.random().toString(36).substring(2, 15) + 
                       Math.random().toString(36).substring(2, 15);
  
  return secret || defaultSecret;
};

module.exports = {
  // JWT configuration - improved security
  jwtSecret: getJWTSecret(),
  jwtExpiresIn: process.env.JWT_EXPIRES_IN || '24h', // Extended to 24 hours
  jwtRefreshExpiresIn: process.env.JWT_REFRESH_EXPIRES_IN || '7d', // For refresh tokens
  
  // Server configuration
  port: process.env.PORT || 3000,
  
  // Environment
  nodeEnv: process.env.NODE_ENV || 'development',
  
  // Security settings
  security: {
    // Enable secure cookies in production
    secureCookies: process.env.NODE_ENV === 'production',
    // CORS settings
    corsOrigin: process.env.CORS_ORIGIN || 'http://localhost:5173',
    // Token blacklist (for logout functionality)
    enableTokenBlacklist: process.env.ENABLE_TOKEN_BLACKLIST === 'true',
  },

  // File upload config
  uploadS3: process.env.UPLOAD_S3 === 'true',

  prompt_language: {
    vi: 'Tiếng Việt',
    en: 'Tiếng Anh',
  },
  regenerate_type: {
    one: 1,
    all: 2,
  },
  test_case_history_type: {
    create: 1,
    update: 2,
    delete: 3,
  },

  mindmap: {
    categories: [
      "Kiểm tra truy cập màn hình/phân quyền",
      "Validate",
      "Kiểm tra UI/UX",
      "Function",
      "Security",
      "Common",
    ]
  },
  test_case: {
    review_status: {
      pending: "pending",
      usable: 'usable',
      update: 'update',
      invalid: 'invalid',
      duplicate: 'duplicate'
    }
  },
  suite_approval_status: {
    draft: 'draft',
    pending: 'pending',
    approved: 'approved',
    rejected: 'rejected'
  },

  suite: {
    field_type: ['web', 'mobile', 'api', 'performance', 'security'],
    objective: ['test_case_manual', 'script_automation'],
    approval_status: ['draft', 'pending', 'approved', 'rejected']
  },

  coverage: {
   "positive_case": "Positive",
   "negative_case": "Negative",
   "boundary_value_analysis": "Boundary Value Analysis",
   "equivalence_partitioning": "Equivalence Partitioning",
   "ui_ux_testing": "UI/UX",
   "compatibility_testing": "Compatibility",
   "accessibility_testing": "Accessibility",
   "edge_corner_case": "Edge",
   "gesture_testing": "Gesture Testing",
   "interrupt_testing": "Interrupt Testing",
   "network_condition_testing": "Network Condition Testing",
   "permission_testing": "Permission Testing",
   "hardware_interaction_testing": "Hardware Interaction Testing",
   "installation_update_uninstall_testing": "Installation/Update/Unistall Testing",
   "input_validation": "Input Validation",
   "authentication": "Authentication",
   "authorization": "Authorization",
   "business_logic_errors": "Business Logic Errors",
   "status_code_errors": "Status Code Errors",
   "schema_validation": "Schema Validation",
   "header_testing": "Header Testing"

  },

  user: {
    role: ['admin', 'test_manager', 'user'],
    project_role: ['leader', 'tester', 'viewer'],
    project_role_priority: ['leader', 'tester', 'viewer'],
    project_full_access_roles: ['admin', 'test_manager'],
    project_change_role_allowed: ['leader', 'tester', 'viewer'],
    project_join_request_allowed: ['tester', 'viewer']
  },

  project: {
    display_mode: ['private', 'internal']
  },
  project_role: {
    leader: 'leader',
    tester: 'tester',
    viewer: 'viewer'
  },

  global_role: {
    admin: 'admin',
    test_manager: 'test_manager',
    user: 'user'
  },

  project_join_request_status: {
    pending: 'pending',
    approved: 'approved',
    rejected: 'rejected'
  },

  project_join_request_status_action: ['approved', 'rejected'],

  api_ai: {
    model_name: 'gemini-3-pro-preview',
    prompt_version: '0.0'
  },
  
  // Python API configuration
  pythonApi: {
    baseURL: process.env.PYTHON_API_URL || 'http://localhost:8000',
    timeout: (parseInt(process.env.PYTHON_API_TIMEOUT) || 5) * 60 * 1000, // 5 minutes
    maxRetries: parseInt(process.env.PYTHON_API_MAX_RETRIES) || 3,
    retryDelay: parseInt(process.env.PYTHON_API_RETRY_DELAY) || 1000, // 1 second
  }
};
