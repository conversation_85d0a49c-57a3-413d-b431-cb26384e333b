{"features": [{"_id": "68d25cb738ec9064b3b87b3a", "name": "<PERSON><PERSON><PERSON> phê du<PERSON>t cuộc tranh chấp", "description": "<PERSON> phép người dùng gửi cuộc tranh chấp lên người có thẩm quyền phê duyệt, chọn phòng ban và người duyệt phù hợp theo quy định tổ chức.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb149", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb14b", "name": "Chỉ người có quyền mới gửi phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người dùng có quyền mới có thể gửi phê duyệt tranh chấp.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb14d", "name": "Ẩn nút gửi với người không đủ quyền", "rationale": "<PERSON><PERSON><PERSON> tra giao diện không hiển thị chức năng gửi phê duyệt cho người không đủ quyền.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb14f", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb151", "name": "<PERSON><PERSON> thông tin tranh chấp tr<PERSON><PERSON><PERSON> khi gửi", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> các trườ<PERSON> bắ<PERSON> buộc của tranh chấp đã được điền đầy đủ trước khi gửi phê duyệt.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb153", "name": "<PERSON><PERSON>n phòng ban và người duyệt hợp lệ", "rationale": "<PERSON><PERSON><PERSON> tra người dùng phải chọn đúng phòng ban và người duyệt theo quy định trước khi gửi.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb155", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb157", "name": "<PERSON><PERSON><PERSON> thị đúng danh sách phòng ban/ng<PERSON><PERSON><PERSON> du<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o danh sách phòng ban và người duyệt hiển thị đúng theo dữ liệu hệ thống.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb159", "name": "Thông báo gửi phê duyệt thành công/thất bại", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống hiển thị thông báo phù hợp sau khi gửi phê duyệt.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb15b", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb15d", "name": "<PERSON><PERSON><PERSON> gửi phê duyệt thành công", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> khi gửi phê duyệt hợp lệ, hệ thống chuyển trạng thái tranh chấp đúng.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb15f", "name": "<PERSON>ồng gửi phê duyệt bị từ chối do thiếu thông tin", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống xử lý đúng khi gửi phê duyệt với thông tin không hợp lệ.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb161", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb163", "name": "<PERSON>ử lý lỗi kết nối khi gửi phê duyệt", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống thông báo lỗi khi gặp sự cố kết nối trong quá trình gửi.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb165", "name": "<PERSON><PERSON> lý gửi phê duyệt trùng lặp", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không cho phép gửi phê duyệt nhiều lần cho cùng một tranh chấp.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb167", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb169", "name": "<PERSON><PERSON><PERSON> nhật trạng thái tranh chấp sau g<PERSON>i", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp đ<PERSON><PERSON><PERSON> cập nhật chính xác sau khi gửi phê duyệt.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb16b", "name": "<PERSON><PERSON> <PERSON>h<PERSON>n lịch sử gửi phê du<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống lưu lại lịch sử gửi phê duyệt cho tranh chấp.", "test_cases": []}]}]}, {"_id": "68d25cb738ec9064b3b87b3c", "name": "<PERSON><PERSON> cuộc tranh chấp", "description": "<PERSON><PERSON><PERSON><PERSON> duyệt có thể phê duyệt cuộc tranh chấp, <PERSON><PERSON><PERSON> <PERSON>hận qua popup và cập nhật trạng thái tranh chấp.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb16e", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb170", "name": "Chỉ người duyệt đ<PERSON><PERSON><PERSON> phép phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người có quyền duyệt mới thực hiện được thao tác phê duyệt.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb172", "name": "Ẩn nút phê duyệt với người không đủ quyền", "rationale": "<PERSON><PERSON><PERSON> tra giao diện không hiển thị chức năng phê duyệt cho người không đủ quyền.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb174", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb176", "name": "<PERSON><PERSON><PERSON> chấp ở trạng thái chờ phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ tranh chấp ở trạng thái chờ phê duyệt mới được phép phê duyệt.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb178", "name": "<PERSON><PERSON><PERSON><PERSON> du<PERSON> thu<PERSON><PERSON> phòng ban phù hợp", "rationale": "<PERSON><PERSON><PERSON> tra người duyệt phải thuộc phòng ban đư<PERSON><PERSON> phân công xử lý tranh chấp.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb17a", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb17c", "name": "<PERSON><PERSON><PERSON> thị popup x<PERSON>c nhận phê du<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống hiển thị popup xác nhận khi người dùng thực hiện phê du<PERSON>.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb17e", "name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON><PERSON> kết quả phê duyệt", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống hiển thị thông báo thành công hoặc thất bại sau khi phê duyệt.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb180", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb182", "name": "<PERSON><PERSON><PERSON> phê duyệt thành công", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp đ<PERSON><PERSON><PERSON> cập nhật đúng khi phê duyệt thành công.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb184", "name": "<PERSON><PERSON><PERSON> ph<PERSON> du<PERSON>t bị hủy qua popup", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không thay đổi trạng thái khi người dùng hủy thao tác phê duyệt.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb186", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb188", "name": "<PERSON>ử lý lỗi khi phê duyệt thất bại", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống thông báo lỗi khi phê duyệt không thành công do sự cố hệ thống.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb18a", "name": "<PERSON><PERSON> lý phê duyệt trùng lặp", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không cho phép phê duyệt nhiều lần cho cùng một tranh chấp.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb18c", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb18e", "name": "<PERSON><PERSON><PERSON> nhật trạng thái tranh chấp sau phê duy<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp chuy<PERSON> sang đã phê duyệt sau khi thao tác thành công.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb190", "name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> lịch sử phê du<PERSON>t", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống lưu lại lịch sử phê duyệt cho tranh chấp.", "test_cases": []}]}]}, {"_id": "68d25cb738ec9064b3b87b3e", "name": "Từ chối cuộc tranh chấp", "description": "<PERSON><PERSON><PERSON><PERSON> duy<PERSON>t có thể từ chối cuộc tranh chấp, <PERSON><PERSON><PERSON> <PERSON>h<PERSON>n qua popup và cập nhật trạng thái tranh chấp.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb193", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb195", "name": "Chỉ người duyệt đ<PERSON><PERSON><PERSON> phép từ chối", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người có quyền duyệt mới thực hiện đư<PERSON>c thao tác từ chối.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb197", "name": "Ẩn nút từ chối với người không đủ quyền", "rationale": "<PERSON><PERSON><PERSON> tra giao diện không hiển thị chức năng từ chối cho người không đủ quyền.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb199", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb19b", "name": "<PERSON><PERSON><PERSON> chấp ở trạng thái chờ phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ tranh chấp ở trạng thái chờ phê duyệt mới được phép từ chối.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb19d", "name": "<PERSON><PERSON><PERSON><PERSON> du<PERSON> thu<PERSON><PERSON> phòng ban phù hợp", "rationale": "<PERSON><PERSON><PERSON> tra người duyệt phải thuộc phòng ban đư<PERSON><PERSON> phân công xử lý tranh chấp.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb19f", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb1a1", "name": "<PERSON><PERSON>n thị popup x<PERSON>c nhận từ chối", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống hiển thị popup xác nhận khi người dùng thực hiện từ chối.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb1a3", "name": "<PERSON><PERSON><PERSON><PERSON> b<PERSON>o kết quả từ chối", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống hiển thị thông báo thành công hoặc thất bại sau khi từ chối.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1a5", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb1a7", "name": "<PERSON><PERSON><PERSON> từ chối thành công", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp đ<PERSON><PERSON><PERSON> cập nhật đúng khi từ chối thành công.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb1a9", "name": "<PERSON><PERSON><PERSON> từ chối bị hủy qua popup", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không thay đổi trạng thái khi người dùng hủy thao tác từ chối.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1ab", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb1ad", "name": "<PERSON>ử lý lỗi khi từ chối thất bại", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống thông báo lỗi khi từ chối không thành công do sự cố hệ thống.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb1af", "name": "<PERSON><PERSON> lý từ chối trùng lặp", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống không cho phép từ chối nhiều lần cho cùng một tranh chấp.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1b1", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb1b3", "name": "<PERSON><PERSON><PERSON> nhật trạng thái tranh chấp sau từ chối", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o trạng thái tranh chấp chuy<PERSON> sang đã từ chối sau khi thao tác thành công.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb1b5", "name": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON> lịch sử từ chối", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống lưu lại lịch sử từ chối cho tranh chấp.", "test_cases": []}]}]}, {"_id": "68d25cb738ec9064b3b87b40", "name": "<PERSON><PERSON> danh s<PERSON>ch ng<PERSON><PERSON> p<PERSON>ê <PERSON>", "description": "<PERSON><PERSON>n thị danh sách người có quyền phê duyệt dựa trên chi nhánh hoặc phòng ban phụ trách tranh chấp.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb1b8", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb1ba", "name": "Chỉ người có quyền xem danh sách", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người dùng có quyền mới xem được danh sách người phê duyệt.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb1bc", "name": "Ẩn danh sách với người không đủ quyền", "rationale": "<PERSON><PERSON><PERSON> tra giao diện không hiển thị danh sách người phê duyệt cho người không đủ quyền.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1be", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb1c0", "name": "<PERSON><PERSON> tranh chấp thuộc chi nh<PERSON>h/phòng ban", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o chỉ hiển thị danh sách người phê duyệt khi có tranh chấp thuộc chi nhánh hoặc phòng ban.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1c2", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb1c4", "name": "<PERSON><PERSON><PERSON> thị đúng danh sách người phê duyệt", "rationale": "<PERSON><PERSON><PERSON> b<PERSON>o danh sách người phê duyệt hiển thị đúng theo dữ liệu hệ thống.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb1c6", "name": "<PERSON><PERSON><PERSON> thị thông tin chi tiết người phê duyệt", "rationale": "<PERSON><PERSON><PERSON> tra các thông tin như tên, ph<PERSON><PERSON> ban, chứ<PERSON> vụ của người phê duyệt đư<PERSON><PERSON> hiển thị đầy đủ.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1c8", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb1ca", "name": "<PERSON><PERSON>ng chọn chi nh<PERSON>h/phòng ban thay đổi danh sách", "rationale": "<PERSON><PERSON><PERSON> bảo khi thay đổi chi nhánh hoặc phòng ban, danh sách người phê duyệt đư<PERSON><PERSON> cập nhật đúng.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1cc", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb1ce", "name": "<PERSON>ử lý lỗi khi không có người phê duyệt", "rationale": "<PERSON><PERSON><PERSON> bả<PERSON> hệ thống thông báo phù hợp khi không có người phê duyệt nào đư<PERSON><PERSON> tìm thấy.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1d0", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb1d2", "name": "<PERSON><PERSON><PERSON> nh<PERSON>t danh sách khi thay đổi dữ liệu người phê duyệt", "rationale": "<PERSON><PERSON><PERSON> tra danh sách người phê duyệt đư<PERSON><PERSON> cập nhật khi có thay đổi về quyền hoặc thông tin người dùng.", "test_cases": []}]}]}, {"_id": "68d25cb738ec9064b3b87b42", "name": "Gửi email thông báo phê du<PERSON>t", "description": "<PERSON><PERSON> thống tự động gửi email thông báo đến người duyệt khi có yêu cầu phê duyệt hoặc khi trạng thái tranh chấp thay đổi.", "source_files": ["1758616752366-554187773.docx"], "categories": [{"_id": "68d25d74b5e186666c9cb1d5", "name": "<PERSON><PERSON><PERSON> tra phân quyền", "checklists": [{"_id": "68d25d74b5e186666c9cb1d7", "name": "Chỉ gửi email cho người có quyền nhận thông báo", "rationale": "<PERSON><PERSON><PERSON> bảo chỉ người duyệt hoặc người liên quan mới nhận đư<PERSON><PERSON> email thông báo.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1d9", "name": "<PERSON><PERSON><PERSON> tra tiền điều kiện", "checklists": [{"_id": "68d25d74b5e186666c9cb1db", "name": "<PERSON><PERSON> đ<PERSON> chỉ email hợp lệ", "rationale": "<PERSON><PERSON><PERSON> bảo người nhận có địa chỉ email hợp lệ trước khi gửi thông báo.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1dd", "name": "<PERSON><PERSON><PERSON> tra màn hình", "checklists": [{"_id": "68d25d74b5e186666c9cb1df", "name": "<PERSON><PERSON>n thị thông b<PERSON>o gửi email thành công/thất bại", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống hiển thị thông báo phù hợp khi gửi email thành công hoặc thất bại.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1e1", "name": "<PERSON><PERSON><PERSON> tra luồng xử lý", "checklists": [{"_id": "68d25d74b5e186666c9cb1e3", "name": "G<PERSON>i email khi có yêu cầu phê duyệt mới", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống gửi email khi có yêu cầu phê duyệt mới đư<PERSON><PERSON> tạo.", "test_cases": []}, {"_id": "68d25d74b5e186666c9cb1e5", "name": "Gửi email khi trạng thái tranh chấp thay đổi", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống gửi email khi trạng thái tranh chấp đ<PERSON><PERSON><PERSON> cập nhật.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1e7", "name": "<PERSON><PERSON><PERSON> tra ngo<PERSON>i lệ", "checklists": [{"_id": "68d25d74b5e186666c9cb1e9", "name": "<PERSON>ử lý lỗi gửi email thất bại", "rationale": "<PERSON><PERSON><PERSON> b<PERSON><PERSON> hệ thống thông báo lỗi khi gửi email không thành công.", "test_cases": []}]}, {"_id": "68d25d74b5e186666c9cb1eb", "name": "<PERSON><PERSON><PERSON> tra <PERSON>nh hưởng", "checklists": [{"_id": "68d25d74b5e186666c9cb1ed", "name": "<PERSON><PERSON> <PERSON><PERSON><PERSON>n lịch sử gửi email", "rationale": "<PERSON><PERSON><PERSON> tra hệ thống lưu lại lịch sử gửi email thông báo cho từng tranh chấp.", "test_cases": []}]}]}]}