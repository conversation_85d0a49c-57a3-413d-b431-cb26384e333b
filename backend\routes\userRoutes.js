const express = require('express');
const router = express.Router();
const userController = require('../controllers/userController');
const authMiddleware = require('../middleware/auth');
const validate = require('../middleware/validate');
const { 
  requirePermission, 
  requireAdmin,
  requireOwnDataOrAdmin
} = require('../middleware/permission');
const { 
  registerSchema, 
  loginSchema, 
  createUserSchema, 
  updateUserSchema, 
  updateUserProfileSchema, 
  changePasswordSchema, 
  updateUserStatusSchema, 
  bulkUpdateUsersSchema 
} = require('../validation/userSchemas');

// Health check
router.get('/health', userController.healthCheck);

// Public endpoints
router.post('/register', validate(registerSchema), userController.register);
router.post('/login', validate(loginSchema), userController.login);
router.post('/refresh-token', userController.refreshToken);

// Protected endpoints
router.post('/logout', authMiddleware, userController.logout);

// User management (admin only)
router.get('/', authMiddleware, requireAdmin, userController.getUsers);
router.get('/filter-user', authMiddleware, userController.filterUser);
router.get('/search', authMiddleware, requireAdmin, userController.searchUserByName);
router.get('/stats', authMiddleware, requireAdmin, userController.getUserStats);
router.get('/get-admin-users', authMiddleware, userController.adminUsers);
router.get('/get-leader-users', authMiddleware, userController.leaderUsers);
router.get('/get-approvers', authMiddleware, userController.getUserApprovers);
router.post('/bulk-update', authMiddleware, requireAdmin, validate(bulkUpdateUsersSchema), userController.bulkUpdateUsers);
router.get('/users-by-project/:projectId', authMiddleware, userController.getUsersByProject);

// Individual user operations
router.get('/:id', authMiddleware, requireOwnDataOrAdmin, userController.getUserById);
router.get('/:id/profile', authMiddleware, requireOwnDataOrAdmin, userController.getUserProfile);
router.post('/', authMiddleware, requireAdmin, validate(createUserSchema), userController.createUser);
router.put('/:id', authMiddleware, requireOwnDataOrAdmin, validate(updateUserSchema), userController.updateUser);
router.put('/:id/profile', authMiddleware, requireOwnDataOrAdmin, validate(updateUserProfileSchema), userController.updateUserProfile);
router.put('/:id/status', authMiddleware, requireAdmin, validate(updateUserStatusSchema), userController.updateUserStatus);
router.put('/:id/password', authMiddleware, requireOwnDataOrAdmin, validate(changePasswordSchema), userController.changePassword);
router.delete('/:id', authMiddleware, requireAdmin, userController.deleteUser);

module.exports = router;
